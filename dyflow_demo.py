#!/usr/bin/env python3
"""
DyFlow Demo - 演示版本
展示Rich UI、钱包管理、专注交易对策略的核心功能
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import box
from rich.align import Align

console = Console()

class DyFlowDemo:
    """DyFlow演示系统"""
    
    def __init__(self):
        self.console = console
        self.session_id = f"dyflow_demo_{int(datetime.now().timestamp())}"
        
        # 模拟数据
        self.demo_wallets = {
            "账户A": {
                "bsc": {
                    "address": "******************************************",
                    "total_assets": 25000.50,
                    "lp_value": 15000.00,
                    "positions": [
                        {"pair": "BNB/USDC", "value": 8000, "pnl": 320, "risk": "MEDIUM"},
                        {"pair": "BNB/USDT", "value": 7000, "pnl": -150, "risk": "LOW"}
                    ],
                    "risk_level": "MEDIUM",
                    "delta_risk": 1200.00,
                    "max_loss": 3750.00
                },
                "solana": {
                    "address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
                    "total_assets": 18000.75,
                    "lp_value": 12000.00,
                    "positions": [
                        {"pair": "SOL/USDC", "value": 12000, "pnl": 480, "risk": "LOW"}
                    ],
                    "risk_level": "LOW",
                    "delta_risk": 600.00,
                    "max_loss": 1800.00
                }
            }
        }
        
        self.focused_pairs = {
            "bsc": ["BNB/USDC", "BNB/USDT"],
            "solana": ["SOL/USDC"]
        }
        
        self.exit_tokens = {
            "bsc": "BNB",
            "solana": "SOL"
        }
        
        console.print("[bold green]🚀 DyFlow Demo 初始化完成")
    
    def show_header(self):
        """显示头部"""
        header_text = Text()
        header_text.append("DyFlow Demo", style="bold blue")
        header_text.append(" - 专业LP策略系统演示", style="bold white")
        
        header_panel = Panel(
            Align.center(header_text),
            box=box.DOUBLE,
            style="blue",
            subtitle="Rich UI | 钱包管理 | 专注交易对策略"
        )
        
        self.console.print(header_panel)
        self.console.print()
    
    def show_portfolio_overview(self):
        """显示投资组合概览"""
        total_value = 0
        total_lp = 0
        total_pnl = 0
        
        for account_data in self.demo_wallets.values():
            for chain_data in account_data.values():
                total_value += chain_data["total_assets"]
                total_lp += chain_data["lp_value"]
                for pos in chain_data["positions"]:
                    total_pnl += pos["pnl"]
        
        overview_table = Table(title="💼 投资组合概览", box=box.ROUNDED)
        overview_table.add_column("指标", style="cyan")
        overview_table.add_column("数值", style="green", justify="right")
        overview_table.add_column("说明", style="yellow")
        
        overview_table.add_row("总资产价值", f"${total_value:,.2f}", "所有钱包总价值")
        overview_table.add_row("LP持仓价值", f"${total_lp:,.2f}", "流动性池持仓价值")
        overview_table.add_row("总盈亏", f"${total_pnl:,.2f}", "未实现盈亏")
        overview_table.add_row("活跃钱包", "2", "BSC + Solana钱包")
        overview_table.add_row("持仓数量", "3", "专注交易对持仓")
        
        return overview_table
    
    def show_wallet_details(self):
        """显示钱包详情"""
        wallet_table = Table(title="💼 钱包资产详情", box=box.ROUNDED)
        wallet_table.add_column("账户", style="cyan")
        wallet_table.add_column("链", style="magenta")
        wallet_table.add_column("地址", style="yellow", max_width=20)
        wallet_table.add_column("总资产", style="green", justify="right")
        wallet_table.add_column("LP价值", style="blue", justify="right")
        wallet_table.add_column("Delta风险", style="bright_yellow", justify="right")
        wallet_table.add_column("最大损失", style="red", justify="right")
        wallet_table.add_column("风险等级", style="red", justify="center")
        
        for account_name, account_data in self.demo_wallets.items():
            for chain, chain_data in account_data.items():
                risk_color = {
                    'LOW': 'green',
                    'MEDIUM': 'yellow',
                    'HIGH': 'red',
                    'CRITICAL': 'bright_red'
                }.get(chain_data["risk_level"], 'white')
                
                wallet_table.add_row(
                    account_name,
                    chain.upper(),
                    f"{chain_data['address'][:8]}...{chain_data['address'][-6:]}",
                    f"${chain_data['total_assets']:,.2f}",
                    f"${chain_data['lp_value']:,.2f}",
                    f"${chain_data['delta_risk']:,.2f}",
                    f"${chain_data['max_loss']:,.2f}",
                    Text(chain_data["risk_level"], style=risk_color)
                )
        
        return wallet_table
    
    def show_focused_pairs_status(self):
        """显示专注交易对状态"""
        pairs_table = Table(title="🎯 专注交易对策略", box=box.ROUNDED)
        pairs_table.add_column("链", style="cyan")
        pairs_table.add_column("交易对", style="yellow")
        pairs_table.add_column("持仓价值", style="green", justify="right")
        pairs_table.add_column("盈亏", style="blue", justify="right")
        pairs_table.add_column("风险等级", style="magenta", justify="center")
        pairs_table.add_column("退出代币", style="bright_yellow", justify="center")
        
        for account_data in self.demo_wallets.values():
            for chain, chain_data in account_data.items():
                exit_token = self.exit_tokens[chain]
                
                for position in chain_data["positions"]:
                    pnl = position["pnl"]
                    pnl_color = "green" if pnl >= 0 else "red"
                    pnl_sign = "+" if pnl >= 0 else ""
                    
                    risk_color = {
                        'LOW': 'green',
                        'MEDIUM': 'yellow',
                        'HIGH': 'red'
                    }.get(position["risk"], 'white')
                    
                    pairs_table.add_row(
                        chain.upper(),
                        position["pair"],
                        f"${position['value']:,.2f}",
                        Text(f"{pnl_sign}${pnl:,.2f}", style=pnl_color),
                        Text(position["risk"], style=risk_color),
                        exit_token
                    )
        
        return pairs_table
    
    def show_strategy_config(self):
        """显示策略配置"""
        config_table = Table(title="⚙️ 策略配置", box=box.ROUNDED)
        config_table.add_column("配置项", style="cyan")
        config_table.add_column("BSC设置", style="yellow")
        config_table.add_column("Solana设置", style="magenta")
        
        config_table.add_row("专注交易对", "BNB/USDC, BNB/USDT", "SOL/USDC")
        config_table.add_row("最小TVL", "$100,000", "$100,000")
        config_table.add_row("最小24h交易量", "$50,000", "$50,000")
        config_table.add_row("止损阈值", "15%", "15%")
        config_table.add_row("紧急退出", "10%", "10%")
        config_table.add_row("退出代币", "BNB", "SOL")
        config_table.add_row("最大持仓", "20%", "20%")
        
        return config_table
    
    def show_risk_analysis(self):
        """显示风险分析"""
        risk_table = Table(title="🛡️ 风险分析仪表板", box=box.ROUNDED)
        risk_table.add_column("风险指标", style="cyan")
        risk_table.add_column("当前值", style="yellow", justify="right")
        risk_table.add_column("阈值", style="red", justify="right")
        risk_table.add_column("状态", style="green", justify="center")
        
        # 计算总风险
        total_delta = sum(
            chain_data["delta_risk"] 
            for account_data in self.demo_wallets.values()
            for chain_data in account_data.values()
        )
        
        total_max_loss = sum(
            chain_data["max_loss"]
            for account_data in self.demo_wallets.values() 
            for chain_data in account_data.values()
        )
        
        risk_metrics = [
            ("总Delta风险", total_delta, 10000, "NORMAL"),
            ("最大可能损失", total_max_loss, 20000, "NORMAL"),
            ("无常损失风险", 3.2, 8.0, "NORMAL"),
            ("流动性风险", 8.5, 15.0, "NORMAL"),
            ("集中度风险", 35.0, 50.0, "NORMAL"),
            ("价格波动率", 18.7, 25.0, "NORMAL")
        ]
        
        for metric, current, threshold, status in risk_metrics:
            status_color = {
                'NORMAL': 'green',
                'WARNING': 'yellow',
                'HIGH': 'red',
                'CRITICAL': 'bright_red'
            }.get(status, 'white')
            
            if metric in ["总Delta风险", "最大可能损失"]:
                current_str = f"${current:,.2f}"
                threshold_str = f"${threshold:,.2f}"
            else:
                current_str = f"{current:.1f}%"
                threshold_str = f"{threshold:.1f}%"
            
            risk_table.add_row(
                metric,
                current_str,
                threshold_str,
                Text(status, style=status_color)
            )
        
        return risk_table
    
    async def simulate_monitoring(self):
        """模拟监控过程"""
        self.console.print("\n[bold green]🔍 启动专注交易对监控...")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            # 模拟监控步骤
            steps = [
                ("连接BSC网络", 2),
                ("扫描BNB/USDC池子", 3),
                ("扫描BNB/USDT池子", 2),
                ("连接Solana网络", 2),
                ("扫描SOL/USDC池子", 3),
                ("AI分析池子质量", 4),
                ("计算风险指标", 2),
                ("生成投资建议", 1)
            ]
            
            for step_name, duration in steps:
                task = progress.add_task(f"[cyan]{step_name}...", total=None)
                await asyncio.sleep(duration)
                progress.update(task, description=f"[green]✅ {step_name}完成")
        
        # 显示模拟结果
        results_table = Table(title="📊 监控结果", box=box.ROUNDED)
        results_table.add_column("链", style="cyan")
        results_table.add_column("交易对", style="yellow")
        results_table.add_column("TVL", style="green", justify="right")
        results_table.add_column("24h交易量", style="blue", justify="right")
        results_table.add_column("AI评分", style="magenta", justify="right")
        results_table.add_column("建议", style="red", justify="center")
        
        mock_results = [
            ("BSC", "BNB/USDC", "$2,500,000", "$850,000", "8.5/10", "BUY"),
            ("BSC", "BNB/USDT", "$1,800,000", "$620,000", "7.8/10", "HOLD"),
            ("Solana", "SOL/USDC", "$3,200,000", "$1,200,000", "9.1/10", "BUY")
        ]
        
        for chain, pair, tvl, volume, score, recommendation in mock_results:
            rec_color = {
                'BUY': 'green',
                'HOLD': 'yellow',
                'AVOID': 'red'
            }.get(recommendation, 'white')
            
            results_table.add_row(
                chain,
                pair,
                tvl,
                volume,
                score,
                Text(recommendation, style=rec_color)
            )
        
        self.console.print(results_table)
        
        # 显示AI建议
        ai_panel = Panel(
            """🤖 AI分析建议:

1. BNB/USDC池子表现优异，建议增加配置
2. SOL/USDC池子TVL和交易量都很健康，强烈推荐
3. BNB/USDT池子稳定，可以保持当前持仓
4. 整体风险可控，建议继续执行当前策略
5. 退出时记得转换为BNB(BSC)或SOL(Solana)""",
            title="💡 AI投资建议",
            style="blue"
        )
        
        self.console.print(ai_panel)
    
    def show_main_menu(self):
        """显示主菜单"""
        menu_options = [
            "🔍 专注交易对监控",
            "💼 钱包资产管理",
            "🛡️ 风险分析仪表板",
            "⚙️ 策略配置查看",
            "📊 投资组合概览",
            "🆘 帮助文档",
            "🚪 退出演示"
        ]
        
        menu_text = "\n".join([f"{i+1}. {option}" for i, option in enumerate(menu_options)])
        
        menu_panel = Panel(
            menu_text,
            title="🎮 DyFlow Demo 主菜单",
            box=box.ROUNDED,
            style="blue"
        )
        
        return menu_panel
    
    async def run(self):
        """运行演示"""
        self.show_header()
        
        while True:
            try:
                # 显示主菜单
                self.console.print(self.show_main_menu())
                
                choice = Prompt.ask(
                    "\n[bold cyan]请选择功能演示",
                    choices=["1", "2", "3", "4", "5", "6", "7"],
                    default="1"
                )
                
                if choice == "1":
                    await self.simulate_monitoring()
                elif choice == "2":
                    self.console.print(self.show_wallet_details())
                elif choice == "3":
                    self.console.print(self.show_risk_analysis())
                elif choice == "4":
                    self.console.print(self.show_strategy_config())
                elif choice == "5":
                    self.console.print(self.show_portfolio_overview())
                    self.console.print(self.show_focused_pairs_status())
                elif choice == "6":
                    help_text = """
🆘 DyFlow Demo 帮助文档

📋 核心功能:
• 专注交易对监控 - 只关注BNB/USDC/USDT和SOL/USDC
• 钱包资产管理 - 多钱包、多链资产统一管理
• 风险分析仪表板 - 实时风险监控和预警
• 智能退出策略 - 退出时自动转换为BNB或SOL

🎯 策略特点:
• BSC专注: BNB/USDC, BNB/USDT交易对
• Solana专注: SOL/USDC交易对  
• 智能退出: BSC退出为BNB, Solana退出为SOL
• 风险控制: 15%止损, 10%紧急退出

💡 使用建议:
• 定期监控专注交易对
• 关注风险指标变化
• 遵循AI投资建议
• 及时执行退出策略
                    """
                    self.console.print(Panel(help_text, title="帮助文档", style="blue"))
                elif choice == "7":
                    if Confirm.ask("[bold red]确定要退出演示吗?"):
                        self.console.print("\n[bold green]👋 感谢体验DyFlow Demo！")
                        break
                
                if choice != "7":
                    Prompt.ask("\n[dim]按回车键继续...")
                    self.console.clear()
                    self.show_header()
                
            except KeyboardInterrupt:
                if Confirm.ask("\n[bold red]确定要退出吗?"):
                    self.console.print("\n[bold green]👋 再见！")
                    break
            except Exception as e:
                self.console.print(f"\n[bold red]❌ 错误: {e}")
                Prompt.ask("[dim]按回车键继续...")

async def main():
    """主函数"""
    demo = DyFlowDemo()
    await demo.run()

if __name__ == "__main__":
    asyncio.run(main())
