#!/bin/bash

echo "🚀 启动DyFlow 真实数据Dashboard..."
echo "✨ 全新功能特性："
echo "   📊 真实API数据连接"
echo "   🔄 3-5秒随机更新间隔"
echo "   💰 真实价格数据 (CoinGecko)"
echo "   🟡 PancakeSwap真实池子数据"
echo "   🟣 Solana DEX增强数据"
echo "   🤖 AI交易助手"
echo "   💼 投资组合管理"
echo ""
echo "🎨 界面布局："
echo "   📱 左侧 1/3:"
echo "      🤖 Agent执行日志"
echo "      💬 AI交易助手对话"
echo "   📊 右侧 2/3:"
echo "      💰 实时价格指标 (5个代币)"
echo "      🔍 BSC池子扫描 (真实数据)"
echo "      🔍 Solana池子扫描 (增强数据)"
echo "      🚀 一键投资功能"
echo ""
echo "📦 安装依赖..."
pip3 install fastapi uvicorn aiohttp websockets supabase python-multipart --user

echo ""
echo "🌐 启动真实数据服务器..."
echo "📱 Dashboard: http://localhost:8001"
echo "🔌 WebSocket: ws://localhost:8001/ws"
echo "📡 真实数据API: http://localhost:8001/api/real-data"
echo "🚀 交易API: http://localhost:8001/api/execute-trade"
echo ""
echo "💡 功能说明："
echo "   1. 连接CoinGecko获取真实价格"
echo "   2. 连接PancakeSwap API获取真实池子"
echo "   3. 数据每3-5秒随机更新"
echo "   4. 点击池子可查看详情"
echo "   5. 点击'投资'按钮可执行交易"
echo "   6. AI助手支持投资建议和执行"
echo ""
echo "🤖 AI指令示例："
echo "   • '扫描最佳池子' - 获取真实数据分析"
echo "   • '我想投资BNB/USDC' - 获取投资建议"
echo "   • '分析风险' - 评估投资风险"
echo ""
echo "🔄 按 Ctrl+C 停止服务"
echo "=" * 50

# 使用正确的Python路径启动
if [ -f "/Users/<USER>/Library/Python/3.9/bin/uvicorn" ]; then
    /Users/<USER>/Library/Python/3.9/bin/uvicorn dyflow_real_data_backend:app --host 0.0.0.0 --port 8001
else
    # 备用方案
    python3 -m uvicorn dyflow_real_data_backend:app --host 0.0.0.0 --port 8001
fi
