<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DyFlow 实时监控Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }
        
        .header {
            background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .status-bar {
            background: rgba(255,255,255,0.95);
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 120px);
            gap: 10px;
            padding: 10px;
        }
        
        .left-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .right-panel {
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .panel {
            background: rgba(255,255,255,0.95);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .panel h3 {
            margin-bottom: 10px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .agent-logs {
            flex: 1;
            overflow-y: auto;
            max-height: 300px;
        }
        
        .log-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            font-size: 12px;
        }
        
        .log-item.success { border-left-color: #28a745; }
        .log-item.warning { border-left-color: #ffc107; }
        .log-item.error { border-left-color: #dc3545; }
        .log-item.info { border-left-color: #17a2b8; }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            max-height: 250px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: #f8f9fa;
        }
        
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .message.ai {
            background: #e9ecef;
            color: #333;
        }
        
        .chat-input {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .chat-input button {
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .data-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        .data-table th,
        .data-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .data-table tr:hover {
            background: #f5f5f5;
        }
        
        .pools-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .connected {
            background: #28a745;
            color: white;
        }
        
        .disconnected {
            background: #dc3545;
            color: white;
        }
        
        .update-indicator {
            position: fixed;
            top: 50px;
            right: 10px;
            padding: 5px 10px;
            background: #17a2b8;
            color: white;
            border-radius: 15px;
            font-size: 12px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .update-indicator.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">🔴 连接中...</div>
    <div class="update-indicator" id="updateIndicator">📊 数据更新中...</div>
    
    <div class="header">
        <h1>🚀 DyFlow 实时监控Dashboard</h1>
        <p>WebSocket实时连接 | AI Agent对话 | 真正的实时数据更新</p>
    </div>
    
    <div class="status-bar">
        <div class="status-item">
            <div class="status-indicator"></div>
            <span>系统运行正常</span>
        </div>
        <div class="status-item">
            <span>🔗 BSC连接: ✅</span>
        </div>
        <div class="status-item">
            <span>🔗 Solana连接: ✅</span>
        </div>
        <div class="status-item">
            <span>🤖 AI代理: ✅</span>
        </div>
        <div class="status-item">
            <span id="currentTime">🕒 --:--:--</span>
        </div>
        <div class="status-item">
            <span id="updateCount">🔄 更新: 0次</span>
        </div>
    </div>
    
    <div class="main-container">
        <!-- 左侧面板: 1/3 -->
        <div class="left-panel">
            <!-- Agent日志 -->
            <div class="panel agent-logs">
                <h3>🤖 Agent执行日志</h3>
                <div id="agentLogs">
                    <div class="log-item info">
                        <strong>系统启动</strong> | 等待WebSocket连接...
                    </div>
                </div>
            </div>
            
            <!-- AI聊天 -->
            <div class="panel chat-container">
                <h3>💬 AI Agent对话</h3>
                <div class="chat-messages" id="chatMessages">
                    <div class="message ai">
                        🤖 DyFlow AI助手已上线！我可以帮你扫描池子、查看持仓、评估风险等。试试输入指令吧！
                    </div>
                </div>
                <div class="chat-input">
                    <input type="text" id="chatInput" placeholder="输入指令，如：扫描最佳池子">
                    <button onclick="sendMessage()">发送</button>
                </div>
            </div>
        </div>
        
        <!-- 右侧面板: 2/3 -->
        <div class="right-panel">
            <!-- 关键指标 -->
            <div class="panel">
                <h3>📊 关键指标</h3>
                <div class="metrics-grid" id="metricsGrid">
                    <div class="metric-card">
                        <div class="metric-value" id="totalAssets">$0</div>
                        <div class="metric-label">💰 总资产</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="totalLP">$0</div>
                        <div class="metric-label">📊 LP价值</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="totalPnL">$0</div>
                        <div class="metric-label">📈 总盈亏</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="totalFees">$0</div>
                        <div class="metric-label">💎 手续费</div>
                    </div>
                </div>
            </div>
            
            <!-- 钱包和持仓 -->
            <div class="panel">
                <h3>💼 钱包资产 & LP持仓</h3>
                <div class="data-grid">
                    <div>
                        <h4>💼 钱包资产</h4>
                        <table class="data-table" id="walletsTable">
                            <thead>
                                <tr>
                                    <th>钱包</th>
                                    <th>主币</th>
                                    <th>USDC</th>
                                    <th>总价值</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div>
                        <h4>📊 LP持仓</h4>
                        <table class="data-table" id="positionsTable">
                            <thead>
                                <tr>
                                    <th>交易对</th>
                                    <th>APR</th>
                                    <th>盈亏</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- 池子扫描 -->
            <div class="panel">
                <h3>🔍 池子扫描</h3>
                <div class="pools-grid">
                    <div>
                        <h4>🟡 BSC池子 (Top 8)</h4>
                        <table class="data-table" id="bscPoolsTable">
                            <thead>
                                <tr>
                                    <th>交易对</th>
                                    <th>APR</th>
                                    <th>TVL</th>
                                    <th>风险</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div>
                        <h4>🟣 Solana池子 (Top 8)</h4>
                        <table class="data-table" id="solanaPoolsTable">
                            <thead>
                                <tr>
                                    <th>交易对</th>
                                    <th>APR</th>
                                    <th>TVL</th>
                                    <th>风险</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- 交易日志 -->
            <div class="panel">
                <h3>📋 交易日志</h3>
                <table class="data-table" id="tradingLogsTable">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>操作</th>
                            <th>交易对</th>
                            <th>金额</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // WebSocket连接
        let ws;
        let updateCount = 0;
        let chatMessages = [];
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                console.log('WebSocket连接已建立');
                document.getElementById('connectionStatus').textContent = '🟢 已连接';
                document.getElementById('connectionStatus').className = 'connection-status connected';
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                
                if (data.type === 'data_update') {
                    updateDashboard(data);
                } else if (data.type === 'chat_response') {
                    addChatMessage('ai', data.ai_response);
                }
            };
            
            ws.onclose = function(event) {
                console.log('WebSocket连接已关闭');
                document.getElementById('connectionStatus').textContent = '🔴 已断开';
                document.getElementById('connectionStatus').className = 'connection-status disconnected';
                
                // 3秒后重连
                setTimeout(connectWebSocket, 3000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }
        
        function updateDashboard(data) {
            updateCount++;
            
            // 显示更新指示器
            const indicator = document.getElementById('updateIndicator');
            indicator.classList.add('show');
            setTimeout(() => indicator.classList.remove('show'), 1000);
            
            // 更新时间和计数
            document.getElementById('currentTime').textContent = `🕒 ${new Date().toLocaleTimeString()}`;
            document.getElementById('updateCount').textContent = `🔄 更新: ${updateCount}次`;
            
            // 更新关键指标
            updateMetrics(data.metrics);
            
            // 更新钱包数据
            updateWallets(data.wallets);
            
            // 更新LP持仓
            updatePositions(data.positions);
            
            // 更新池子数据
            updatePools(data.bsc_pools, 'bscPoolsTable');
            updatePools(data.solana_pools, 'solanaPoolsTable');
            
            // 更新Agent日志
            updateAgentLogs(data.agent_logs);
            
            // 更新交易日志
            updateTradingLogs(data.trading_logs);
        }
        
        function updateMetrics(metrics) {
            document.getElementById('totalAssets').textContent = `$${metrics.total_assets.toLocaleString()}`;
            document.getElementById('totalLP').textContent = `$${metrics.total_lp.toLocaleString()}`;
            document.getElementById('totalPnL').textContent = `$${metrics.total_pnl > 0 ? '+' : ''}${metrics.total_pnl.toLocaleString()}`;
            document.getElementById('totalFees').textContent = `$${metrics.total_fees.toLocaleString()}`;
        }
        
        function updateWallets(wallets) {
            const tbody = document.querySelector('#walletsTable tbody');
            tbody.innerHTML = '';
            
            Object.entries(wallets).forEach(([key, wallet]) => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${key === 'bsc' ? 'BSC' : 'Solana'}</td>
                    <td>${wallet.BNB ? `${wallet.BNB} BNB` : `${wallet.SOL} SOL`}</td>
                    <td>${wallet.USDC}</td>
                    <td>$${wallet.total_value.toLocaleString()}</td>
                `;
            });
        }
        
        function updatePositions(positions) {
            const tbody = document.querySelector('#positionsTable tbody');
            tbody.innerHTML = '';
            
            positions.forEach(pos => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${pos.pair}</td>
                    <td>${pos.apr}%</td>
                    <td style="color: ${pos.pnl > 0 ? 'green' : 'red'}">$${pos.pnl > 0 ? '+' : ''}${pos.pnl}</td>
                    <td>${pos.status}</td>
                `;
            });
        }
        
        function updatePools(pools, tableId) {
            const tbody = document.querySelector(`#${tableId} tbody`);
            tbody.innerHTML = '';
            
            pools.slice(0, 8).forEach(pool => {
                const row = tbody.insertRow();
                const riskColor = pool.risk_level === '低' ? 'green' : pool.risk_level === '中' ? 'orange' : 'red';
                row.innerHTML = `
                    <td>${pool.pair}</td>
                    <td>${pool.apr}%</td>
                    <td>$${pool.tvl}M</td>
                    <td style="color: ${riskColor}">${pool.risk_level}</td>
                `;
            });
        }
        
        function updateAgentLogs(logs) {
            const container = document.getElementById('agentLogs');
            container.innerHTML = '';
            
            logs.forEach(log => {
                const div = document.createElement('div');
                div.className = `log-item ${log.level.toLowerCase()}`;
                div.innerHTML = `
                    <strong>${log.time}</strong> | ${log.level} | ${log.agent}<br>
                    <small>${log.message}</small>
                `;
                container.appendChild(div);
            });
        }
        
        function updateTradingLogs(logs) {
            const tbody = document.querySelector('#tradingLogsTable tbody');
            tbody.innerHTML = '';
            
            logs.forEach(log => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${log.time}</td>
                    <td>${log.operation}</td>
                    <td>${log.pair}</td>
                    <td>${log.amount}</td>
                    <td>${log.status}</td>
                `;
            });
        }
        
        function addChatMessage(role, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.textContent = content;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (message && ws && ws.readyState === WebSocket.OPEN) {
                addChatMessage('user', message);
                
                ws.send(JSON.stringify({
                    type: 'chat_message',
                    message: message
                }));
                
                input.value = '';
            }
        }
        
        // 回车发送消息
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 页面加载时连接WebSocket
        window.onload = function() {
            connectWebSocket();
        };
    </script>
</body>
</html>
