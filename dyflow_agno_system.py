#!/usr/bin/env python3
"""
DyFlow Agno System - 基于Agno Framework的LP监控和自动调整系统
使用Agno的Agent、Workflow和Team架构
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import structlog

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

# Agno Framework imports
from agno.agent import Agent
from agno.workflow import Workflow
from agno.models.ollama import Ollama

# DyFlow工具导入
from src.tools.pancake_subgraph_tool import PancakeSubgraphTool
from src.tools.meteora_dlmm_tool import MeteoraDLMMTool
from src.tools.jupiter_swap_tool import JupiterSwapTool
from src.tools.oneinch_swap_tool import OneInchSwapTool

logger = structlog.get_logger(__name__)

# ========== Agno Agents ==========

class DyFlowDataAgent(Agent):
    """数据收集Agent - 基于Agno Agent"""
    
    def __init__(self):
        super().__init__(
            name="DataAgent",
            role="DeFi数据收集专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=[
                "你是DyFlow的数据收集专家。",
                "负责从BSC和Solana链上收集LP池子数据。",
                "专注于获取准确、实时的池子信息。",
                "包括TVL、交易量、手续费率等关键指标。",
                "确保数据质量和完整性。",
                "优先收集高TVL和高交易量的优质池子。"
            ],
            reasoning=False,
            show_tool_calls=True,
            markdown=True
        )
        
        # 添加DeFi工具
        self.pancakeswap_tool = PancakeSubgraphTool()
        self.meteora_tool = MeteoraDLMMTool()
        
    def collect_bsc_data(self, max_pools: int = 5) -> Dict[str, Any]:
        """收集BSC链数据"""
        try:
            pools = self.pancakeswap_tool.get_top_pools(limit=max_pools)
            return {
                "chain": "bsc",
                "pools_count": len(pools),
                "pools": [
                    {
                        "address": pool.address,
                        "token0": pool.token0,
                        "token1": pool.token1,
                        "tvl_usd": pool.tvl_usd,
                        "volume_24h": pool.volume_usd_24h,
                        "fee_tier": pool.fee_tier
                    } for pool in pools
                ],
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error("bsc_data_collection_failed", error=str(e))
            return {"chain": "bsc", "error": str(e)}
    
    def collect_solana_data(self, max_pools: int = 5) -> Dict[str, Any]:
        """收集Solana链数据"""
        try:
            pools = self.meteora_tool.get_pools(limit=max_pools)
            return {
                "chain": "solana",
                "pools_count": len(pools),
                "pools": [
                    {
                        "address": pool.address,
                        "name": pool.name,
                        "mint_x": pool.mint_x,
                        "mint_y": pool.mint_y,
                        "liquidity": pool.liquidity,
                        "volume_24h": pool.trade_volume_24h,
                        "fees_24h": pool.fees_24h
                    } for pool in pools[:max_pools]  # 限制数量
                ],
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error("solana_data_collection_failed", error=str(e))
            return {"chain": "solana", "error": str(e)}

class DyFlowAnalysisAgent(Agent):
    """分析Agent - 基于Agno Agent"""
    
    def __init__(self):
        super().__init__(
            name="AnalysisAgent",
            role="DeFi池子分析专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=[
                "你是DyFlow的LP池子分析专家。",
                "负责深度分析LP池子的性能指标。",
                "评估池子的收益潜力和风险水平。",
                "计算APR、无常损失、流动性深度等关键指标。",
                "提供池子质量评分和投资建议。",
                "专注于BSC和Solana链上的主要DEX协议。",
                "使用6因子评分系统进行综合评估。"
            ],
            reasoning=True,
            show_tool_calls=True,
            markdown=True
        )
    
    def analyze_pools(self, pool_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析池子数据"""
        try:
            chain = pool_data.get("chain", "unknown")
            pools = pool_data.get("pools", [])
            
            analysis_results = []
            
            for pool in pools:
                # 简化的分析逻辑
                tvl = pool.get("tvl_usd", 0) or pool.get("liquidity", 0)
                volume_24h = pool.get("volume_24h", 0) or pool.get("trade_volume_24h", 0)
                
                # 计算简单评分
                tvl_score = min(tvl / 1000000, 10)  # TVL评分，最高10分
                volume_score = min(volume_24h / 100000, 10)  # 交易量评分，最高10分
                total_score = (tvl_score + volume_score) / 2
                
                analysis_results.append({
                    "pool_address": pool.get("address", "unknown"),
                    "pool_name": pool.get("name", f"{pool.get('token0', {}).get('symbol', 'Unknown')}/{pool.get('token1', {}).get('symbol', 'Unknown')}"),
                    "tvl_score": round(tvl_score, 2),
                    "volume_score": round(volume_score, 2),
                    "total_score": round(total_score, 2),
                    "recommendation": "BUY" if total_score >= 7 else "HOLD" if total_score >= 4 else "AVOID"
                })
            
            # 排序并选择前3名
            analysis_results.sort(key=lambda x: x["total_score"], reverse=True)
            top_pools = analysis_results[:3]
            
            return {
                "chain": chain,
                "analysis_timestamp": datetime.now().isoformat(),
                "total_pools_analyzed": len(analysis_results),
                "top_pools": top_pools,
                "average_score": round(sum(r["total_score"] for r in analysis_results) / len(analysis_results), 2) if analysis_results else 0
            }
            
        except Exception as e:
            logger.error("pool_analysis_failed", error=str(e))
            return {"error": str(e)}

class DyFlowRiskAgent(Agent):
    """风险评估Agent - 基于Agno Agent"""
    
    def __init__(self):
        super().__init__(
            name="RiskAgent",
            role="DeFi风险评估专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=[
                "你是DyFlow的风险评估专家。",
                "负责评估LP池子的各种风险。",
                "包括无常损失、流动性风险、价格风险等。",
                "监控市场条件和波动性。",
                "提供详细的风险评估报告。",
                "建议风险缓解策略和对冲方案。",
                "识别高风险池子并发出预警。"
            ],
            reasoning=True,
            show_tool_calls=True,
            markdown=True
        )
    
    def assess_risk(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估风险"""
        try:
            chain = analysis_data.get("chain", "unknown")
            top_pools = analysis_data.get("top_pools", [])
            
            risk_assessments = []
            
            for pool in top_pools:
                score = pool.get("total_score", 0)
                
                # 简化的风险评估
                if score >= 8:
                    risk_level = "LOW"
                    risk_score = 2
                elif score >= 6:
                    risk_level = "MEDIUM"
                    risk_score = 5
                elif score >= 4:
                    risk_level = "HIGH"
                    risk_score = 7
                else:
                    risk_level = "VERY_HIGH"
                    risk_score = 9
                
                risk_assessments.append({
                    "pool_name": pool.get("pool_name", "Unknown"),
                    "pool_address": pool.get("pool_address", "unknown"),
                    "risk_level": risk_level,
                    "risk_score": risk_score,
                    "recommendation": pool.get("recommendation", "HOLD"),
                    "risk_factors": [
                        "Impermanent Loss Risk",
                        "Liquidity Risk",
                        "Smart Contract Risk"
                    ]
                })
            
            return {
                "chain": chain,
                "risk_assessment_timestamp": datetime.now().isoformat(),
                "pools_assessed": len(risk_assessments),
                "risk_assessments": risk_assessments,
                "overall_market_risk": "MEDIUM"  # 简化的市场风险评估
            }
            
        except Exception as e:
            logger.error("risk_assessment_failed", error=str(e))
            return {"error": str(e)}

# ========== Agno Workflow ==========

class DyFlowLPWorkflow(Workflow):
    """DyFlow LP监控工作流 - 基于Agno Workflow"""
    
    description: str = "DyFlow LP池子监控和分析工作流程"
    
    def __init__(self):
        super().__init__()
        
        # 创建Agents
        self.data_agent = DyFlowDataAgent()
        self.analysis_agent = DyFlowAnalysisAgent()
        self.risk_agent = DyFlowRiskAgent()
        
        logger.info("dyflow_lp_workflow_initialized")
    
    def run(self, chains: List[str] = None, max_pools: int = 5) -> Dict[str, Any]:
        """执行LP监控工作流"""
        try:
            chains = chains or ["bsc", "solana"]
            workflow_id = f"dyflow_workflow_{int(datetime.now().timestamp())}"
            
            logger.info("workflow_started", workflow_id=workflow_id, chains=chains)
            
            results = {
                "workflow_id": workflow_id,
                "timestamp": datetime.now().isoformat(),
                "chains": chains,
                "status": "running",
                "results": {}
            }
            
            # 步骤1: 数据收集
            print("📡 步骤1: 数据收集...")
            data_results = {}
            
            if "bsc" in chains:
                bsc_data = self.data_agent.collect_bsc_data(max_pools)
                data_results["bsc"] = bsc_data
                print(f"   BSC: 收集到 {bsc_data.get('pools_count', 0)} 个池子")
            
            if "solana" in chains:
                solana_data = self.data_agent.collect_solana_data(max_pools)
                data_results["solana"] = solana_data
                print(f"   Solana: 收集到 {solana_data.get('pools_count', 0)} 个池子")
            
            results["results"]["data_collection"] = data_results
            
            # 步骤2: 池子分析
            print("📊 步骤2: 池子分析...")
            analysis_results = {}
            
            for chain, chain_data in data_results.items():
                if "error" not in chain_data:
                    analysis = self.analysis_agent.analyze_pools(chain_data)
                    analysis_results[chain] = analysis
                    print(f"   {chain.upper()}: 分析完成，平均评分 {analysis.get('average_score', 0)}")
            
            results["results"]["pool_analysis"] = analysis_results
            
            # 步骤3: 风险评估
            print("🛡️ 步骤3: 风险评估...")
            risk_results = {}
            
            for chain, analysis_data in analysis_results.items():
                if "error" not in analysis_data:
                    risk_assessment = self.risk_agent.assess_risk(analysis_data)
                    risk_results[chain] = risk_assessment
                    print(f"   {chain.upper()}: 风险评估完成")
            
            results["results"]["risk_assessment"] = risk_results
            
            # 完成工作流
            results["status"] = "completed"
            results["completion_time"] = datetime.now().isoformat()
            
            logger.info("workflow_completed", workflow_id=workflow_id)
            
            return results
            
        except Exception as e:
            logger.error("workflow_failed", error=str(e))
            return {
                "workflow_id": workflow_id if 'workflow_id' in locals() else "unknown",
                "timestamp": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e)
            }

# ========== DyFlow Agno System ==========

class DyFlowAgnoSystem:
    """DyFlow Agno系统主类"""
    
    def __init__(self):
        self.session_id = f"dyflow_session_{int(datetime.now().timestamp())}"
        self.workflow = DyFlowLPWorkflow()
        
        # 系统配置
        self.config = {
            'supported_chains': ['bsc', 'solana'],
            'default_max_pools': 5,
            'monitoring_interval_minutes': 5
        }
        
        logger.info("dyflow_agno_system_initialized", session_id=self.session_id)
    
    async def initialize(self) -> bool:
        """初始化系统"""
        try:
            print("🚀 初始化DyFlow Agno系统...")
            print("✅ 基于Agno Framework架构")
            print("✅ 使用本地Ollama Qwen2.5模型")
            print("✅ 支持BSC和Solana链")
            print("✅ 集成PancakeSwap和Meteora协议")
            
            return True
            
        except Exception as e:
            logger.error("system_initialization_failed", error=str(e))
            return False
    
    async def run_monitoring_cycle(self, chains: List[str] = None, max_pools: int = None) -> Dict[str, Any]:
        """运行监控周期"""
        chains = chains or self.config['supported_chains']
        max_pools = max_pools or self.config['default_max_pools']
        
        print(f"\n🔍 开始监控周期 - {datetime.now().strftime('%H:%M:%S')}")
        print(f"   目标链: {', '.join(chains)}")
        print(f"   最大池子数: {max_pools}")
        
        # 执行工作流
        result = self.workflow.run(chains=chains, max_pools=max_pools)
        
        if result.get('status') == 'completed':
            print("✅ 监控周期完成")
            self._display_results(result)
        else:
            print(f"❌ 监控周期失败: {result.get('error', '未知错误')}")
        
        return result
    
    def _display_results(self, result: Dict[str, Any]):
        """显示结果摘要"""
        results = result.get('results', {})
        
        # 显示数据收集结果
        data_collection = results.get('data_collection', {})
        for chain, data in data_collection.items():
            if 'error' not in data:
                print(f"📡 {chain.upper()}: 收集到 {data.get('pools_count', 0)} 个池子")
        
        # 显示分析结果
        analysis = results.get('pool_analysis', {})
        for chain, data in analysis.items():
            if 'error' not in data:
                top_pools = data.get('top_pools', [])
                if top_pools:
                    print(f"📊 {chain.upper()} 最佳池子: {top_pools[0].get('pool_name', 'Unknown')} (评分: {top_pools[0].get('total_score', 0)})")
        
        # 显示风险评估结果
        risk_assessment = results.get('risk_assessment', {})
        for chain, data in risk_assessment.items():
            if 'error' not in data:
                print(f"🛡️ {chain.upper()} 整体风险: {data.get('overall_market_risk', 'Unknown')}")

async def main():
    """主函数"""
    print("🚀 DyFlow Agno System")
    print("基于Agno Framework的LP监控和自动调整系统")
    print("=" * 60)
    
    # 创建系统
    dyflow = DyFlowAgnoSystem()
    
    # 初始化
    if not await dyflow.initialize():
        print("❌ 系统初始化失败")
        return False
    
    # 运行监控周期
    result = await dyflow.run_monitoring_cycle()
    
    # 显示最终状态
    print(f"\n📊 会话ID: {dyflow.session_id}")
    print(f"🕐 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return result.get('status') == 'completed'

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
