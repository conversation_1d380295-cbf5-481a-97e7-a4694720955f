#!/usr/bin/env python3
"""
DyFlow Pro - 专业版LP策略系统
集成Rich UI、钱包管理、专注交易对策略
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import box
from rich.align import Align

# DyFlow组件
from src.core.wallet_manager import WalletManager, LPPosition, RiskMetrics
from src.strategies.focused_pairs_strategy import FocusedPairsStrategy
from dyflow_agno_system import DyFlowAgnoSystem

console = Console()

class DyFlowPro:
    """DyFlow Pro主系统"""
    
    def __init__(self):
        self.console = console
        self.session_id = f"dyflow_pro_{int(datetime.now().timestamp())}"
        
        # 初始化组件
        self.wallet_manager = WalletManager()
        self.strategy = FocusedPairsStrategy()
        self.agno_system = DyFlowAgnoSystem()
        
        console.print("[bold green]🚀 DyFlow Pro 初始化完成")
    
    def show_header(self):
        """显示头部"""
        header_text = Text()
        header_text.append("DyFlow Pro", style="bold blue")
        header_text.append(" - 专业LP策略系统", style="bold white")
        
        header_panel = Panel(
            Align.center(header_text),
            box=box.DOUBLE,
            style="blue",
            subtitle="Rich UI | 钱包管理 | 专注交易对策略"
        )
        
        self.console.print(header_panel)
        self.console.print()
    
    def show_portfolio_overview(self):
        """显示投资组合概览"""
        summary = self.wallet_manager.get_portfolio_summary()
        
        # 创建概览表格
        overview_table = Table(title="💼 投资组合概览", box=box.ROUNDED)
        overview_table.add_column("指标", style="cyan")
        overview_table.add_column("数值", style="green", justify="right")
        overview_table.add_column("说明", style="yellow")
        
        overview_table.add_row(
            "总资产价值",
            f"${summary.get('total_portfolio_value', 0):,.2f}",
            "所有钱包总价值"
        )
        overview_table.add_row(
            "LP持仓价值", 
            f"${summary.get('total_lp_value', 0):,.2f}",
            "流动性池持仓价值"
        )
        overview_table.add_row(
            "总盈亏",
            f"${summary.get('total_pnl', 0):,.2f}",
            "未实现盈亏"
        )
        overview_table.add_row(
            "手续费收益",
            f"${summary.get('total_fees_earned', 0):,.2f}",
            "累计手续费收入"
        )
        overview_table.add_row(
            "活跃钱包",
            f"{summary.get('active_wallets', 0)}",
            "正在使用的钱包数"
        )
        overview_table.add_row(
            "持仓数量",
            f"{summary.get('total_positions', 0)}",
            "当前LP持仓数"
        )
        
        return overview_table
    
    def show_chain_breakdown(self):
        """显示链分布"""
        summary = self.wallet_manager.get_portfolio_summary()
        chain_data = summary.get('chain_breakdown', {})
        
        chain_table = Table(title="🔗 链分布", box=box.ROUNDED)
        chain_table.add_column("区块链", style="cyan")
        chain_table.add_column("资产价值", style="green", justify="right")
        chain_table.add_column("占比", style="yellow", justify="right")
        chain_table.add_column("专注交易对", style="magenta")
        
        total_value = sum(chain_data.values())
        
        for chain, value in chain_data.items():
            percentage = (value / total_value * 100) if total_value > 0 else 0
            target_pairs = self.strategy.get_target_pairs(chain)
            pairs_str = ", ".join([f"{pair[0]}/{pair[1]}" for pair in target_pairs])
            
            chain_table.add_row(
                chain.upper(),
                f"${value:,.2f}",
                f"{percentage:.1f}%",
                pairs_str
            )
        
        return chain_table
    
    def show_risk_dashboard(self):
        """显示风险仪表板"""
        risk_table = Table(title="🛡️ 风险监控", box=box.ROUNDED)
        risk_table.add_column("钱包", style="cyan")
        risk_table.add_column("总Delta", style="yellow", justify="right")
        risk_table.add_column("最大损失", style="red", justify="right")
        risk_table.add_column("风险等级", style="magenta", justify="center")
        risk_table.add_column("最后更新", style="dim")
        
        for wallet_id, wallet in self.wallet_manager.wallets.items():
            if not wallet.is_active:
                continue
                
            risk_metrics = self.wallet_manager.risk_metrics.get(wallet_id)
            if risk_metrics:
                risk_color = {
                    'LOW': 'green',
                    'MEDIUM': 'yellow',
                    'HIGH': 'red',
                    'CRITICAL': 'bright_red'
                }.get(risk_metrics.risk_level, 'white')
                
                risk_table.add_row(
                    f"{wallet.name} ({wallet.chain.upper()})",
                    f"${risk_metrics.total_delta:,.2f}",
                    f"${risk_metrics.max_possible_loss:,.2f}",
                    Text(risk_metrics.risk_level, style=risk_color),
                    risk_metrics.last_calculated[:16]
                )
        
        return risk_table
    
    def show_strategy_status(self):
        """显示策略状态"""
        strategy_summary = self.strategy.get_strategy_summary()
        
        strategy_table = Table(title="⚙️ 策略状态", box=box.ROUNDED)
        strategy_table.add_column("配置项", style="cyan")
        strategy_table.add_column("BSC", style="yellow")
        strategy_table.add_column("Solana", style="magenta")
        
        bsc_pairs = ", ".join([f"{p[0]}/{p[1]}" for p in strategy_summary['target_pairs']['bsc']])
        sol_pairs = ", ".join([f"{p[0]}/{p[1]}" for p in strategy_summary['target_pairs']['solana']])
        
        strategy_table.add_row("目标交易对", bsc_pairs, sol_pairs)
        strategy_table.add_row("退出代币", 
                              strategy_summary['exit_tokens']['bsc'],
                              strategy_summary['exit_tokens']['solana'])
        strategy_table.add_row("止损阈值", 
                              strategy_summary['risk_management']['stop_loss'],
                              strategy_summary['risk_management']['stop_loss'])
        strategy_table.add_row("最大持仓", 
                              strategy_summary['risk_management']['max_position_size'],
                              strategy_summary['risk_management']['max_position_size'])
        
        return strategy_table
    
    def show_main_menu(self):
        """显示主菜单"""
        menu_options = [
            "🔍 池子监控 (专注交易对)",
            "💼 钱包管理",
            "📊 风险分析", 
            "⚙️ 策略配置",
            "🔄 执行重平衡",
            "📈 历史记录",
            "🧪 系统测试",
            "🆘 帮助文档",
            "🚪 退出系统"
        ]
        
        menu_text = "\n".join([f"{i+1}. {option}" for i, option in enumerate(menu_options)])
        
        menu_panel = Panel(
            menu_text,
            title="🎮 主菜单",
            box=box.ROUNDED,
            style="blue"
        )
        
        return menu_panel
    
    async def run_focused_monitoring(self):
        """运行专注交易对监控"""
        self.console.print("\n[bold green]🔍 启动专注交易对监控...")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            task = progress.add_task("[cyan]正在监控专注交易对...", total=None)
            
            try:
                # 运行Agno系统监控
                result = await self.agno_system.run_monitoring_cycle(
                    chains=["bsc", "solana"], 
                    max_pools=10
                )
                
                progress.update(task, description="[green]✅ 监控完成，正在过滤专注交易对...")
                
                # 过滤专注交易对
                filtered_results = self.filter_monitoring_results(result)
                
                progress.update(task, description="[green]✅ 专注交易对监控完成")
                
                # 显示结果
                self.show_monitoring_results(filtered_results)
                
                return filtered_results
                
            except Exception as e:
                progress.update(task, description=f"[red]❌ 监控失败: {str(e)}")
                return None
    
    def filter_monitoring_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """过滤监控结果，只保留专注交易对"""
        if not results or results.get('status') != 'completed':
            return results
        
        filtered_results = results.copy()
        
        # 过滤池子分析结果
        pool_analysis = results.get('results', {}).get('pool_analysis', {})
        filtered_analysis = {}
        
        for chain, analysis_data in pool_analysis.items():
            if 'error' in analysis_data:
                filtered_analysis[chain] = analysis_data
                continue
            
            top_pools = analysis_data.get('top_pools', [])
            focused_pools = []
            
            for pool in top_pools:
                pool_name = pool.get('pool_name', '')
                # 简单的交易对检查
                if any(pair in pool_name.upper() for pair in ['BNB/USDC', 'BNB/USDT', 'SOL/USDC']):
                    focused_pools.append(pool)
            
            filtered_analysis[chain] = {
                **analysis_data,
                'top_pools': focused_pools,
                'focused_pools_count': len(focused_pools)
            }
        
        filtered_results['results']['pool_analysis'] = filtered_analysis
        return filtered_results
    
    def show_monitoring_results(self, results: Dict[str, Any]):
        """显示监控结果"""
        if not results:
            self.console.print("[red]❌ 暂无监控结果")
            return
        
        results_table = Table(title="📊 专注交易对监控结果", box=box.ROUNDED)
        results_table.add_column("链", style="cyan")
        results_table.add_column("交易对", style="yellow")
        results_table.add_column("评分", style="green", justify="right")
        results_table.add_column("建议", style="magenta", justify="center")
        results_table.add_column("退出代币", style="blue", justify="center")
        
        pool_analysis = results.get('results', {}).get('pool_analysis', {})
        
        for chain, analysis_data in pool_analysis.items():
            if 'error' in analysis_data:
                continue
            
            focused_pools = analysis_data.get('top_pools', [])
            exit_token = self.strategy.focused_pairs[chain].exit_token
            
            for pool in focused_pools:
                recommendation = pool.get('recommendation', 'HOLD')
                rec_color = {
                    'BUY': 'green',
                    'HOLD': 'yellow', 
                    'AVOID': 'red'
                }.get(recommendation, 'white')
                
                results_table.add_row(
                    chain.upper(),
                    pool.get('pool_name', 'Unknown'),
                    f"{pool.get('total_score', 0):.1f}/10",
                    Text(recommendation, style=rec_color),
                    exit_token
                )
        
        self.console.print(results_table)
    
    async def manage_wallets(self):
        """钱包管理"""
        self.console.print("\n[bold blue]💼 钱包管理")
        
        while True:
            # 显示当前钱包
            self.console.print(self.show_portfolio_overview())
            self.console.print(self.show_chain_breakdown())
            
            wallet_menu = [
                "添加新钱包",
                "查看钱包详情", 
                "更新风险指标",
                "返回主菜单"
            ]
            
            choice = Prompt.ask(
                "\n[bold cyan]钱包管理选项",
                choices=["1", "2", "3", "4"],
                default="4"
            )
            
            if choice == "1":
                await self.add_new_wallet()
            elif choice == "2":
                await self.view_wallet_details()
            elif choice == "3":
                await self.update_risk_metrics()
            elif choice == "4":
                break
    
    async def add_new_wallet(self):
        """添加新钱包"""
        self.console.print("\n[bold green]➕ 添加新钱包")
        
        name = Prompt.ask("钱包名称")
        chain = Prompt.ask("区块链", choices=["bsc", "solana"])
        address = Prompt.ask("钱包地址")
        
        # 注意：实际使用中应该更安全地处理私钥
        private_key = Prompt.ask("私钥 (将被加密存储)", password=True)
        
        try:
            wallet_id = self.wallet_manager.add_wallet(name, chain, address, private_key)
            self.console.print(f"[green]✅ 钱包添加成功！ID: {wallet_id}")
        except Exception as e:
            self.console.print(f"[red]❌ 添加失败: {e}")
    
    async def view_wallet_details(self):
        """查看钱包详情"""
        if not self.wallet_manager.wallets:
            self.console.print("[yellow]⚠️ 暂无钱包")
            return
        
        # 显示钱包列表
        wallet_list = list(self.wallet_manager.wallets.keys())
        for i, wallet_id in enumerate(wallet_list):
            wallet = self.wallet_manager.wallets[wallet_id]
            self.console.print(f"{i+1}. {wallet.name} ({wallet.chain.upper()}) - {wallet.address[:10]}...")
        
        try:
            choice = int(Prompt.ask("选择钱包")) - 1
            if 0 <= choice < len(wallet_list):
                wallet_id = wallet_list[choice]
                summary = self.wallet_manager.get_wallet_summary(wallet_id)
                
                # 显示详细信息
                detail_table = Table(title=f"钱包详情: {summary['wallet_info']['name']}", box=box.ROUNDED)
                detail_table.add_column("项目", style="cyan")
                detail_table.add_column("值", style="yellow")
                
                detail_table.add_row("地址", summary['wallet_info']['address'])
                detail_table.add_row("链", summary['wallet_info']['chain'].upper())
                detail_table.add_row("总价值", f"${summary['total_value']:,.2f}")
                detail_table.add_row("总盈亏", f"${summary['total_pnl']:,.2f}")
                detail_table.add_row("手续费收益", f"${summary['total_fees']:,.2f}")
                detail_table.add_row("持仓数量", str(summary['position_count']))
                
                self.console.print(detail_table)
        except (ValueError, IndexError):
            self.console.print("[red]❌ 无效选择")
    
    async def update_risk_metrics(self):
        """更新风险指标"""
        self.console.print("\n[bold yellow]📊 更新风险指标...")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            task = progress.add_task("[cyan]正在计算风险指标...", total=len(self.wallet_manager.wallets))
            
            for wallet_id in self.wallet_manager.wallets:
                try:
                    self.wallet_manager.calculate_risk_metrics(wallet_id)
                    progress.advance(task)
                except Exception as e:
                    self.console.print(f"[red]❌ 计算 {wallet_id} 风险失败: {e}")
            
            progress.update(task, description="[green]✅ 风险指标更新完成")
        
        # 显示风险仪表板
        self.console.print(self.show_risk_dashboard())
    
    async def run(self):
        """运行DyFlow Pro"""
        self.show_header()
        
        # 初始化Agno系统
        await self.agno_system.initialize()
        
        while True:
            try:
                # 显示概览
                self.console.print(self.show_portfolio_overview())
                self.console.print(self.show_strategy_status())
                
                # 显示主菜单
                self.console.print(self.show_main_menu())
                
                choice = Prompt.ask(
                    "\n[bold cyan]请选择操作",
                    choices=["1", "2", "3", "4", "5", "6", "7", "8", "9"],
                    default="1"
                )
                
                if choice == "1":
                    await self.run_focused_monitoring()
                elif choice == "2":
                    await self.manage_wallets()
                elif choice == "3":
                    await self.update_risk_metrics()
                elif choice == "4":
                    self.console.print(self.show_strategy_status())
                elif choice == "5":
                    self.console.print("[yellow]🔄 重平衡功能开发中...")
                elif choice == "6":
                    self.console.print("[yellow]📈 历史记录功能开发中...")
                elif choice == "7":
                    self.console.print("[green]🧪 运行系统测试...")
                    from test_agno_success import main as test_main
                    await test_main()
                elif choice == "8":
                    self.console.print("[blue]🆘 帮助文档功能开发中...")
                elif choice == "9":
                    if Confirm.ask("[bold red]确定要退出DyFlow Pro吗?"):
                        self.console.print("\n[bold green]👋 感谢使用DyFlow Pro，再见！")
                        break
                
                if choice != "9":
                    Prompt.ask("\n[dim]按回车键继续...")
                    self.console.clear()
                    self.show_header()
                
            except KeyboardInterrupt:
                if Confirm.ask("\n[bold red]确定要退出吗?"):
                    self.console.print("\n[bold green]👋 再见！")
                    break
            except Exception as e:
                self.console.print(f"\n[bold red]❌ 错误: {e}")
                Prompt.ask("[dim]按回车键继续...")

async def main():
    """主函数"""
    dyflow_pro = DyFlowPro()
    await dyflow_pro.run()

if __name__ == "__main__":
    asyncio.run(main())
