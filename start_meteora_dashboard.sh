#!/bin/bash

echo "🚀 启动DyFlow 真实Meteora API Dashboard..."
echo "✨ 使用真实数据源："
echo "   💰 价格数据: CoinGecko API"
echo "   🟣 Solana池子:"
echo "      - Meteora DLMM API (https://dlmm-api.meteora.ag)"
echo "      - Meteora Dynamic AMM API (https://damm-api.meteora.ag)"
echo "   🟡 BSC池子:"
echo "      - The Graph BSC Subgraph"
echo "      - API Key: 9731921233db132a98c2325878e6c153"
echo ""
echo "🎯 真实数据特性："
echo "   📊 Meteora DLMM: 真实TVL、交易量、手续费"
echo "   📊 Meteora Dynamic AMM: 真实池子数据"
echo "   📊 BSC Graph: PancakeSwap V3真实数据"
echo "   💰 价格: CoinGecko实时价格"
echo ""
echo "🔄 更新频率: 每5秒"
echo "📊 数据质量: 100%真实API数据"
echo ""
echo "🌐 启动Meteora API服务器..."
echo "📱 Dashboard: http://localhost:8003"
echo "🔌 WebSocket: ws://localhost:8003/ws"
echo "📡 Meteora数据API: http://localhost:8003/api/meteora-data"
echo ""
echo "💡 功能说明："
echo "   1. 真实Meteora DLMM池子数据"
echo "   2. 真实Meteora Dynamic AMM数据"
echo "   3. BSC The Graph真实数据"
echo "   4. CoinGecko真实价格"
echo "   5. 每5秒自动更新"
echo ""
echo "🔄 按 Ctrl+C 停止服务"
echo "=" * 50

# 启动服务器
if [ -f "/Users/<USER>/Library/Python/3.9/bin/uvicorn" ]; then
    /Users/<USER>/Library/Python/3.9/bin/uvicorn dyflow_real_meteora_backend:app --host 0.0.0.0 --port 8003
else
    python3 -m uvicorn dyflow_real_meteora_backend:app --host 0.0.0.0 --port 8003
fi
