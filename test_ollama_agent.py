#!/usr/bin/env python3
"""
测试Ollama Agent集成
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

from src.agents.base_agent import BaseAgent, AgentConfig

async def test_ollama_agent():
    """测试Ollama Agent"""
    print("🤖 测试Ollama Agent集成")
    print("=" * 50)
    
    try:
        # 创建使用Ollama的Agent配置
        config = AgentConfig(
            name="OllamaTestAgent",
            model_provider="ollama",
            model_name="qwen2.5:3b",
            enable_memory=False,
            enable_reasoning=False
        )
        
        # 创建Agent
        agent = BaseAgent(config)
        print("✅ Agent创建成功")
        
        # 初始化Agent
        init_success = await agent.initialize()
        if init_success:
            print("✅ Ollama Agent初始化成功")
        else:
            print("❌ Ollama Agent初始化失败")
            return False
        
        # 测试简单的对话
        print("\n🔄 测试Agent对话...")
        
        test_tasks = [
            "你好，请简单介绍一下你自己",
            "什么是DeFi？请用一句话回答",
            "解释一下什么是流动性挖矿"
        ]
        
        for i, task in enumerate(test_tasks, 1):
            print(f"\n测试 {i}: {task}")
            
            result = await agent.execute(task)
            
            if result.success:
                print(f"✅ 响应: {result.data.get('response', '')[:100]}...")
                print(f"   执行时间: {result.execution_time:.2f}秒")
            else:
                print(f"❌ 执行失败: {result.error}")
        
        # 显示Agent状态
        status = agent.get_status()
        print(f"\n📊 Agent状态:")
        print(f"   执行次数: {status['execution_count']}")
        print(f"   错误次数: {status['error_count']}")
        print(f"   成功率: {status['success_rate']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_ollama_connection():
    """测试Ollama连接"""
    print("🔗 测试Ollama连接")
    print("-" * 30)
    
    try:
        import requests
        
        # 测试Ollama服务是否运行
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama服务运行正常，发现 {len(models)} 个模型")
            
            # 检查qwen2.5模型
            qwen_models = [m for m in models if "qwen2.5" in m.get("name", "")]
            if qwen_models:
                print(f"✅ 找到Qwen2.5模型: {[m['name'] for m in qwen_models]}")
                return True
            else:
                print("⚠️  未找到Qwen2.5模型")
                return False
        else:
            print(f"❌ Ollama服务连接失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Ollama连接测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始Ollama Agent测试")
    print("=" * 50)
    
    # 1. 测试Ollama连接
    connection_ok = await test_ollama_connection()
    if not connection_ok:
        print("\n❌ Ollama连接失败，请确保:")
        print("   1. Ollama已安装并运行")
        print("   2. qwen2.5:3b模型已下载")
        print("   3. 服务运行在 http://localhost:11434")
        return False
    
    print("\n" + "=" * 50)
    
    # 2. 测试Agent
    agent_ok = await test_ollama_agent()
    
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    if connection_ok and agent_ok:
        print("✅ 所有测试通过！Ollama Agent集成成功")
        print("🎉 现在可以使用本地Qwen2.5模型进行AI推理")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
