#!/usr/bin/env python3
"""
DyFlow 自动刷新WebUI - 新布局设计
左侧1/3: 上方Agent日志 + 下方AI聊天
右侧2/3: 所有数据信息展开显示
"""

import streamlit as st
import pandas as pd
from datetime import datetime
import random
import time
import threading

# 页面配置
st.set_page_config(
    page_title="DyFlow - 自动刷新监控Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="collapsed"
)

class DyFlowAutoRefreshUI:
    """自动刷新WebUI类"""
    
    def __init__(self):
        self.initialize_session_state()
    
    def initialize_session_state(self):
        """初始化会话状态"""
        if 'chat_messages' not in st.session_state:
            st.session_state.chat_messages = [
                {"role": "assistant", "content": "🤖 DyFlow AI助手已上线！\n\n我可以帮你：\n- 扫描最佳池子\n- 查看持仓状态\n- 风险评估\n- 价格监控\n\n试试输入指令吧！"}
            ]
        if 'refresh_counter' not in st.session_state:
            st.session_state.refresh_counter = 0
        if 'auto_refresh_enabled' not in st.session_state:
            st.session_state.auto_refresh_enabled = True
        if 'last_refresh_time' not in st.session_state:
            st.session_state.last_refresh_time = datetime.now()
    
    def generate_agent_logs(self):
        """生成Agent日志"""
        current_time = datetime.now()
        
        agents = ["LP Manager", "Risk Sentinel", "Pool Scanner", "Yield Optimizer", "Price Monitor", "Market Analyzer"]
        levels = ["✅ SUCCESS", "⚠️ WARNING", "ℹ️ INFO", "❌ ERROR"]
        messages = [
            "成功优化价格范围，APR提升2.5%",
            "检测到高APR池子，建议深度分析",
            "持仓风险评估完成，所有持仓在安全范围内",
            "自动收取手续费操作成功",
            "发现套利机会，正在执行交易",
            "网络拥堵检测，延迟执行交易",
            "价格突破关键阻力位，触发重新平衡",
            "BNB/USD1池子APR异常上涨至45.2%",
            "SOL价格突破$145，触发重新平衡条件",
            "成功收取BNB/USDC池子手续费 $156",
            "监控到持仓盈亏转正，风险等级下调",
            "检测到网络Gas费用上涨，暂停交易"
        ]
        
        logs = []
        for i in range(8):  # 显示8条最新日志
            time_offset = current_time - pd.Timedelta(minutes=random.randint(1, 60))
            logs.append({
                "时间": time_offset.strftime("%H:%M"),
                "等级": random.choice(levels),
                "Agent": random.choice(agents),
                "消息": random.choice(messages)
            })
        
        # 按时间排序，最新的在前
        logs.sort(key=lambda x: x["时间"], reverse=True)
        return logs
    
    def generate_dynamic_data(self):
        """生成所有动态数据"""
        # 钱包数据
        bnb_price = 680 + random.uniform(-20, 20)
        sol_price = 140 + random.uniform(-10, 10)
        
        wallets = {
            "BSC主钱包": {
                "地址": "0x1234...5678",
                "BNB": f"{2.45 + random.uniform(-0.1, 0.1):.2f}",
                "USDC": f"{1250.30 + random.uniform(-50, 50):.2f}",
                "USDT": f"{890.75 + random.uniform(-30, 30):.2f}",
                "USD1": f"{650.20 + random.uniform(-20, 20):.2f}",
                "总价值": f"${4457.75 + random.uniform(-200, 200):.2f}"
            },
            "Solana主钱包": {
                "地址": "9WzDXw...AWWM",
                "SOL": f"{15.67 + random.uniform(-0.5, 0.5):.2f}",
                "USDC": f"{2100.50 + random.uniform(-100, 100):.2f}",
                "总价值": f"${4294.75 + random.uniform(-150, 150):.2f}"
            }
        }
        
        # LP持仓数据
        positions = [
            {
                "链": "BSC",
                "交易对": "BNB/USDC",
                "流动性价值": "$8,500",
                "APR": f"{24.5 + random.uniform(-3, 5):.1f}%",
                "盈亏": f"+${320.50 + random.uniform(-30, 50):.2f}",
                "手续费": f"${45.20 + random.uniform(0, 8):.2f}",
                "状态": "🟢 活跃"
            },
            {
                "链": "BSC",
                "交易对": "BNB/USDT",
                "流动性价值": "$6,200",
                "APR": f"{21.2 + random.uniform(-3, 5):.1f}%",
                "盈亏": f"-${85.30 - random.uniform(-15, 25):.2f}",
                "手续费": f"${28.90 + random.uniform(0, 5):.2f}",
                "状态": "🟡 监控"
            },
            {
                "链": "Solana",
                "交易对": "SOL/USDC",
                "流动性价值": "$12,000",
                "APR": f"{32.8 + random.uniform(-3, 8):.1f}%",
                "盈亏": f"+${480.75 + random.uniform(-40, 80):.2f}",
                "手续费": f"${67.40 + random.uniform(0, 12):.2f}",
                "状态": "🟢 活跃"
            }
        ]
        
        # BSC池子数据
        bsc_pools = []
        pairs = ["BNB/USDC", "BNB/USDT", "BNB/USD1"]
        protocols = ["PancakeSwap V3", "PancakeSwap V2", "Uniswap V3", "SushiSwap"]
        
        for i in range(12):
            base_apr = 20 + i * 3 + random.uniform(-5, 15)
            risk_level = "🟢 低" if i < 4 else "🟡 中" if i < 8 else "🔴 高"
            
            bsc_pools.append({
                "交易对": random.choice(pairs),
                "协议": random.choice(protocols),
                "TVL": f"${random.uniform(0.5, 5.0):.1f}M",
                "APR": f"{base_apr:.1f}%",
                "风险": risk_level,
                "建议": random.choice(["🟢 BUY", "🟡 HOLD", "🔴 AVOID"])
            })
        
        # Solana池子数据
        solana_pools = []
        protocols = ["Meteora DLMM", "Orca Whirlpool", "Raydium CLMM", "Lifinity"]
        
        for i in range(12):
            base_apr = 25 + i * 4 + random.uniform(-8, 20)
            risk_level = "🟢 低" if i < 4 else "🟡 中" if i < 8 else "🔴 高"
            
            solana_pools.append({
                "交易对": "SOL/USDC",
                "协议": random.choice(protocols),
                "TVL": f"${random.uniform(1.0, 8.0):.1f}M",
                "APR": f"{base_apr:.1f}%",
                "风险": risk_level,
                "建议": random.choice(["🟢 BUY", "🟡 HOLD", "🔴 AVOID"])
            })
        
        # 按APR排序
        bsc_pools.sort(key=lambda x: float(x["APR"].replace("%", "")), reverse=True)
        solana_pools.sort(key=lambda x: float(x["APR"].replace("%", "")), reverse=True)
        
        # 交易日志
        trading_logs = [
            {"时间": datetime.now().strftime("%H:%M"), "操作": "调整范围", "交易对": "BNB/USDC", "金额": "$2,306", "状态": "⏳ 处理中"},
            {"时间": "19:45", "操作": "开仓", "交易对": "SOL/USDC", "金额": "$12,000", "状态": "✅ 成功"},
            {"时间": "18:30", "操作": "增加流动性", "交易对": "BNB/USD1", "金额": "$3,500", "状态": "✅ 成功"},
            {"时间": "17:22", "操作": "收取手续费", "交易对": "BNB/USDT", "金额": "$45.20", "状态": "✅ 成功"},
            {"时间": "16:15", "操作": "平仓", "交易对": "SOL/USDC", "金额": "$8,500", "状态": "✅ 成功"},
        ]
        
        return wallets, positions, bsc_pools, solana_pools, trading_logs
    
    def process_chat_message(self, user_input: str) -> str:
        """处理聊天消息"""
        user_input_lower = user_input.lower()
        
        if "扫描" in user_input_lower or "池子" in user_input_lower or "最佳" in user_input_lower:
            _, _, bsc_pools, solana_pools, _ = self.generate_dynamic_data()
            
            response = "🔍 **最佳池子扫描结果**:\n\n"
            response += "**🟡 BSC Top 3:**\n"
            for i, pool in enumerate(bsc_pools[:3], 1):
                response += f"{i}. {pool['交易对']} - APR: {pool['APR']}\n"
            
            response += "\n**🟣 Solana Top 3:**\n"
            for i, pool in enumerate(solana_pools[:3], 1):
                response += f"{i}. {pool['交易对']} - APR: {pool['APR']}\n"
                
        elif "持仓" in user_input_lower or "状态" in user_input_lower:
            _, positions, _, _, _ = self.generate_dynamic_data()
            response = "📊 **当前LP持仓状态**:\n\n"
            for pos in positions:
                response += f"🔗 {pos['交易对']} ({pos['链']})\n"
                response += f"   APR: {pos['APR']} | 盈亏: {pos['盈亏']}\n\n"
                
        elif "风险" in user_input_lower:
            response = "🛡️ **风险评估**:\n\n"
            response += "📊 整体风险: 🟡 中等\n"
            response += "💹 无常损失: -2.1%\n"
            response += "🔄 流动性风险: 低\n"
            response += "💡 建议: 持仓风险可控"
            
        elif "价格" in user_input_lower:
            bnb_price = 680 + random.uniform(-20, 20)
            sol_price = 140 + random.uniform(-10, 10)
            response = f"💰 **实时价格**:\n\n"
            response += f"🟡 BNB: ${bnb_price:.2f}\n"
            response += f"🟣 SOL: ${sol_price:.2f}\n"
            response += f"💵 USDC: $1.00\n"
            response += f"💵 USD1: $0.998"
            
        else:
            responses = [
                "🤖 我可以帮你扫描池子、查看持仓、评估风险等。",
                "💡 试试：'扫描最佳池子'、'查看持仓状态'、'风险评估'",
                "🔍 需要我帮你分析什么数据？",
            ]
            response = random.choice(responses)
        
        return response
    
    def render_chat_interface(self):
        """渲染聊天界面"""
        st.markdown("### 🤖 AI Agent 对话")
        
        # 聊天容器，限制高度
        chat_container = st.container()
        with chat_container:
            # 显示最近5条消息
            recent_messages = st.session_state.chat_messages[-5:]
            for message in recent_messages:
                with st.chat_message(message["role"]):
                    st.markdown(message["content"])
        
        # 聊天输入
        if prompt := st.chat_input("输入指令..."):
            # 添加用户消息
            st.session_state.chat_messages.append({"role": "user", "content": prompt})
            
            # 生成AI回复
            response = self.process_chat_message(prompt)
            st.session_state.chat_messages.append({"role": "assistant", "content": response})
            
            # 重新运行
            st.rerun()
    
    def auto_refresh_logic(self):
        """自动刷新逻辑"""
        if st.session_state.auto_refresh_enabled:
            # 使用更稳定的刷新机制
            placeholder = st.empty()

            # 倒计时显示
            for i in range(5, 0, -1):
                with placeholder.container():
                    st.info(f"⏱️ {i}秒后自动刷新...")
                time.sleep(1)

            placeholder.empty()
            st.session_state.refresh_counter += 1
            st.session_state.last_refresh_time = datetime.now()
            st.rerun()
    
    def run(self):
        """运行WebUI"""
        # 页面标题
        st.markdown("""
        <div style="background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
            <h1 style="color: white; text-align: center; margin: 0; font-size: 28px;">
                🚀 DyFlow 实时监控Dashboard
            </h1>
            <p style="color: #e0e0e0; text-align: center; margin: 5px 0 0 0; font-size: 14px;">
                自动刷新 | AI对话 | 实时数据 | 24/7监控
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # 自动刷新控制栏
        col1, col2, col3, col4 = st.columns([2, 1, 1, 2])
        with col1:
            auto_refresh = st.checkbox("🔄 自动刷新 (5秒)", value=st.session_state.auto_refresh_enabled)
            st.session_state.auto_refresh_enabled = auto_refresh
        with col2:
            if st.button("🔄 立即刷新"):
                st.session_state.refresh_counter += 1
                st.rerun()
        with col3:
            st.metric("刷新次数", st.session_state.refresh_counter)
        with col4:
            st.info(f"🕒 {datetime.now().strftime('%H:%M:%S')} | 系统运行正常 ✅")
        
        # 主要布局：左侧1/3，右侧2/3
        left_col, right_col = st.columns([1, 2])
        
        # 左侧：Agent日志 + AI聊天
        with left_col:
            # 上半部分：Agent日志
            st.markdown("### 🤖 Agent执行日志")
            agent_logs = self.generate_agent_logs()
            
            # 创建紧凑的Agent日志显示
            for log in agent_logs:
                level_color = {
                    "✅ SUCCESS": "success",
                    "⚠️ WARNING": "warning", 
                    "ℹ️ INFO": "info",
                    "❌ ERROR": "error"
                }.get(log["等级"], "info")
                
                with st.container():
                    st.markdown(f"""
                    <div style="border-left: 3px solid #{'28a745' if level_color=='success' else 'ffc107' if level_color=='warning' else 'dc3545' if level_color=='error' else '17a2b8'}; 
                                padding: 8px; margin: 5px 0; background: #f8f9fa; border-radius: 5px;">
                        <small><strong>{log['时间']}</strong> | {log['等级']} | {log['Agent']}</small><br>
                        <span style="font-size: 12px;">{log['消息']}</span>
                    </div>
                    """, unsafe_allow_html=True)
            
            st.markdown("---")
            
            # 下半部分：AI聊天
            self.render_chat_interface()
        
        # 右侧：所有数据信息
        with right_col:
            # 生成所有数据
            wallets, positions, bsc_pools, solana_pools, trading_logs = self.generate_dynamic_data()
            
            # 关键指标
            st.markdown("### 📊 关键指标")
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("💰 总资产", "$35,452", f"+${715 + random.randint(-50, 50)}")
            with col2:
                st.metric("📊 LP价值", "$26,700", f"{random.uniform(-1, 3):.1f}%")
            with col3:
                st.metric("📈 总盈亏", f"+${715 + random.randint(-50, 50)}", f"{random.uniform(-1, 3):.1f}%")
            with col4:
                st.metric("💎 手续费", f"${141 + random.randint(0, 20)}")
            
            # 钱包和持仓
            st.markdown("### 💼 钱包资产 & LP持仓")
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**💼 钱包资产**")
                wallet_data = []
                for name, wallet in wallets.items():
                    wallet_data.append({
                        "钱包": name.replace("主钱包", ""),
                        "地址": wallet["地址"],
                        "主币": f"{wallet.get('BNB', wallet.get('SOL', 'N/A'))} {'BNB' if 'BNB' in wallet else 'SOL'}",
                        "USDC": wallet["USDC"],
                        "总价值": wallet["总价值"]
                    })
                df_wallets = pd.DataFrame(wallet_data)
                st.dataframe(df_wallets, use_container_width=True, hide_index=True, height=150)
            
            with col2:
                st.markdown("**📊 LP持仓状态**")
                df_positions = pd.DataFrame(positions)
                st.dataframe(df_positions, use_container_width=True, hide_index=True, height=150)
            
            # 池子扫描
            st.markdown("### 🔍 池子扫描")
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**🟡 BSC池子 (Top 8)**")
                df_bsc = pd.DataFrame(bsc_pools[:8])
                st.dataframe(df_bsc, use_container_width=True, hide_index=True, height=300)
            
            with col2:
                st.markdown("**🟣 Solana池子 (Top 8)**")
                df_sol = pd.DataFrame(solana_pools[:8])
                st.dataframe(df_sol, use_container_width=True, hide_index=True, height=300)
            
            # 交易日志
            st.markdown("### 📋 交易日志")
            df_trading = pd.DataFrame(trading_logs)
            st.dataframe(df_trading, use_container_width=True, hide_index=True, height=200)
        
        # 自动刷新逻辑 - 放在页面最底部
        if st.session_state.auto_refresh_enabled:
            self.auto_refresh_logic()

def main():
    """主函数"""
    app = DyFlowAutoRefreshUI()
    app.run()

if __name__ == "__main__":
    main()
