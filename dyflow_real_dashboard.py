#!/usr/bin/env python3
"""
DyFlow Real Dashboard - 真正的一屏显示所有信息的Dashboard
启动后立即显示完整的系统状态，底部有AI对话区域
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import json
import threading
import queue

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.prompt import Prompt
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich import box
from rich.align import Align
from rich.columns import Columns

console = Console()

class DyFlowRealDashboard:
    """DyFlow真正的Dashboard - 一屏显示所有信息"""
    
    def __init__(self):
        self.console = console
        self.user_input_queue = queue.Queue()
        self.ai_response_queue = queue.Queue()
        self.chat_history = []
        
        # 系统实时数据
        self.system_data = {
            "timestamp": datetime.now(),
            "connections": {
                "BSC RPC": {"status": "🟢 已连接", "latency": 45, "block": 41234567, "health": 98},
                "Solana RPC": {"status": "🟢 已连接", "latency": 32, "slot": 287654321, "health": 99},
                "PancakeSwap": {"status": "🟢 已连接", "latency": 120, "health": 95},
                "Meteora": {"status": "🟢 已连接", "latency": 89, "health": 97},
                "Ollama AI": {"status": "🟢 已连接", "model": "qwen2.5:3b", "health": 100}
            },
            "wallets": {
                "BSC主钱包": {
                    "address": "******************************************",
                    "balances": {
                        "BNB": {"amount": 2.45, "usd_value": 1666.50},
                        "USDC": {"amount": 1250.30, "usd_value": 1250.30},
                        "USDT": {"amount": 890.75, "usd_value": 890.75}
                    },
                    "total_usd": 3807.55,
                    "status": "🟢 活跃"
                },
                "Solana主钱包": {
                    "address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
                    "balances": {
                        "SOL": {"amount": 15.67, "usd_value": 2194.25},
                        "USDC": {"amount": 2100.50, "usd_value": 2100.50}
                    },
                    "total_usd": 4294.75,
                    "status": "🟢 活跃"
                }
            },
            "positions": {
                "BSC_BNB_USDC_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDC",
                    "pool_address": "0xabcdef1234567890abcdef1234567890abcdef12",
                    "liquidity_value": 8500.00,
                    "token0": {"symbol": "BNB", "amount": 12.5},
                    "token1": {"symbol": "USDC", "amount": 8500.0},
                    "entry_time": "2024-01-15 10:30:00",
                    "current_pnl": 320.50,
                    "pnl_percentage": 3.77,
                    "fees_earned": 45.20,
                    "impermanent_loss": -1.2,
                    "current_apr": 24.5,  # 当前APR
                    "status": "🟢 活跃",
                    "status_reason": "表现优异，盈利稳定",
                    "health": 95
                },
                "BSC_BNB_USDT_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDT",
                    "pool_address": "0xfedcba0987654321fedcba0987654321fedcba09",
                    "liquidity_value": 6200.00,
                    "token0": {"symbol": "BNB", "amount": 9.2},
                    "token1": {"symbol": "USDT", "amount": 6200.0},
                    "entry_time": "2024-01-16 14:20:00",
                    "current_pnl": -85.30,
                    "pnl_percentage": -1.38,
                    "fees_earned": 28.90,
                    "impermanent_loss": -0.8,
                    "current_apr": 21.2,  # 当前APR
                    "status": "🟡 监控",
                    "status_reason": "盈亏为负，需要密切关注",
                    "health": 88
                },
                "SOL_SOL_USDC_001": {
                    "chain": "Solana",
                    "pair": "SOL/USDC",
                    "pool_address": "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",
                    "liquidity_value": 12000.00,
                    "token0": {"symbol": "SOL", "amount": 85.5},
                    "token1": {"symbol": "USDC", "amount": 12000.0},
                    "entry_time": "2024-01-17 09:15:00",
                    "current_pnl": 480.75,
                    "pnl_percentage": 4.01,
                    "fees_earned": 67.40,
                    "impermanent_loss": -0.5,
                    "current_apr": 32.8,  # 当前APR
                    "status": "🟢 活跃",
                    "status_reason": "表现最佳，强烈推荐继续持有",
                    "health": 98
                }
            },
            "portfolio": {
                "total_value": 34802.30,
                "total_lp_value": 26700.00,
                "total_cash_value": 8102.30,
                "total_pnl": 715.95,
                "total_pnl_percentage": 2.74,
                "total_fees": 141.50,
                "total_il": -2.5,
                "active_positions": 3,
                "risk_score": 25.5,
                "risk_level": "🟡 中等"
            },
            "system": {
                "uptime": "2d 14h 32m",
                "last_rebalance": "2024-01-18 08:00:00",
                "monitoring_status": "🟢 正常",
                "auto_trading": "🟢 启用",
                "risk_monitoring": "🟢 启用",
                "alerts_count": 0
            },
            "available_pools": {
                "BSC_BNB_USDC_V3_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDC",
                    "protocol": "PancakeSwap V3",
                    "pool_address": "0x36696169c63e42cd08ce11f5deebbcebae652050",
                    "tvl": 2500000.00,
                    "volume_24h": 850000.00,
                    "fees_24h": 2550.00,
                    "apr": 24.5,
                    "apy": 27.8,
                    "fee_tier": 0.25,
                    "price_range": {"min": 650, "max": 710},
                    "liquidity_depth": 95.2,
                    "volatility": 18.5,
                    "risk_score": 25,
                    "recommendation": "BUY",
                    "last_updated": "2024-01-18 19:50:30"
                },
                "BSC_BNB_USDT_V3_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDT",
                    "protocol": "PancakeSwap V3",
                    "pool_address": "0x92b7807bf19b7dddf89b706143896d05228f3121",
                    "tvl": 1800000.00,
                    "volume_24h": 620000.00,
                    "fees_24h": 1860.00,
                    "apr": 21.2,
                    "apy": 23.6,
                    "fee_tier": 0.25,
                    "price_range": {"min": 645, "max": 705},
                    "liquidity_depth": 88.7,
                    "volatility": 19.2,
                    "risk_score": 28,
                    "recommendation": "HOLD",
                    "last_updated": "2024-01-18 19:50:30"
                },
                "SOL_SOL_USDC_DLMM_001": {
                    "chain": "Solana",
                    "pair": "SOL/USDC",
                    "protocol": "Meteora DLMM",
                    "pool_address": "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",
                    "tvl": 3200000.00,
                    "volume_24h": 1200000.00,
                    "fees_24h": 3600.00,
                    "apr": 32.8,
                    "apy": 38.9,
                    "fee_tier": 0.30,
                    "price_range": {"min": 130, "max": 150},
                    "liquidity_depth": 98.5,
                    "volatility": 22.1,
                    "risk_score": 18,
                    "recommendation": "BUY",
                    "last_updated": "2024-01-18 19:50:30"
                },
                "BSC_BNB_USDC_V3_002": {
                    "chain": "BSC",
                    "pair": "BNB/USDC",
                    "protocol": "PancakeSwap V3",
                    "pool_address": "0x172fcd41e0913e95784454622d1c3724f546f849",
                    "tvl": 980000.00,
                    "volume_24h": 320000.00,
                    "fees_24h": 960.00,
                    "apr": 18.7,
                    "apy": 20.5,
                    "fee_tier": 0.05,
                    "price_range": {"min": 660, "max": 700},
                    "liquidity_depth": 76.3,
                    "volatility": 16.8,
                    "risk_score": 32,
                    "recommendation": "HOLD",
                    "last_updated": "2024-01-18 19:50:30"
                },
                "SOL_SOL_USDC_DLMM_002": {
                    "chain": "Solana",
                    "pair": "SOL/USDC",
                    "protocol": "Meteora DLMM",
                    "pool_address": "9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E",
                    "tvl": 1650000.00,
                    "volume_24h": 580000.00,
                    "fees_24h": 1740.00,
                    "apr": 28.9,
                    "apy": 33.5,
                    "fee_tier": 0.30,
                    "price_range": {"min": 135, "max": 145},
                    "liquidity_depth": 82.1,
                    "volatility": 20.5,
                    "risk_score": 22,
                    "recommendation": "BUY",
                    "last_updated": "2024-01-18 19:50:30"
                }
            }
        }

    def create_header_panel(self):
        """创建头部面板"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        portfolio = self.system_data["portfolio"]
        
        header_content = f"""
[bold blue]🚀 DyFlow 实时监控Dashboard[/bold blue]                    [dim]{current_time}[/dim]

[green]💰 总资产: ${portfolio['total_value']:,.2f}[/green]  |  [blue]📊 LP价值: ${portfolio['total_lp_value']:,.2f}[/blue]  |  [yellow]💵 现金: ${portfolio['total_cash_value']:,.2f}[/yellow]  |  [magenta]📈 总盈亏: ${portfolio['total_pnl']:+,.2f} ({portfolio['total_pnl_percentage']:+.2f}%)[/magenta]
        """.strip()
        
        return Panel(
            header_content,
            box=box.DOUBLE,
            style="blue"
        )
    
    def create_connections_table(self):
        """创建连接状态表"""
        table = Table(title="🔗 系统连接状态", box=box.ROUNDED, show_header=True)
        table.add_column("服务", style="cyan", width=12)
        table.add_column("状态", justify="center", width=10)
        table.add_column("延迟", justify="right", width=8)
        table.add_column("健康度", justify="center", width=8)
        table.add_column("详情", style="dim", width=15)
        
        for service, info in self.system_data["connections"].items():
            health = info.get("health", 0)
            health_color = "green" if health >= 95 else "yellow" if health >= 85 else "red"
            health_bar = "█" * (health // 10) + "░" * (10 - health // 10)
            
            details = ""
            if "block" in info:
                details = f"#{info['block']}"
            elif "slot" in info:
                details = f"#{info['slot']}"
            elif "model" in info:
                details = info['model']
            
            table.add_row(
                service,
                info["status"],
                f"{info.get('latency', 0)}ms",
                Text(f"{health}%", style=health_color),
                details
            )
        
        return table
    
    def create_wallets_table(self):
        """创建钱包状态表"""
        table = Table(title="💼 钱包资产", box=box.ROUNDED)
        table.add_column("钱包", style="cyan", width=12)
        table.add_column("地址", style="yellow", width=12)
        table.add_column("主币", style="green", justify="right", width=12)
        table.add_column("USDC", style="blue", justify="right", width=12)
        table.add_column("其他", style="magenta", justify="right", width=12)
        table.add_column("总价值", style="bold green", justify="right", width=12)
        
        for wallet_name, wallet_info in self.system_data["wallets"].items():
            address = wallet_info["address"]
            short_address = f"{address[:6]}...{address[-4:]}"
            
            balances = wallet_info["balances"]
            if "BNB" in balances:
                main_coin = f"{balances['BNB']['amount']:.2f} BNB"
                other = f"{balances['USDT']['amount']:.2f} USDT"
            else:
                main_coin = f"{balances['SOL']['amount']:.2f} SOL"
                other = "-"
            
            table.add_row(
                wallet_name,
                short_address,
                main_coin,
                f"{balances['USDC']['amount']:.2f}",
                other,
                f"${wallet_info['total_usd']:,.2f}"
            )
        
        return table
    
    def create_positions_table(self):
        """创建持仓表"""
        table = Table(title="📊 LP持仓状态", box=box.ROUNDED)
        table.add_column("链", style="cyan", width=6)
        table.add_column("交易对", style="yellow", width=10)
        table.add_column("流动性价值", style="green", justify="right", width=10)
        table.add_column("APR", style="magenta", justify="right", width=8)
        table.add_column("盈亏", justify="right", width=10)
        table.add_column("手续费", style="blue", justify="right", width=8)
        table.add_column("无常损失", style="red", justify="right", width=8)
        table.add_column("状态", justify="center", width=10)
        
        for pos_id, pos_info in self.system_data["positions"].items():
            pnl = pos_info["current_pnl"]
            pnl_color = "green" if pnl >= 0 else "red"
            pnl_text = f"+${pnl:.2f}" if pnl >= 0 else f"${pnl:.2f}"

            il = pos_info["impermanent_loss"]
            il_color = "green" if il >= -1 else "yellow" if il >= -3 else "red"

            apr = pos_info["current_apr"]
            apr_color = "green" if apr >= 30 else "yellow" if apr >= 20 else "red"

            # 状态显示包含原因
            status_display = pos_info["status"]
            if "🟡" in status_display:
                status_display += f"\n{pos_info.get('status_reason', '')[:20]}..."

            table.add_row(
                pos_info["chain"],
                pos_info["pair"],
                f"${pos_info['liquidity_value']:,.0f}",
                Text(f"{apr:.1f}%", style=apr_color),
                Text(pnl_text, style=pnl_color),
                f"${pos_info['fees_earned']:.2f}",
                Text(f"{il:.1f}%", style=il_color),
                status_display
            )
        
        return table
    
    def create_portfolio_summary(self):
        """创建投资组合摘要"""
        portfolio = self.system_data["portfolio"]
        system = self.system_data["system"]
        
        summary_content = f"""
[bold]📈 投资组合摘要[/bold]

💰 总资产价值: [green]${portfolio['total_value']:,.2f}[/green]
📊 LP持仓价值: [blue]${portfolio['total_lp_value']:,.2f}[/blue]
💵 现金价值: [yellow]${portfolio['total_cash_value']:,.2f}[/yellow]

📈 总盈亏: [{'green' if portfolio['total_pnl'] >= 0 else 'red'}]${portfolio['total_pnl']:+,.2f} ({portfolio['total_pnl_percentage']:+.2f}%)[/{'green' if portfolio['total_pnl'] >= 0 else 'red'}]
💎 累计手续费: [green]${portfolio['total_fees']:,.2f}[/green]
⚠️ 无常损失: [red]{portfolio['total_il']:+.1f}%[/red]

🔄 活跃持仓: [blue]{portfolio['active_positions']}个[/blue]
🛡️ 风险评分: [yellow]{portfolio['risk_score']:.1f}/100[/yellow]
📊 风险等级: {portfolio['risk_level']}

⏰ 运行时间: [dim]{system['uptime']}[/dim]
🔄 上次重平衡: [dim]{system['last_rebalance']}[/dim]
        """.strip()
        
        return Panel(
            summary_content,
            title="📊 系统概览",
            box=box.ROUNDED,
            style="green"
        )
    
    def create_ai_chat_panel(self):
        """创建AI对话面板"""
        if not self.chat_history:
            chat_content = "[dim]💬 与AI助手对话 - 输入指令执行操作[/dim]\n\n"
            chat_content += "[yellow]💡 常用指令:[/yellow]\n"
            chat_content += "[blue]• 出清所有持仓[/blue]\n"
            chat_content += "[blue]• 查看BNB/USDC详情[/blue]\n"
            chat_content += "[blue]• 增加SOL/USDC持仓[/blue]\n"
            chat_content += "[blue]• 紧急停止[/blue]"
        else:
            chat_content = ""
            for msg in self.chat_history[-2:]:  # 显示最近2条
                if msg["role"] == "user":
                    chat_content += f"[bold blue]👤 你:[/bold blue] {msg['content']}\n\n"
                else:
                    chat_content += f"[bold green]🤖 AI:[/bold green] {msg['content'][:100]}...\n\n"
        
        return Panel(
            chat_content,
            title="🤖 AI助手",
            box=box.ROUNDED,
            style="blue"
        )

    def create_pool_scanner_panel(self):
        """创建池子扫描面板"""
        table = Table(title="🔍 可用池子扫描 (根据条件筛选)", box=box.ROUNDED, show_header=True)
        table.add_column("链", style="cyan", width=6)
        table.add_column("交易对", style="yellow", width=10)
        table.add_column("TVL", style="green", justify="right", width=10)
        table.add_column("24h交易量", style="blue", justify="right", width=10)
        table.add_column("APR", style="magenta", justify="right", width=8)
        table.add_column("APY", style="bright_magenta", justify="right", width=8)
        table.add_column("风险", justify="center", width=6)
        table.add_column("建议", justify="center", width=6)

        # 筛选条件：专注交易对 + 最小TVL + 最小交易量
        filtered_pools = []
        for pool_id, pool_info in self.system_data["available_pools"].items():
            # 检查是否为专注交易对
            pair = pool_info["pair"]
            if pair not in ["BNB/USDC", "BNB/USDT", "SOL/USDC"]:
                continue

            # 检查TVL和交易量条件
            if pool_info["tvl"] < 100000 or pool_info["volume_24h"] < 50000:
                continue

            filtered_pools.append(pool_info)

        # 按APR排序
        filtered_pools.sort(key=lambda x: x["apr"], reverse=True)

        for pool in filtered_pools[:6]:  # 显示前6个最佳池子
            # 风险等级颜色
            risk_score = pool["risk_score"]
            if risk_score <= 20:
                risk_level = "低"
                risk_color = "green"
            elif risk_score <= 35:
                risk_level = "中"
                risk_color = "yellow"
            else:
                risk_level = "高"
                risk_color = "red"

            # 建议颜色
            rec = pool["recommendation"]
            rec_color = {
                "BUY": "green",
                "HOLD": "yellow",
                "AVOID": "red"
            }.get(rec, "white")

            table.add_row(
                pool["chain"],
                pool["pair"],
                f"${pool['tvl']/1000000:.1f}M",
                f"${pool['volume_24h']/1000:.0f}K",
                f"{pool['apr']:.1f}%",
                f"{pool['apy']:.1f}%",
                Text(risk_level, style=risk_color),
                Text(rec, style=rec_color)
            )

        return table

    def create_dashboard_layout(self):
        """创建完整的Dashboard布局"""
        layout = Layout()
        
        # 主要布局
        layout.split_column(
            Layout(name="header", size=5),
            Layout(name="main"),
            Layout(name="footer", size=8)
        )
        
        # 主要区域分为左右两部分
        layout["main"].split_row(
            Layout(name="left", ratio=2),
            Layout(name="right", ratio=1)
        )
        
        # 左侧分为上中下三部分
        layout["left"].split_column(
            Layout(name="connections", ratio=1),
            Layout(name="wallets", ratio=1),
            Layout(name="positions", ratio=2)
        )
        
        # 右侧分为上中下三部分
        layout["right"].split_column(
            Layout(name="summary", ratio=1),
            Layout(name="pool_scanner", ratio=2),
            Layout(name="ai_chat", ratio=1)
        )
        
        # 填充所有内容
        layout["header"].update(self.create_header_panel())
        layout["connections"].update(self.create_connections_table())
        layout["wallets"].update(self.create_wallets_table())
        layout["positions"].update(self.create_positions_table())
        layout["summary"].update(self.create_portfolio_summary())
        layout["pool_scanner"].update(self.create_pool_scanner_panel())
        layout["ai_chat"].update(self.create_ai_chat_panel())
        
        # 底部状态栏
        system = self.system_data["system"]
        footer_content = f"""
[bold green]系统状态:[/bold green] {system['monitoring_status']} | [bold blue]自动交易:[/bold blue] {system['auto_trading']} | [bold yellow]风险监控:[/bold yellow] {system['risk_monitoring']} | [bold red]告警:[/bold red] {system['alerts_count']}个

[dim]💬 在下方输入AI指令 | 按Ctrl+C退出系统[/dim]
        """.strip()
        
        layout["footer"].update(Panel(
            footer_content,
            title="💡 系统状态 & AI指令输入",
            style="yellow"
        ))
        
        return layout
    
    def update_data(self):
        """更新实时数据"""
        # 模拟数据变化
        import random
        
        # 更新时间戳
        self.system_data["timestamp"] = datetime.now()
        
        # 更新连接延迟
        for service in self.system_data["connections"]:
            current_latency = self.system_data["connections"][service].get("latency", 50)
            self.system_data["connections"][service]["latency"] = max(20, current_latency + random.randint(-5, 5))
        
        # 更新持仓盈亏
        for pos_id in self.system_data["positions"]:
            pos = self.system_data["positions"][pos_id]
            change = random.uniform(-10, 15)  # 随机变化
            pos["current_pnl"] += change
            pos["pnl_percentage"] = (pos["current_pnl"] / pos["liquidity_value"]) * 100

            # 更新手续费
            pos["fees_earned"] += random.uniform(0, 0.5)

            # 更新APR
            apr_change = random.uniform(-1, 2)  # APR变化
            pos["current_apr"] = max(5, pos["current_apr"] + apr_change)
        
        # 重新计算投资组合总计
        total_pnl = sum(pos["current_pnl"] for pos in self.system_data["positions"].values())
        total_fees = sum(pos["fees_earned"] for pos in self.system_data["positions"].values())
        
        self.system_data["portfolio"]["total_pnl"] = total_pnl
        self.system_data["portfolio"]["total_fees"] = total_fees
        self.system_data["portfolio"]["total_pnl_percentage"] = (total_pnl / self.system_data["portfolio"]["total_lp_value"]) * 100

        # 更新可用池子数据
        for pool_id in self.system_data["available_pools"]:
            pool = self.system_data["available_pools"][pool_id]

            # 更新APR/APY (模拟市场变化)
            apr_change = random.uniform(-2, 3)
            pool["apr"] = max(5, pool["apr"] + apr_change)
            pool["apy"] = pool["apr"] * 1.15  # APY通常比APR高15%左右

            # 更新TVL和交易量
            tvl_change = random.uniform(-0.05, 0.08)  # -5%到+8%变化
            pool["tvl"] = max(50000, pool["tvl"] * (1 + tvl_change))

            volume_change = random.uniform(-0.1, 0.15)  # -10%到+15%变化
            pool["volume_24h"] = max(10000, pool["volume_24h"] * (1 + volume_change))

            # 更新24h手续费
            pool["fees_24h"] = pool["volume_24h"] * pool["fee_tier"] / 100

            # 更新风险评分
            risk_change = random.uniform(-2, 2)
            pool["risk_score"] = max(10, min(50, pool["risk_score"] + risk_change))

            # 根据APR和风险更新建议
            if pool["apr"] >= 30 and pool["risk_score"] <= 25:
                pool["recommendation"] = "BUY"
            elif pool["apr"] >= 20 and pool["risk_score"] <= 35:
                pool["recommendation"] = "HOLD"
            else:
                pool["recommendation"] = "HOLD"
    
    async def process_ai_command(self, command: str) -> str:
        """处理AI指令"""
        command_lower = command.lower()
        
        if '出清' in command_lower or '清仓' in command_lower:
            response = "✅ 正在执行出清操作...\n"
            response += "🔄 关闭BNB/USDC持仓 → 获得21.7 BNB\n"
            response += "🔄 关闭BNB/USDT持仓 → 获得9.2 BNB\n"
            response += "🔄 关闭SOL/USDC持仓 → 获得85.5 SOL\n"
            response += "✅ 出清完成！总计: 30.9 BNB + 85.5 SOL"
            
            # 清空持仓
            self.system_data["positions"] = {}
            self.system_data["portfolio"]["active_positions"] = 0
            
        elif 'bnb/usdc' in command_lower:
            response = "📊 BNB/USDC详情:\n"
            response += "💰 持仓价值: $8,500\n"
            response += "📈 盈亏: +$320.50 (+3.77%)\n"
            response += "💎 手续费: $45.20\n"
            response += "⚠️ 无常损失: -1.2%\n"
            response += "✅ 建议: 继续持有"

        elif '扫描' in command_lower or '池子' in command_lower:
            # 获取最佳池子
            best_pools = []
            for pool_id, pool_info in self.system_data["available_pools"].items():
                if pool_info["pair"] in ["BNB/USDC", "BNB/USDT", "SOL/USDC"]:
                    best_pools.append(pool_info)

            best_pools.sort(key=lambda x: x["apr"], reverse=True)

            response = "🔍 池子扫描结果:\n\n"
            for i, pool in enumerate(best_pools[:3], 1):
                response += f"{i}. {pool['pair']} ({pool['chain']})\n"
                response += f"   💰 TVL: ${pool['tvl']/1000000:.1f}M\n"
                response += f"   📈 APR: {pool['apr']:.1f}% | APY: {pool['apy']:.1f}%\n"
                response += f"   🔄 24h交易量: ${pool['volume_24h']/1000:.0f}K\n"
                response += f"   🛡️ 风险: {pool['risk_score']:.0f}/100\n"
                response += f"   💡 建议: {pool['recommendation']}\n\n"

        elif '最佳' in command_lower or '推荐' in command_lower:
            # 找到最佳APR的池子
            best_pool = None
            best_apr = 0
            for pool_info in self.system_data["available_pools"].values():
                if pool_info["pair"] in ["BNB/USDC", "BNB/USDT", "SOL/USDC"] and pool_info["apr"] > best_apr:
                    best_apr = pool_info["apr"]
                    best_pool = pool_info

            if best_pool:
                response = f"⭐ 最佳推荐池子: {best_pool['pair']} ({best_pool['chain']})\n\n"
                response += f"💰 TVL: ${best_pool['tvl']/1000000:.1f}M\n"
                response += f"📈 APR: {best_pool['apr']:.1f}% | APY: {best_pool['apy']:.1f}%\n"
                response += f"🔄 24h交易量: ${best_pool['volume_24h']/1000:.0f}K\n"
                response += f"💎 24h手续费: ${best_pool['fees_24h']:,.0f}\n"
                response += f"🛡️ 风险评分: {best_pool['risk_score']:.0f}/100\n"
                response += f"📊 流动性深度: {best_pool['liquidity_depth']:.1f}%\n"
                response += f"⚡ 波动率: {best_pool['volatility']:.1f}%\n"
                response += f"💡 建议: {best_pool['recommendation']}\n"
                response += f"🎯 价格范围: ${best_pool['price_range']['min']}-${best_pool['price_range']['max']}"
            else:
                response = "❌ 未找到符合条件的池子"

        else:
            response = "🤖 收到指令，正在处理..."
        
        return response
    
    async def run_dashboard(self):
        """运行Dashboard"""
        with Live(
            self.create_dashboard_layout(),
            refresh_per_second=2,
            console=self.console
        ) as live:
            
            # 启动输入线程
            def input_thread():
                while True:
                    try:
                        user_input = input()
                        self.user_input_queue.put(user_input)
                    except EOFError:
                        break
            
            input_thread_obj = threading.Thread(target=input_thread, daemon=True)
            input_thread_obj.start()
            
            while True:
                try:
                    # 更新数据
                    self.update_data()
                    
                    # 检查用户输入
                    try:
                        user_input = self.user_input_queue.get_nowait()
                        if user_input.lower() in ['quit', 'exit', '退出']:
                            break
                        
                        # 处理AI指令
                        response = await self.process_ai_command(user_input)
                        self.chat_history.append({"role": "user", "content": user_input})
                        self.chat_history.append({"role": "assistant", "content": response})
                        
                    except queue.Empty:
                        pass
                    
                    # 更新显示
                    live.update(self.create_dashboard_layout())
                    await asyncio.sleep(0.5)
                    
                except KeyboardInterrupt:
                    break

async def main():
    """主函数"""
    dashboard = DyFlowRealDashboard()
    console.print("[bold green]🚀 DyFlow Real Dashboard 启动中...[/bold green]")
    console.print("[yellow]💡 所有信息将在一屏显示，底部可输入AI指令[/yellow]")
    console.print()
    
    await dashboard.run_dashboard()
    
    console.print("\n[bold green]👋 感谢使用DyFlow Dashboard！[/bold green]")

if __name__ == "__main__":
    asyncio.run(main())
