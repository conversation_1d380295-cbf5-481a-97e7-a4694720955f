#!/usr/bin/env python3
"""
Agno Framework + DyFlow 成功案例测试
专注于验证核心功能的正常工作
"""

import asyncio
import sys
from datetime import datetime

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

async def test_agno_agent_basic():
    """测试Agno Agent基础功能"""
    print("🤖 测试Agno Agent基础功能...")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 创建Agent
        agent = Agent(
            name="DyFlowAgent",
            role="DeFi分析专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=[
                "你是DeFi分析专家。",
                "专注于LP池子分析和风险评估。",
                "提供简洁、专业的分析建议。"
            ],
            reasoning=False,
            show_tool_calls=False,
            markdown=True
        )
        
        print("✅ Agno Agent创建成功")
        
        # 测试DeFi相关问答
        questions = [
            "什么是流动性挖矿？请简要说明",
            "BSC和Solana链有什么区别？",
            "如何评估LP池子的风险？"
        ]
        
        for i, question in enumerate(questions, 1):
            response = agent.run(question)
            print(f"✅ 问题{i}回答成功: {response.content[:80]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Agno Agent测试失败: {e}")
        return False

async def test_multi_agent_collaboration():
    """测试多Agent协作"""
    print("\n🤝 测试多Agent协作...")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 创建数据收集Agent
        data_agent = Agent(
            name="DataCollector",
            role="数据收集专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=[
                "你负责收集和整理DeFi市场数据。",
                "提供准确、结构化的数据信息。"
            ],
            reasoning=False,
            show_tool_calls=False,
            markdown=True
        )
        
        # 创建分析Agent
        analysis_agent = Agent(
            name="Analyzer",
            role="数据分析专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=[
                "你负责分析DeFi数据并提供投资建议。",
                "基于数据给出专业的分析结论。"
            ],
            reasoning=False,
            show_tool_calls=False,
            markdown=True
        )
        
        print("✅ 多Agent创建成功")
        
        # 模拟协作流程
        # 步骤1: 数据收集
        data_prompt = "请收集PancakeSwap V3上主要LP池子的基本信息"
        data_result = data_agent.run(data_prompt)
        
        # 步骤2: 数据分析
        analysis_prompt = f"基于以下数据进行分析：{data_result.content[:300]}"
        analysis_result = analysis_agent.run(analysis_prompt)
        
        print("✅ 多Agent协作成功")
        print(f"   数据收集: {data_result.content[:60]}...")
        print(f"   分析结果: {analysis_result.content[:60]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 多Agent协作测试失败: {e}")
        return False

async def test_dyflow_workflow_simulation():
    """测试DyFlow工作流模拟"""
    print("\n🔄 测试DyFlow工作流模拟...")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 创建DyFlow核心Agents
        agents = {
            "scanner": Agent(
                name="PoolScanner",
                role="池子扫描专家",
                model=Ollama(id="qwen2.5:3b"),
                instructions=["扫描和发现优质LP池子", "提供池子基本信息"],
                reasoning=False,
                show_tool_calls=False,
                markdown=True
            ),
            "scorer": Agent(
                name="PoolScorer", 
                role="池子评分专家",
                model=Ollama(id="qwen2.5:3b"),
                instructions=["评估LP池子质量", "提供0-100分评分"],
                reasoning=False,
                show_tool_calls=False,
                markdown=True
            ),
            "risk_assessor": Agent(
                name="RiskAssessor",
                role="风险评估专家", 
                model=Ollama(id="qwen2.5:3b"),
                instructions=["评估投资风险", "提供风险等级和建议"],
                reasoning=False,
                show_tool_calls=False,
                markdown=True
            )
        }
        
        print("✅ DyFlow Agents创建成功")
        
        # 模拟工作流执行
        workflow_steps = [
            ("scanner", "扫描BSC链上TVL最高的5个LP池子"),
            ("scorer", "对发现的池子进行质量评分，重点关注TVL和交易量"),
            ("risk_assessor", "评估高分池子的投资风险，提供风险等级")
        ]
        
        workflow_results = {}
        
        for step_name, prompt in workflow_steps:
            result = agents[step_name].run(prompt)
            workflow_results[step_name] = result.content[:100]
            print(f"✅ {step_name}步骤完成: {result.content[:50]}...")
        
        # 生成最终报告
        final_agent = Agent(
            name="ReportGenerator",
            role="报告生成专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=["生成投资分析报告", "整合各步骤结果"],
            reasoning=False,
            show_tool_calls=False,
            markdown=True
        )
        
        report_prompt = f"""
        基于以下分析结果生成投资建议报告：
        
        池子扫描结果: {workflow_results['scanner']}
        质量评分结果: {workflow_results['scorer']}
        风险评估结果: {workflow_results['risk_assessor']}
        
        请提供最终投资建议。
        """
        
        final_report = final_agent.run(report_prompt)
        
        print("✅ DyFlow工作流模拟成功")
        print(f"   最终报告: {final_report.content[:80]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ DyFlow工作流模拟失败: {e}")
        return False

async def test_ollama_performance():
    """测试Ollama性能"""
    print("\n⚡ 测试Ollama性能...")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        import time
        
        agent = Agent(
            name="PerformanceTest",
            role="性能测试专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=["快速回答问题", "保持简洁"],
            reasoning=False,
            show_tool_calls=False,
            markdown=True
        )
        
        # 测试响应时间
        test_questions = [
            "什么是DeFi？",
            "解释流动性池",
            "BSC链的特点",
            "风险管理策略",
            "投资建议总结"
        ]
        
        total_time = 0
        successful_responses = 0
        
        for question in test_questions:
            start_time = time.time()
            try:
                response = agent.run(question)
                end_time = time.time()
                response_time = end_time - start_time
                total_time += response_time
                successful_responses += 1
                print(f"✅ 问题响应时间: {response_time:.2f}秒")
            except Exception as e:
                print(f"❌ 问题响应失败: {e}")
        
        if successful_responses > 0:
            avg_time = total_time / successful_responses
            print(f"✅ 平均响应时间: {avg_time:.2f}秒")
            print(f"✅ 成功率: {successful_responses}/{len(test_questions)} ({successful_responses/len(test_questions)*100:.1f}%)")
            return True
        else:
            return False
        
    except Exception as e:
        print(f"❌ Ollama性能测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 Agno Framework + DyFlow 成功案例测试")
    print("=" * 60)
    print("🎯 专注于验证核心功能的正常工作")
    print("=" * 60)
    
    test_results = {}
    
    # 1. 测试Agno Agent基础功能
    test_results['agno_agent_basic'] = await test_agno_agent_basic()
    
    # 2. 测试多Agent协作
    test_results['multi_agent_collaboration'] = await test_multi_agent_collaboration()
    
    # 3. 测试DyFlow工作流模拟
    test_results['dyflow_workflow_simulation'] = await test_dyflow_workflow_simulation()
    
    # 4. 测试Ollama性能
    test_results['ollama_performance'] = await test_ollama_performance()
    
    # 计算结果
    print("\n" + "=" * 60)
    print("📊 成功案例测试结果")
    print("=" * 60)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = passed_tests / total_tests * 100
    
    for test_name, result in test_results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总测试数: {total_tests}")
    print(f"成功: {passed_tests}")
    print(f"失败: {total_tests - passed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print("\n🎉 Agno Framework + DyFlow 集成成功！")
        print("💡 核心功能验证通过，系统准备就绪")
        print("🚀 可以开始构建完整的DyFlow系统")
        
        # 显示系统能力总结
        print("\n📋 已验证的系统能力:")
        print("   ✅ 本地AI推理 (Ollama Qwen2.5)")
        print("   ✅ 多Agent协作")
        print("   ✅ DeFi专业知识")
        print("   ✅ 工作流程模拟")
        print("   ✅ 性能稳定性")
        
        return True
    else:
        print("\n⚠️  部分功能需要优化")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
