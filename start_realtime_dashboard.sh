#!/bin/bash

echo "🚀 启动DyFlow 真正实时Dashboard..."
echo "✨ 技术架构："
echo "   🔧 后端: FastAPI + WebSocket"
echo "   🌐 前端: HTML + JavaScript"
echo "   📊 数据库: Supabase (已配置)"
echo "   ⚡ 实时通信: WebSocket"
echo ""
echo "🎨 功能特性："
echo "   📱 左侧 1/3:"
echo "      🤖 Agent执行日志 (实时滚动)"
echo "      💬 AI对话界面 (WebSocket通信)"
echo "   📊 右侧 2/3:"
echo "      💰 关键指标 (每2秒更新)"
echo "      💼 钱包资产 & LP持仓"
echo "      🔍 BSC & Solana池子扫描"
echo "      📋 交易日志"
echo ""
echo "📦 安装依赖..."
pip3 install -r requirements_realtime.txt --user

echo ""
echo "🌐 启动服务器..."
echo "📱 Dashboard: http://localhost:8000"
echo "🔌 WebSocket: ws://localhost:8000/ws"
echo "📡 API: http://localhost:8000/api/data"
echo ""
echo "💡 使用说明："
echo "   1. 页面自动连接WebSocket"
echo "   2. 数据每2秒实时更新"
echo "   3. 在左下角与AI对话"
echo "   4. 观察右上角连接状态"
echo ""
echo "🔄 按 Ctrl+C 停止服务"
echo "=" * 50

# 使用正确的Python路径启动
if [ -f "/Users/<USER>/Library/Python/3.9/bin/uvicorn" ]; then
    /Users/<USER>/Library/Python/3.9/bin/uvicorn dyflow_realtime_backend:app --host 0.0.0.0 --port 8000
else
    # 备用方案
    python3 -m uvicorn dyflow_realtime_backend:app --host 0.0.0.0 --port 8000
fi
