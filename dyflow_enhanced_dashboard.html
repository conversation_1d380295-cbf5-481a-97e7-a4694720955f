<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DyFlow 真实数据Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }
        
        .header {
            background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .status-bar {
            background: rgba(255,255,255,0.95);
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 120px);
            gap: 10px;
            padding: 10px;
        }
        
        .left-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .right-panel {
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .panel {
            background: rgba(255,255,255,0.95);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .panel h3 {
            margin-bottom: 10px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .agent-logs {
            flex: 1;
            overflow-y: auto;
            max-height: 300px;
        }
        
        .log-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            font-size: 12px;
        }
        
        .log-item.success { border-left-color: #28a745; }
        .log-item.warning { border-left-color: #ffc107; }
        .log-item.error { border-left-color: #dc3545; }
        .log-item.info { border-left-color: #17a2b8; }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            max-height: 250px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: #f8f9fa;
        }
        
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .message.ai {
            background: #e9ecef;
            color: #333;
        }
        
        .chat-input {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .chat-input button {
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 11px;
            opacity: 0.9;
        }
        
        .pools-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            flex: 1;
        }
        
        .pool-panel {
            display: flex;
            flex-direction: column;
        }
        
        .pool-table-container {
            flex: 1;
            overflow-y: auto;
            max-height: 400px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }
        
        .data-table th,
        .data-table td {
            padding: 6px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .data-table tr:hover {
            background: #f5f5f5;
            cursor: pointer;
        }
        
        .trade-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
        }
        
        .trade-button:hover {
            background: #218838;
        }
        
        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .connected {
            background: #28a745;
            color: white;
        }
        
        .disconnected {
            background: #dc3545;
            color: white;
        }
        
        .update-indicator {
            position: fixed;
            top: 50px;
            right: 10px;
            padding: 5px 10px;
            background: #17a2b8;
            color: white;
            border-radius: 15px;
            font-size: 12px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .update-indicator.show {
            opacity: 1;
        }
        
        .real-data-badge {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            margin-left: 5px;
        }
        
        .price-change {
            font-size: 10px;
            margin-left: 5px;
        }
        
        .price-change.positive {
            color: #28a745;
        }
        
        .price-change.negative {
            color: #dc3545;
        }
        
        .trade-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .trade-modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 10px;
            width: 400px;
            max-width: 90%;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">🔴 连接中...</div>
    <div class="update-indicator" id="updateIndicator">📊 真实数据更新中...</div>
    
    <div class="header">
        <h1>🚀 DyFlow 真实数据Dashboard</h1>
        <p>真实API数据 | 3-5秒更新 | AI交易助手 | 投资组合管理</p>
    </div>
    
    <div class="status-bar">
        <div class="status-item">
            <div class="status-indicator"></div>
            <span>真实数据源</span>
            <span class="real-data-badge">LIVE</span>
        </div>
        <div class="status-item">
            <span>🔗 PancakeSwap: ✅</span>
        </div>
        <div class="status-item">
            <span>🔗 Solana DEX: ✅</span>
        </div>
        <div class="status-item">
            <span>🤖 交易助手: ✅</span>
        </div>
        <div class="status-item">
            <span id="currentTime">🕒 --:--:--</span>
        </div>
        <div class="status-item">
            <span id="updateCount">🔄 更新: 0次</span>
        </div>
    </div>
    
    <div class="main-container">
        <!-- 左侧面板: 1/3 -->
        <div class="left-panel">
            <!-- Agent日志 -->
            <div class="panel agent-logs">
                <h3>🤖 Agent执行日志</h3>
                <div id="agentLogs">
                    <div class="log-item info">
                        <strong>系统启动</strong> | 连接真实数据源...
                    </div>
                </div>
            </div>
            
            <!-- AI聊天 -->
            <div class="panel chat-container">
                <h3>💬 AI交易助手</h3>
                <div class="chat-messages" id="chatMessages">
                    <div class="message ai">
                        🤖 DyFlow交易助手已上线！我可以帮你：<br>
                        • 分析真实池子数据<br>
                        • 执行LP投资交易<br>
                        • 管理投资组合<br>
                        • 风险评估和建议<br><br>
                        试试说："扫描最佳池子" 或 "我想投资BNB/USDC"
                    </div>
                </div>
                <div class="chat-input">
                    <input type="text" id="chatInput" placeholder="输入指令，如：我想投资BNB/USDC">
                    <button onclick="sendMessage()">发送</button>
                </div>
            </div>
        </div>
        
        <!-- 右侧面板: 2/3 -->
        <div class="right-panel">
            <!-- 价格指标 -->
            <div class="panel">
                <h3>💰 实时价格 <span class="real-data-badge">真实数据</span></h3>
                <div class="metrics-grid" id="pricesGrid">
                    <div class="metric-card">
                        <div class="metric-value" id="bnbPrice">$0</div>
                        <div class="metric-label">🟡 BNB</div>
                        <div class="price-change" id="bnbChange">0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="solPrice">$0</div>
                        <div class="metric-label">🟣 SOL</div>
                        <div class="price-change" id="solChange">0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="usdcPrice">$1.00</div>
                        <div class="metric-label">💵 USDC</div>
                        <div class="price-change" id="usdcChange">0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="usdtPrice">$1.00</div>
                        <div class="metric-label">💵 USDT</div>
                        <div class="price-change" id="usdtChange">0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="usd1Price">$0.998</div>
                        <div class="metric-label">💵 USD1</div>
                        <div class="price-change">-0.2%</div>
                    </div>
                </div>
            </div>
            
            <!-- 池子扫描 -->
            <div class="panel pools-container">
                <div class="pool-panel">
                    <h4>🟡 BSC池子 (真实数据) <span class="real-data-badge">PancakeSwap</span></h4>
                    <div class="pool-table-container">
                        <table class="data-table" id="bscPoolsTable">
                            <thead>
                                <tr>
                                    <th>交易对</th>
                                    <th>协议</th>
                                    <th>APR</th>
                                    <th>TVL</th>
                                    <th>24h量</th>
                                    <th>风险</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="pool-panel">
                    <h4>🟣 Solana池子 (增强数据) <span class="real-data-badge">多DEX</span></h4>
                    <div class="pool-table-container">
                        <table class="data-table" id="solanaPoolsTable">
                            <thead>
                                <tr>
                                    <th>交易对</th>
                                    <th>协议</th>
                                    <th>APR</th>
                                    <th>TVL</th>
                                    <th>24h量</th>
                                    <th>风险</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 交易模态框 -->
    <div id="tradeModal" class="trade-modal">
        <div class="trade-modal-content">
            <span class="close" onclick="closeTradeModal()">&times;</span>
            <h3>🚀 执行LP投资</h3>
            <div id="tradeDetails"></div>
            <div style="margin-top: 20px;">
                <label>投资金额 (USD):</label>
                <input type="number" id="investAmount" placeholder="1000" style="width: 100%; padding: 8px; margin: 5px 0;">
                <button onclick="executeTradeAction()" style="width: 100%; padding: 10px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    确认投资
                </button>
            </div>
        </div>
    </div>

    <script>
        // WebSocket连接
        let ws;
        let updateCount = 0;
        let currentPoolData = {};
        let selectedPool = null;
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                console.log('WebSocket连接已建立');
                document.getElementById('connectionStatus').textContent = '🟢 真实数据已连接';
                document.getElementById('connectionStatus').className = 'connection-status connected';
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                
                if (data.type === 'real_data_update') {
                    updateDashboard(data);
                } else if (data.type === 'chat_response') {
                    addChatMessage('ai', data.ai_response);
                } else if (data.type === 'trade_result') {
                    handleTradeResult(data.result);
                }
            };
            
            ws.onclose = function(event) {
                console.log('WebSocket连接已关闭');
                document.getElementById('connectionStatus').textContent = '🔴 连接断开';
                document.getElementById('connectionStatus').className = 'connection-status disconnected';
                
                // 3秒后重连
                setTimeout(connectWebSocket, 3000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }
        
        function updateDashboard(data) {
            updateCount++;
            
            // 显示更新指示器
            const indicator = document.getElementById('updateIndicator');
            indicator.classList.add('show');
            setTimeout(() => indicator.classList.remove('show'), 1000);
            
            // 更新时间和计数
            document.getElementById('currentTime').textContent = `🕒 ${new Date().toLocaleTimeString()}`;
            document.getElementById('updateCount').textContent = `🔄 更新: ${updateCount}次`;
            
            // 更新价格
            updatePrices(data.prices);
            
            // 更新池子数据
            updatePools(data.bsc_pools, 'bscPoolsTable', 'BSC');
            updatePools(data.solana_pools, 'solanaPoolsTable', 'Solana');
            
            // 保存当前数据
            currentPoolData = data;
            
            // 更新Agent日志
            updateAgentLogs();
        }
        
        function updatePrices(prices) {
            if (!prices) return;
            
            document.getElementById('bnbPrice').textContent = `$${prices.BNB?.toFixed(2) || '0'}`;
            document.getElementById('solPrice').textContent = `$${prices.SOL?.toFixed(2) || '0'}`;
            document.getElementById('usdcPrice').textContent = `$${prices.USDC?.toFixed(3) || '1.000'}`;
            document.getElementById('usdtPrice').textContent = `$${prices.USDT?.toFixed(3) || '1.000'}`;
            
            // 更新价格变化
            if (prices.changes) {
                updatePriceChange('bnbChange', prices.changes.BNB);
                updatePriceChange('solChange', prices.changes.SOL);
                updatePriceChange('usdcChange', prices.changes.USDC);
                updatePriceChange('usdtChange', prices.changes.USDT);
            }
        }
        
        function updatePriceChange(elementId, change) {
            const element = document.getElementById(elementId);
            if (element && change !== undefined) {
                element.textContent = `${change > 0 ? '+' : ''}${change.toFixed(2)}%`;
                element.className = `price-change ${change >= 0 ? 'positive' : 'negative'}`;
            }
        }
        
        function updatePools(pools, tableId, chain) {
            const tbody = document.querySelector(`#${tableId} tbody`);
            tbody.innerHTML = '';
            
            if (!pools) return;
            
            pools.slice(0, 20).forEach((pool, index) => {
                const row = tbody.insertRow();
                const riskColor = pool.risk_level === '低' ? 'green' : pool.risk_level === '中' ? 'orange' : 'red';
                
                row.innerHTML = `
                    <td>${pool.pair}</td>
                    <td>${pool.protocol}</td>
                    <td><strong>${pool.apr}%</strong></td>
                    <td>$${pool.tvl}M</td>
                    <td>$${pool.volume_24h}K</td>
                    <td style="color: ${riskColor}">${pool.risk_level}</td>
                    <td><button class="trade-button" onclick="openTradeModal('${chain}', ${index})">投资</button></td>
                `;
                
                row.onclick = function() {
                    showPoolDetails(pool, chain);
                };
            });
        }
        
        function updateAgentLogs() {
            const container = document.getElementById('agentLogs');
            const logs = [
                { time: new Date().toLocaleTimeString().slice(0, 5), level: 'success', agent: 'Data Provider', message: '成功获取真实池子数据' },
                { time: new Date().toLocaleTimeString().slice(0, 5), level: 'info', agent: 'Price Monitor', message: '价格数据已更新' },
                { time: new Date().toLocaleTimeString().slice(0, 5), level: 'success', agent: 'Pool Scanner', message: 'PancakeSwap数据同步完成' }
            ];
            
            container.innerHTML = '';
            logs.forEach(log => {
                const div = document.createElement('div');
                div.className = `log-item ${log.level}`;
                div.innerHTML = `
                    <strong>${log.time}</strong> | ${log.level.toUpperCase()} | ${log.agent}<br>
                    <small>${log.message}</small>
                `;
                container.appendChild(div);
            });
        }
        
        function openTradeModal(chain, poolIndex) {
            const pools = chain === 'BSC' ? currentPoolData.bsc_pools : currentPoolData.solana_pools;
            const pool = pools[poolIndex];
            
            selectedPool = { ...pool, chain };
            
            document.getElementById('tradeDetails').innerHTML = `
                <h4>📊 池子详情</h4>
                <p><strong>链:</strong> ${chain}</p>
                <p><strong>交易对:</strong> ${pool.pair}</p>
                <p><strong>协议:</strong> ${pool.protocol}</p>
                <p><strong>APR:</strong> ${pool.apr}%</p>
                <p><strong>TVL:</strong> $${pool.tvl}M</p>
                <p><strong>风险等级:</strong> ${pool.risk_level}</p>
                <p><strong>建议:</strong> ${pool.recommendation}</p>
            `;
            
            document.getElementById('tradeModal').style.display = 'block';
        }
        
        function closeTradeModal() {
            document.getElementById('tradeModal').style.display = 'none';
            selectedPool = null;
        }
        
        function executeTradeAction() {
            if (!selectedPool) return;
            
            const amount = document.getElementById('investAmount').value;
            if (!amount || amount <= 0) {
                alert('请输入有效的投资金额');
                return;
            }
            
            const tradeData = {
                pool: selectedPool,
                amount: parseFloat(amount),
                action: 'add_liquidity',
                timestamp: new Date().toISOString()
            };
            
            // 发送交易请求
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'execute_trade',
                    trade_data: tradeData
                }));
                
                addChatMessage('user', `投资 $${amount} 到 ${selectedPool.pair} (${selectedPool.chain})`);
                closeTradeModal();
            }
        }
        
        function handleTradeResult(result) {
            if (result.success) {
                addChatMessage('ai', `✅ 交易成功！\n交易哈希: ${result.tx_hash}\n预估Gas: ${result.estimated_gas}\n状态: ${result.status}`);
            } else {
                addChatMessage('ai', `❌ 交易失败: ${result.error}`);
            }
        }
        
        function showPoolDetails(pool, chain) {
            const message = `📊 ${pool.pair} (${chain}) 详情:\nAPR: ${pool.apr}%\nTVL: $${pool.tvl}M\n24h交易量: $${pool.volume_24h}K\n风险等级: ${pool.risk_level}`;
            addChatMessage('ai', message);
        }
        
        function addChatMessage(role, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.innerHTML = content.replace(/\n/g, '<br>');
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (message && ws && ws.readyState === WebSocket.OPEN) {
                addChatMessage('user', message);
                
                ws.send(JSON.stringify({
                    type: 'chat_message',
                    message: message
                }));
                
                input.value = '';
            }
        }
        
        // 回车发送消息
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('tradeModal');
            if (event.target === modal) {
                closeTradeModal();
            }
        }
        
        // 页面加载时连接WebSocket
        window.onload = function() {
            connectWebSocket();
        };
    </script>
</body>
</html>
