#!/usr/bin/env python3
"""
DyFlow WebUI Dashboard - 基于Streamlit的Web界面
提供完整的LP策略监控和池子扫描功能
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import random
import time
import asyncio
from typing import Dict, List

# 页面配置
st.set_page_config(
    page_title="DyFlow - LP策略监控Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

class DyFlowWebUI:
    """DyFlow WebUI Dashboard"""
    
    def __init__(self):
        self.initialize_session_state()
        self.generate_mock_data()
    
    def initialize_session_state(self):
        """初始化会话状态"""
        if 'last_update' not in st.session_state:
            st.session_state.last_update = datetime.now()
        if 'chat_history' not in st.session_state:
            st.session_state.chat_history = []
        if 'auto_refresh' not in st.session_state:
            st.session_state.auto_refresh = True
    
    def generate_bsc_pools(self) -> List[Dict]:
        """生成BSC池子数据"""
        pools = []
        pairs = ["BNB/USDC", "BNB/USDT", "BNB/USD1"]
        protocols = ["PancakeSwap V3", "PancakeSwap V2", "Uniswap V3", "SushiSwap"]
        
        for i in range(25):
            pair = random.choice(pairs)
            protocol = random.choice(protocols)
            
            # 生成不同风险等级的池子
            if i < 8:  # 低风险
                risk_score = random.randint(10, 20)
                apr = random.uniform(15, 30)
                tvl = random.uniform(1000000, 5000000)
                volume = random.uniform(500000, 2000000)
                recommendation = "BUY"
                risk_level = "低"
            elif i < 17:  # 中风险
                risk_score = random.randint(21, 35)
                apr = random.uniform(25, 45)
                tvl = random.uniform(500000, 2000000)
                volume = random.uniform(200000, 1000000)
                recommendation = random.choice(["BUY", "HOLD"])
                risk_level = "中"
            else:  # 高风险
                risk_score = random.randint(36, 50)
                apr = random.uniform(40, 80)
                tvl = random.uniform(100000, 800000)
                volume = random.uniform(50000, 500000)
                recommendation = random.choice(["HOLD", "AVOID"])
                risk_level = "高"
            
            pools.append({
                "链": "BSC",
                "交易对": pair,
                "协议": protocol,
                "TVL": tvl,
                "24h交易量": volume,
                "APR": apr,
                "APY": apr * 1.15,
                "手续费率": random.choice([0.05, 0.25, 0.30, 1.0]),
                "风险评分": risk_score,
                "风险等级": risk_level,
                "建议": recommendation
            })
        
        return sorted(pools, key=lambda x: x["APR"], reverse=True)
    
    def generate_solana_pools(self) -> List[Dict]:
        """生成Solana池子数据"""
        pools = []
        protocols = ["Meteora DLMM", "Orca Whirlpool", "Raydium CLMM", "Lifinity"]
        
        for i in range(25):
            protocol = random.choice(protocols)
            
            # 生成不同风险等级的池子
            if i < 8:  # 低风险
                risk_score = random.randint(10, 20)
                apr = random.uniform(20, 35)
                tvl = random.uniform(1500000, 6000000)
                volume = random.uniform(800000, 3000000)
                recommendation = "BUY"
                risk_level = "低"
            elif i < 17:  # 中风险
                risk_score = random.randint(21, 35)
                apr = random.uniform(30, 55)
                tvl = random.uniform(600000, 2500000)
                volume = random.uniform(300000, 1500000)
                recommendation = random.choice(["BUY", "HOLD"])
                risk_level = "中"
            else:  # 高风险
                risk_score = random.randint(36, 50)
                apr = random.uniform(50, 100)
                tvl = random.uniform(150000, 1000000)
                volume = random.uniform(80000, 800000)
                recommendation = random.choice(["HOLD", "AVOID"])
                risk_level = "高"
            
            pools.append({
                "链": "Solana",
                "交易对": "SOL/USDC",
                "协议": protocol,
                "TVL": tvl,
                "24h交易量": volume,
                "APR": apr,
                "APY": apr * 1.15,
                "手续费率": random.choice([0.25, 0.30, 0.50]),
                "风险评分": risk_score,
                "风险等级": risk_level,
                "建议": recommendation
            })
        
        return sorted(pools, key=lambda x: x["APR"], reverse=True)
    
    def generate_mock_data(self):
        """生成模拟数据"""
        # 钱包数据
        self.wallets = {
            "BSC主钱包": {
                "地址": "******************************************",
                "BNB": 2.45,
                "USDC": 1250.30,
                "USDT": 890.75,
                "USD1": 650.20,
                "总价值": 4457.75
            },
            "Solana主钱包": {
                "地址": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
                "SOL": 15.67,
                "USDC": 2100.50,
                "总价值": 4294.75
            }
        }
        
        # LP持仓数据
        self.positions = [
            {
                "链": "BSC",
                "交易对": "BNB/USDC",
                "流动性价值": 8500.00,
                "APR": 24.5 + random.uniform(-2, 3),
                "盈亏": 320.50 + random.uniform(-20, 30),
                "手续费": 45.20 + random.uniform(0, 5),
                "状态": "🟢 活跃",
                "持仓时间": "515天"
            },
            {
                "链": "BSC", 
                "交易对": "BNB/USDT",
                "流动性价值": 6200.00,
                "APR": 21.2 + random.uniform(-2, 3),
                "盈亏": -85.30 + random.uniform(-10, 20),
                "手续费": 28.90 + random.uniform(0, 3),
                "状态": "🟡 监控",
                "持仓时间": "514天"
            },
            {
                "链": "Solana",
                "交易对": "SOL/USDC", 
                "流动性价值": 12000.00,
                "APR": 32.8 + random.uniform(-2, 5),
                "盈亏": 480.75 + random.uniform(-30, 50),
                "手续费": 67.40 + random.uniform(0, 8),
                "状态": "🟢 活跃",
                "持仓时间": "513天"
            }
        ]
        
        # 交易日志
        self.trading_logs = [
            {"时间": "20:24", "操作": "调整范围", "交易对": "BNB/USDC", "金额": "$2,306", "状态": "⏳ 处理中"},
            {"时间": "19:45", "操作": "开仓", "交易对": "SOL/USDC", "金额": "$12,000", "状态": "✅ 成功"},
            {"时间": "18:30", "操作": "增加流动性", "交易对": "BNB/USDC", "金额": "$2,500", "状态": "✅ 成功"},
            {"时间": "17:22", "操作": "收取手续费", "交易对": "BNB/USDT", "金额": "$45.20", "状态": "✅ 成功"},
            {"时间": "16:15", "操作": "调整范围", "交易对": "SOL/USDC", "金额": "$8,500", "状态": "✅ 成功"},
        ]
        
        # Agent日志
        self.agent_logs = [
            {"时间": "20:24", "等级": "✅ SUCCESS", "Agent": "Yield Optimizer", "消息": "成功优化价格范围，APR提升2%"},
            {"时间": "20:24", "等级": "ℹ️ INFO", "Agent": "Risk Sentinel", "消息": "持仓风险评估完成，所有持仓在安全范围内"},
            {"时间": "20:24", "等级": "⚠️ WARNING", "Agent": "Pool Scanner", "消息": "检测到高APR池子，建议深度分析"},
            {"时间": "20:11", "等级": "✅ SUCCESS", "Agent": "LP Manager", "消息": "成功收取SOL/USDC池子手续费 $278"},
            {"时间": "20:11", "等级": "❌ ERROR", "Agent": "Transaction Manager", "消息": "BNB/USD1平仓交易失败，Gas费用不足"},
        ]
        
        # 生成池子数据
        self.bsc_pools = self.generate_bsc_pools()
        self.solana_pools = self.generate_solana_pools()
    
    def render_header(self):
        """渲染页面头部"""
        st.markdown("""
        <div style="background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
            <h1 style="color: white; text-align: center; margin: 0;">
                🚀 DyFlow 实时监控Dashboard
            </h1>
            <p style="color: #e0e0e0; text-align: center; margin: 10px 0 0 0;">
                24/7 自动化单边LP策略系统 - BSC & Solana
            </p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_metrics(self):
        """渲染关键指标"""
        total_wallet = sum(wallet["总价值"] for wallet in self.wallets.values())
        total_lp = sum(pos["流动性价值"] for pos in self.positions)
        total_pnl = sum(pos["盈亏"] for pos in self.positions)
        total_fees = sum(pos["手续费"] for pos in self.positions)
        total_value = total_wallet + total_lp
        
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            st.metric("💰 总资产", f"${total_value:,.2f}", f"+${total_pnl:,.2f}")
        with col2:
            st.metric("📊 LP价值", f"${total_lp:,.2f}", f"{(total_pnl/total_lp)*100:+.2f}%")
        with col3:
            st.metric("💵 钱包余额", f"${total_wallet:,.2f}")
        with col4:
            st.metric("📈 总盈亏", f"${total_pnl:+,.2f}", f"{(total_pnl/total_lp)*100:+.2f}%")
        with col5:
            st.metric("💎 手续费", f"${total_fees:,.2f}")
    
    def render_wallets(self):
        """渲染钱包信息"""
        st.subheader("💼 钱包资产")
        
        wallet_data = []
        for name, wallet in self.wallets.items():
            if "BNB" in wallet:
                wallet_data.append({
                    "钱包": name,
                    "地址": f"{wallet['地址'][:6]}...{wallet['地址'][-4:]}",
                    "主币": f"{wallet['BNB']:.2f} BNB",
                    "USDC": f"{wallet['USDC']:.2f}",
                    "USDT": f"{wallet.get('USDT', 0):.2f}",
                    "USD1": f"{wallet.get('USD1', 0):.2f}",
                    "总价值": f"${wallet['总价值']:,.2f}"
                })
            else:
                wallet_data.append({
                    "钱包": name,
                    "地址": f"{wallet['地址'][:6]}...{wallet['地址'][-4:]}",
                    "主币": f"{wallet['SOL']:.2f} SOL",
                    "USDC": f"{wallet['USDC']:.2f}",
                    "USDT": "-",
                    "USD1": "-",
                    "总价值": f"${wallet['总价值']:,.2f}"
                })
        
        df_wallets = pd.DataFrame(wallet_data)
        st.dataframe(df_wallets, use_container_width=True, hide_index=True)
    
    def render_positions(self):
        """渲染LP持仓"""
        st.subheader("📊 LP持仓状态")
        
        # 格式化持仓数据
        formatted_positions = []
        for pos in self.positions:
            formatted_positions.append({
                "链": pos["链"],
                "交易对": pos["交易对"],
                "流动性价值": f"${pos['流动性价值']:,.0f}",
                "APR": f"{pos['APR']:.1f}%",
                "盈亏": f"${pos['盈亏']:+,.2f}",
                "手续费": f"${pos['手续费']:.2f}",
                "状态": pos["状态"],
                "持仓时间": pos["持仓时间"]
            })
        
        df_positions = pd.DataFrame(formatted_positions)
        
        # 使用颜色编码
        def color_pnl(val):
            if "+" in val:
                return 'background-color: #d4edda; color: #155724'
            elif "-" in val:
                return 'background-color: #f8d7da; color: #721c24'
            return ''
        
        styled_df = df_positions.style.applymap(color_pnl, subset=['盈亏'])
        st.dataframe(styled_df, use_container_width=True, hide_index=True)
    
    def run(self):
        """运行WebUI"""
        self.render_header()
        
        # 侧边栏控制
        with st.sidebar:
            st.header("⚙️ 控制面板")
            
            # 自动刷新控制
            auto_refresh = st.checkbox("🔄 自动刷新", value=st.session_state.auto_refresh)
            st.session_state.auto_refresh = auto_refresh
            
            if auto_refresh:
                refresh_interval = st.slider("刷新间隔 (秒)", 1, 10, 3)
                time.sleep(refresh_interval)
                st.rerun()
            
            # 手动刷新按钮
            if st.button("🔄 立即刷新"):
                self.generate_mock_data()
                st.rerun()
            
            st.markdown("---")
            
            # 系统状态
            st.markdown("### 📡 系统状态")
            st.success("🟢 系统正常运行")
            st.info("🔗 BSC连接: ✅")
            st.info("🔗 Solana连接: ✅") 
            st.info("🤖 AI代理: ✅")
            
            st.markdown("---")
            
            # AI指令输入
            st.markdown("### 💬 AI指令")
            user_input = st.text_input("输入指令:", placeholder="例如: 扫描最佳池子")
            
            if st.button("发送") and user_input:
                # 处理AI指令
                if "扫描" in user_input or "最佳" in user_input:
                    response = "🔍 最佳池子扫描结果:\n\n"
                    all_pools = self.bsc_pools[:3] + self.solana_pools[:2]
                    for i, pool in enumerate(all_pools, 1):
                        response += f"{i}. {pool['交易对']} ({pool['链']})\n"
                        response += f"   📈 APR: {pool['APR']:.1f}%\n"
                        response += f"   💰 TVL: ${pool['TVL']/1000000:.1f}M\n\n"
                elif "出清" in user_input:
                    response = "✅ 正在执行出清操作...\n所有持仓将转换为主币"
                else:
                    response = "🤖 收到指令，正在处理..."
                
                st.session_state.chat_history.append({"user": user_input, "ai": response})
            
            # 显示聊天历史
            if st.session_state.chat_history:
                st.markdown("### 💭 对话历史")
                for chat in st.session_state.chat_history[-3:]:
                    st.markdown(f"**👤 你:** {chat['user']}")
                    st.markdown(f"**🤖 AI:** {chat['ai']}")
                    st.markdown("---")
        
        # 主要内容区域
        self.render_metrics()
        
        # 创建标签页
        tab1, tab2, tab3, tab4, tab5 = st.tabs(["💼 钱包 & 持仓", "🟡 BSC池子", "🟣 Solana池子", "📋 交易日志", "🤖 Agent日志"])
        
        with tab1:
            col1, col2 = st.columns([1, 2])
            with col1:
                self.render_wallets()
            with col2:
                self.render_positions()
        
        with tab2:
            st.subheader("🟡 BSC池子扫描 (25个池子 - 高中低风险)")
            df_bsc = pd.DataFrame(self.bsc_pools[:15])  # 显示前15个
            
            # 格式化显示
            df_bsc_display = df_bsc.copy()
            df_bsc_display["TVL"] = df_bsc_display["TVL"].apply(lambda x: f"${x/1000000:.1f}M")
            df_bsc_display["24h交易量"] = df_bsc_display["24h交易量"].apply(lambda x: f"${x/1000:.0f}K")
            df_bsc_display["APR"] = df_bsc_display["APR"].apply(lambda x: f"{x:.1f}%")
            df_bsc_display["APY"] = df_bsc_display["APY"].apply(lambda x: f"{x:.1f}%")
            df_bsc_display["手续费率"] = df_bsc_display["手续费率"].apply(lambda x: f"{x:.2f}%")
            
            # 选择显示的列
            display_cols = ["交易对", "协议", "TVL", "24h交易量", "APR", "风险等级", "建议"]
            st.dataframe(df_bsc_display[display_cols], use_container_width=True, hide_index=True)
        
        with tab3:
            st.subheader("🟣 Solana池子扫描 (25个池子 - 高中低风险)")
            df_sol = pd.DataFrame(self.solana_pools[:15])  # 显示前15个
            
            # 格式化显示
            df_sol_display = df_sol.copy()
            df_sol_display["TVL"] = df_sol_display["TVL"].apply(lambda x: f"${x/1000000:.1f}M")
            df_sol_display["24h交易量"] = df_sol_display["24h交易量"].apply(lambda x: f"${x/1000:.0f}K")
            df_sol_display["APR"] = df_sol_display["APR"].apply(lambda x: f"{x:.1f}%")
            df_sol_display["APY"] = df_sol_display["APY"].apply(lambda x: f"{x:.1f}%")
            df_sol_display["手续费率"] = df_sol_display["手续费率"].apply(lambda x: f"{x:.2f}%")
            
            # 选择显示的列
            display_cols = ["交易对", "协议", "TVL", "24h交易量", "APR", "风险等级", "建议"]
            st.dataframe(df_sol_display[display_cols], use_container_width=True, hide_index=True)
        
        with tab4:
            st.subheader("📋 交易日志 (最近执行记录)")
            df_trading = pd.DataFrame(self.trading_logs)
            st.dataframe(df_trading, use_container_width=True, hide_index=True)
        
        with tab5:
            st.subheader("🤖 Agent执行日志 (智能代理活动)")
            df_agent = pd.DataFrame(self.agent_logs)
            st.dataframe(df_agent, use_container_width=True, hide_index=True)
        
        # 页面底部信息
        st.markdown("---")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.markdown("**🕒 最后更新:** " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        with col2:
            st.markdown("**📊 支持的交易对:** BNB/USDC, BNB/USDT, BNB/USD1, SOL/USDC")
        with col3:
            st.markdown("**🔧 版本:** DyFlow WebUI v1.0")

def main():
    """主函数"""
    app = DyFlowWebUI()
    app.run()

if __name__ == "__main__":
    main()
