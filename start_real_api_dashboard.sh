#!/bin/bash

echo "🚀 启动DyFlow 真实API Dashboard..."
echo "✨ 使用多个真实数据源："
echo "   💰 价格数据:"
echo "      - CoinGecko API (主要)"
echo "      - CoinCap API (备用)"
echo "   🟡 BSC数据:"
echo "      - DeFiLlama API (协议TVL)"
echo "      - 1inch API (流动性源)"
echo "   🟣 Solana数据:"
echo "      - Solana Beach API"
echo "      - Birdeye API (代币数据)"
echo ""
echo "🎯 目标: 获取真实市场数据，而不是模拟数据"
echo "🔄 更新频率: 每5秒"
echo "📊 数据质量: 尽可能使用真实API"
echo ""
echo "🌐 启动真实API服务器..."
echo "📱 Dashboard: http://localhost:8002"
echo "🔌 WebSocket: ws://localhost:8002/ws"
echo "📡 真实数据API: http://localhost:8002/api/real-data"
echo ""
echo "🔄 按 Ctrl+C 停止服务"
echo "=" * 50

# 启动服务器
if [ -f "/Users/<USER>/Library/Python/3.9/bin/uvicorn" ]; then
    /Users/<USER>/Library/Python/3.9/bin/uvicorn dyflow_real_api_backend:app --host 0.0.0.0 --port 8002
else
    python3 -m uvicorn dyflow_real_api_backend:app --host 0.0.0.0 --port 8002
fi
