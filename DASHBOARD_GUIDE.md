# 🚀 DyFlow Live Dashboard 使用指南

## 📋 系统概述

DyFlow Live Dashboard 是一个**实时监控系统**，提供：
- 🔍 **实时系统状态监控** - 连接状态、钱包余额、持仓情况
- 💬 **AI对话界面** - 通过自然语言与系统交互
- ⚡ **即时执行** - AI理解指令并立即执行操作
- 🎯 **专注策略** - 专门针对BNB/USDC/USDT和SOL/USDC交易对

## 🖥️ Dashboard界面说明

### 主要区域

#### 1. 🔗 系统连接状态
- **BSC RPC** - BSC网络连接状态和延迟
- **Solana RPC** - Solana网络连接状态和延迟  
- **PancakeSwap** - PancakeSwap子图连接
- **Meteora** - Meteora API连接
- **AI Model** - 本地AI模型状态

#### 2. 💼 钱包状态
- **BSC钱包** - BNB、USDC、USDT余额
- **Solana钱包** - SOL、USDC余额
- **地址显示** - 钱包地址简化显示

#### 3. 📊 当前持仓
- **交易对** - BNB/USDC, BNB/USDT, SOL/USDC
- **持仓价值** - 当前LP持仓的USD价值
- **盈亏状态** - 实时盈亏和百分比
- **手续费收益** - 累计手续费收入

#### 4. 📈 系统指标
- **总资产价值** - 所有资产的总价值
- **总盈亏** - 未实现盈亏总计
- **累计手续费** - 总手续费收入
- **运行时间** - 系统连续运行时间

## 💬 AI对话指令

### 基本操作指令

#### 🔄 持仓管理
```
出清所有持仓          # 关闭所有LP，换回BNB/SOL
清仓                 # 同上
关闭BNB/USDC持仓      # 关闭特定持仓
关闭SOL/USDC持仓      # 关闭Solana持仓
```

#### 📊 查询指令
```
查看BNB/USDC状态      # 获取特定池子详情
查看系统状态         # 获取整体系统概况
查看概况            # 同上
```

#### 💰 开仓指令
```
增加SOL/USDC持仓      # 分析并建议开新仓
增加BNB/USDC持仓 $5000 # 指定金额开仓
开仓SOL/USDC         # 开新仓位
```

#### 🚨 紧急操作
```
紧急停止            # 立即关闭所有持仓
急停               # 同上
```

### AI回复示例

#### 出清操作回复
```
✅ 出清操作执行完成！

📋 执行结果:
• BNB/USDC → 获得 12.5 BNB
• BNB/USDT → 获得 9.2 BNB
• SOL/USDC → 获得 85.5 SOL

💰 总计获得: 21.7 BNB + 85.5 SOL
⛽ Gas费用: 0.005 BNB + 0.00001 SOL
```

#### 池子状态回复
```
📊 BNB/USDC 池子详情:

💰 当前持仓: $8,500
📈 盈亏: +$320.50 (+3.77%)
💎 手续费: $45.20
📅 持仓时间: 3天
🎯 价格范围: $650 - $710
✅ 建议: 继续持有，表现良好
```

## 🎮 使用方式

### 方法1: 对话模式 (推荐)
```bash
python dyflow_live_dashboard.py
```

**特点**:
- 💬 直接与AI对话
- ⚡ 即时执行指令
- 📊 实时状态显示
- 🔄 持续交互

**使用流程**:
1. 启动系统
2. 查看当前状态摘要
3. 输入指令与AI对话
4. AI执行操作并反馈结果

### 方法2: 实时Dashboard
在对话模式中输入 `dashboard` 启动实时面板

**特点**:
- 📊 全屏实时监控
- 🔄 自动数据更新
- 📈 可视化图表
- ⏰ 实时时间戳

## 🎯 典型使用场景

### 场景1: 日常监控
```bash
# 启动系统
python dyflow_live_dashboard.py

# 查看状态
💬 DyFlow AI: 查看系统状态

# 查看实时面板
💬 DyFlow AI: dashboard
```

### 场景2: 风险管理
```bash
# 检查特定池子
💬 DyFlow AI: 查看BNB/USDC状态

# 如果风险过高，出清持仓
💬 DyFlow AI: 出清所有持仓
```

### 场景3: 策略调整
```bash
# 分析新机会
💬 DyFlow AI: 增加SOL/USDC持仓

# 确认后执行
💬 DyFlow AI: 开仓SOL/USDC $5000
```

### 场景4: 紧急情况
```bash
# 市场异常时立即止损
💬 DyFlow AI: 紧急停止
```

## 🔧 系统特色

### ✅ 智能特性
- **自然语言理解** - AI理解中文指令
- **上下文记忆** - 记住对话历史
- **智能建议** - 基于市场分析提供建议
- **风险评估** - 实时风险监控和预警

### ✅ 专注策略
- **BSC专注** - BNB/USDC, BNB/USDT交易对
- **Solana专注** - SOL/USDC交易对
- **智能退出** - 确保退出为BNB或SOL
- **风险控制** - 自动止损和风险管理

### ✅ 实时监控
- **连接状态** - 实时网络连接监控
- **钱包余额** - 实时资产余额更新
- **持仓状态** - LP持仓实时盈亏
- **系统指标** - 综合系统健康度

## 🚀 立即开始

### 快速启动
```bash
# 启动Live Dashboard
python dyflow_live_dashboard.py

# 试试这些指令:
💬 DyFlow AI: 查看系统状态
💬 DyFlow AI: dashboard
💬 DyFlow AI: 查看BNB/USDC状态
```

### 进阶使用
```bash
# 模拟出清操作
💬 DyFlow AI: 出清所有持仓

# 查看执行结果
💬 DyFlow AI: 查看概况

# 分析新机会
💬 DyFlow AI: 增加SOL/USDC持仓
```

## 💡 使用技巧

### 1. 指令技巧
- 使用简洁明确的中文指令
- 可以包含具体金额，如"增加$5000"
- 支持模糊匹配，如"bnb usdc"

### 2. 监控技巧
- 定期查看dashboard了解整体状态
- 关注盈亏变化和手续费收益
- 监控连接状态确保系统正常

### 3. 风险管理
- 设置合理的止损点
- 定期检查持仓状态
- 市场异常时及时出清

## 🎉 开始体验

**现在就启动你的DyFlow Live Dashboard：**

```bash
python dyflow_live_dashboard.py
```

**第一次使用建议**:
1. 输入 `查看系统状态` 了解当前情况
2. 输入 `dashboard` 查看实时面板
3. 输入 `查看BNB/USDC状态` 了解具体持仓
4. 尝试 `出清所有持仓` 体验执行功能

**🚀 享受与AI对话管理你的DeFi投资组合的全新体验！**
