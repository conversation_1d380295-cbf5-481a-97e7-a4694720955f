#!/usr/bin/env python3
"""
DyFlow启动器 - 简化的用户界面和执行指南
提供友好的交互式界面来运行DyFlow系统
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any
import json

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

class DyFlowLauncher:
    """DyFlow启动器 - 提供友好的用户界面"""
    
    def __init__(self):
        self.session_id = f"dyflow_session_{int(datetime.now().timestamp())}"
        
    def show_welcome(self):
        """显示欢迎界面"""
        print("🚀 " + "="*60)
        print("🚀 DyFlow - 24/7 自动化LP策略系统")
        print("🚀 " + "="*60)
        print("📊 基于Agno Framework的智能DeFi投资系统")
        print("🤖 使用本地Ollama Qwen2.5模型")
        print("🔗 支持BSC (PancakeSwap V3) 和 Solana (Meteora DLMM)")
        print("💰 自动化流动性池监控、分析和风险管理")
        print("="*64)
        print()
    
    def show_strategy_info(self):
        """显示策略信息"""
        print("📋 当前策略配置:")
        print("   🎯 投资目标: 低浮动Meme币LP策略")
        print("   💵 最小TVL: $100,000")
        print("   📈 最小24h交易量: $50,000")
        print("   🛡️ 风险管理: 15%止损, 10%紧急退出")
        print("   ⚖️ 资金分配: 每池最大20%, 储备10%")
        print("   🔄 重平衡间隔: 24小时")
        print("   ⏰ 风险检查: 每5分钟")
        print()
    
    def show_menu(self):
        """显示主菜单"""
        print("🎮 请选择操作:")
        print("   1. 🔍 单次监控 (扫描和分析池子)")
        print("   2. 🔄 持续监控 (24/7自动运行)")
        print("   3. 📊 市场总结 (获取市场概况)")
        print("   4. 🧪 测试系统 (验证所有组件)")
        print("   5. ⚙️  查看配置 (显示详细配置)")
        print("   6. 📈 查看历史 (显示执行历史)")
        print("   7. 🆘 帮助文档 (使用说明)")
        print("   0. 🚪 退出系统")
        print()
    
    async def run_single_monitoring(self):
        """运行单次监控"""
        print("🔍 启动单次监控...")
        print("-" * 40)
        
        try:
            # 选择链
            print("请选择要监控的链:")
            print("1. BSC (PancakeSwap V3)")
            print("2. Solana (Meteora DLMM)")
            print("3. 两个链都监控")
            
            choice = input("请输入选择 (1-3): ").strip()
            
            if choice == "1":
                chains = ["bsc"]
            elif choice == "2":
                chains = ["solana"]
            elif choice == "3":
                chains = ["bsc", "solana"]
            else:
                print("❌ 无效选择，使用默认配置 (两个链)")
                chains = ["bsc", "solana"]
            
            # 选择池子数量
            max_pools = input("请输入每链最大池子数 (默认5): ").strip()
            max_pools = int(max_pools) if max_pools.isdigit() else 5
            
            print(f"\n🚀 开始监控 {', '.join(chains)} 链，每链最多 {max_pools} 个池子...")
            
            # 使用基于Agno的系统
            from dyflow_agno_system import DyFlowAgnoSystem
            
            dyflow = DyFlowAgnoSystem()
            await dyflow.initialize()
            
            result = await dyflow.run_monitoring_cycle(chains=chains, max_pools=max_pools)
            
            # 显示结果
            self.display_monitoring_result(result)
            
            return result
            
        except Exception as e:
            print(f"❌ 监控执行失败: {e}")
            return None
    
    async def run_continuous_monitoring(self):
        """运行持续监控"""
        print("🔄 启动持续监控...")
        print("-" * 40)
        
        try:
            duration = input("请输入监控时长(分钟，默认60): ").strip()
            duration = int(duration) if duration.isdigit() else 60
            
            interval = input("请输入检查间隔(分钟，默认5): ").strip()
            interval = int(interval) if interval.isdigit() else 5
            
            print(f"\n🔄 开始持续监控 {duration} 分钟，每 {interval} 分钟检查一次...")
            print("💡 按 Ctrl+C 可以随时停止")
            
            cycles = 0
            max_cycles = duration // interval
            
            while cycles < max_cycles:
                try:
                    print(f"\n📅 第 {cycles + 1}/{max_cycles} 轮监控 - {datetime.now().strftime('%H:%M:%S')}")
                    
                    # 执行监控
                    result = await self.run_single_monitoring()
                    
                    if result and result.get('status') == 'completed':
                        print("✅ 本轮监控完成")
                    else:
                        print("⚠️ 本轮监控有问题")
                    
                    cycles += 1
                    
                    if cycles < max_cycles:
                        print(f"⏰ 等待 {interval} 分钟后进行下一轮...")
                        await asyncio.sleep(interval * 60)
                    
                except KeyboardInterrupt:
                    print("\n🛑 用户中断，停止持续监控")
                    break
                except Exception as e:
                    print(f"❌ 监控周期失败: {e}")
                    cycles += 1
            
            print(f"\n🏁 持续监控完成，共执行 {cycles} 轮")
            
        except KeyboardInterrupt:
            print("\n🛑 用户中断，退出持续监控")
        except Exception as e:
            print(f"❌ 持续监控失败: {e}")
    
    async def get_market_summary(self):
        """获取市场总结"""
        print("📊 获取市场总结...")
        print("-" * 40)
        
        try:
            from dyflow_agno_system import DyFlowAgnoSystem
            
            dyflow = DyFlowAgnoSystem()
            await dyflow.initialize()
            
            # 获取快速市场概况
            result = await dyflow.run_monitoring_cycle(chains=["bsc", "solana"], max_pools=3)
            
            if result and result.get('status') == 'completed':
                print("📈 市场总结:")
                results = result.get('results', {})
                
                for chain, data in results.get('data_collection', {}).items():
                    if 'error' not in data:
                        print(f"   {chain.upper()}: {data.get('pools_count', 0)} 个活跃池子")
                
                for chain, data in results.get('pool_analysis', {}).items():
                    if 'error' not in data:
                        avg_score = data.get('average_score', 0)
                        print(f"   {chain.upper()} 平均评分: {avg_score}/10")
                
                print(f"   📅 更新时间: {result.get('timestamp', 'Unknown')}")
            else:
                print("❌ 无法获取市场总结")
                
        except Exception as e:
            print(f"❌ 获取市场总结失败: {e}")
    
    async def test_system(self):
        """测试系统"""
        print("🧪 测试系统组件...")
        print("-" * 40)
        
        try:
            # 运行我们之前的成功测试
            from test_agno_success import main as test_main
            success = await test_main()
            
            if success:
                print("\n✅ 系统测试通过！所有组件正常工作")
            else:
                print("\n⚠️ 系统测试发现问题，请检查日志")
                
        except Exception as e:
            print(f"❌ 系统测试失败: {e}")
    
    def show_config(self):
        """显示配置"""
        print("⚙️ 系统配置:")
        print("-" * 40)
        
        try:
            # 显示策略配置
            import yaml
            with open('config/strategies.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            print("📋 策略配置:")
            print(f"   最小TVL: ${config['strategy']['min_tvl']:,}")
            print(f"   最小24h交易量: ${config['strategy']['min_volume_24h']:,}")
            print(f"   每链最大池子数: {config['strategy']['max_pools_per_chain']}")
            print(f"   重平衡阈值: {config['strategy']['rebalance_threshold']*100}%")
            
            print("\n🛡️ 风险管理:")
            print(f"   最大滑点: {config['risk_management']['max_slippage']*100}%")
            print(f"   紧急退出阈值: {config['risk_management']['emergency_exit_threshold']*100}%")
            print(f"   止损阈值: {config['risk_management']['stop_loss_threshold']*100}%")
            
            print("\n💰 资金分配:")
            print(f"   每池最大分配: {config['strategy']['allocation']['max_per_pool']*100}%")
            print(f"   储备比例: {config['strategy']['allocation']['reserve_ratio']*100}%")
            
        except Exception as e:
            print(f"❌ 无法读取配置: {e}")
    
    def show_history(self):
        """显示历史"""
        print("📈 执行历史:")
        print("-" * 40)
        
        try:
            # 查找历史报告文件
            history_dir = "data/test_reports"
            if os.path.exists(history_dir):
                files = [f for f in os.listdir(history_dir) if f.endswith('.json')]
                files.sort(reverse=True)  # 最新的在前
                
                if files:
                    print(f"找到 {len(files)} 个历史记录:")
                    for i, file in enumerate(files[:5]):  # 显示最近5个
                        print(f"   {i+1}. {file}")
                    
                    if len(files) > 5:
                        print(f"   ... 还有 {len(files)-5} 个更早的记录")
                else:
                    print("   暂无历史记录")
            else:
                print("   历史记录目录不存在")
                
        except Exception as e:
            print(f"❌ 无法读取历史: {e}")
    
    def show_help(self):
        """显示帮助"""
        print("🆘 DyFlow使用帮助:")
        print("-" * 40)
        print("📖 系统功能:")
        print("   • 自动扫描BSC和Solana链上的LP池子")
        print("   • 使用AI分析池子质量和风险")
        print("   • 提供投资建议和风险评估")
        print("   • 支持24/7持续监控")
        print()
        print("🎯 策略说明:")
        print("   • 专注于低浮动Meme币LP策略")
        print("   • 优先选择高TVL和高交易量池子")
        print("   • 自动风险管理和止损")
        print("   • 智能资金分配和重平衡")
        print()
        print("⚙️ 配置文件:")
        print("   • config/strategies.yaml - 策略配置")
        print("   • config/config.yaml - 系统配置")
        print("   • config/networks.yaml - 网络配置")
        print()
        print("📁 重要目录:")
        print("   • data/test_reports/ - 执行历史")
        print("   • logs/ - 系统日志")
        print("   • src/ - 源代码")
    
    def display_monitoring_result(self, result: Dict[str, Any]):
        """显示监控结果"""
        if not result:
            return
        
        print("\n📊 监控结果:")
        print("="*50)
        
        print(f"🆔 工作流ID: {result.get('workflow_id', 'Unknown')}")
        print(f"📅 时间戳: {result.get('timestamp', 'Unknown')}")
        print(f"📈 状态: {result.get('status', 'Unknown')}")
        
        results = result.get('results', {})
        
        # 数据收集结果
        data_collection = results.get('data_collection', {})
        if data_collection:
            print("\n📡 数据收集:")
            for chain, data in data_collection.items():
                if 'error' not in data:
                    print(f"   {chain.upper()}: {data.get('pools_count', 0)} 个池子")
                else:
                    print(f"   {chain.upper()}: 错误 - {data['error']}")
        
        # 分析结果
        analysis = results.get('pool_analysis', {})
        if analysis:
            print("\n📊 池子分析:")
            for chain, data in analysis.items():
                if 'error' not in data:
                    avg_score = data.get('average_score', 0)
                    top_pools = data.get('top_pools', [])
                    print(f"   {chain.upper()}: 平均评分 {avg_score}/10")
                    if top_pools:
                        best_pool = top_pools[0]
                        print(f"   最佳池子: {best_pool.get('pool_name', 'Unknown')} (评分: {best_pool.get('total_score', 0)})")
        
        # 风险评估
        risk_assessment = results.get('risk_assessment', {})
        if risk_assessment:
            print("\n🛡️ 风险评估:")
            for chain, data in risk_assessment.items():
                if 'error' not in data:
                    risk_level = data.get('overall_market_risk', 'Unknown')
                    print(f"   {chain.upper()}: 整体风险 {risk_level}")
        
        print("="*50)
    
    async def run(self):
        """运行启动器"""
        self.show_welcome()
        self.show_strategy_info()
        
        while True:
            try:
                self.show_menu()
                choice = input("请输入选择 (0-7): ").strip()
                
                if choice == "0":
                    print("👋 感谢使用DyFlow，再见！")
                    break
                elif choice == "1":
                    await self.run_single_monitoring()
                elif choice == "2":
                    await self.run_continuous_monitoring()
                elif choice == "3":
                    await self.get_market_summary()
                elif choice == "4":
                    await self.test_system()
                elif choice == "5":
                    self.show_config()
                elif choice == "6":
                    self.show_history()
                elif choice == "7":
                    self.show_help()
                else:
                    print("❌ 无效选择，请重新输入")
                
                if choice != "0":
                    input("\n按回车键继续...")
                    print("\n" + "="*64 + "\n")
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出系统")
                break
            except Exception as e:
                print(f"❌ 系统错误: {e}")
                input("按回车键继续...")

async def main():
    """主函数"""
    launcher = DyFlowLauncher()
    await launcher.run()

if __name__ == "__main__":
    asyncio.run(main())
