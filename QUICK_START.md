# 🚀 DyFlow 快速开始指南

## 📋 系统概述

DyFlow是一个基于Agno Framework的24/7自动化LP策略系统，专注于BSC和Solana链上的低浮动Meme币流动性池管理。

### 🎯 核心功能
- **智能池子扫描**: 自动发现高质量LP池子
- **AI驱动分析**: 使用本地Ollama Qwen2.5模型进行专业分析
- **风险管理**: 自动止损、紧急退出和风险评估
- **多链支持**: BSC (PancakeSwap V3) 和 Solana (Meteora DLMM)
- **24/7监控**: 持续监控和自动调整

## 🏗️ 系统架构

### 基于Agno Framework
```
DyFlow System
├── 🤖 AI Agents (基于Ollama Qwen2.5)
│   ├── DataAgent - 数据收集专家
│   ├── AnalysisAgent - 池子分析专家
│   └── RiskAgent - 风险评估专家
├── 🔄 Workflows
│   └── LP监控工作流
├── 🛠️ Tools
│   ├── PancakeSwap V3 工具
│   ├── Meteora DLMM 工具
│   └── Jupiter Swap 工具
└── ⚙️ 配置系统
    ├── 策略配置
    ├── 风险管理
    └── 资金分配
```

## 🚀 快速开始

### 1. 启动系统 (推荐)
```bash
python dyflow_launcher.py
```
这会启动友好的交互式界面，提供以下选项：
- 🔍 单次监控
- 🔄 持续监控
- 📊 市场总结
- 🧪 系统测试

### 2. 命令行方式
```bash
# 单次监控
python dyflow_agno_system.py

# 或使用原始系统
python dyflow_main.py monitor
```

### 3. 测试系统
```bash
# 测试所有组件
python test_agno_success.py

# 测试Agno Framework集成
python test_agno_framework.py
```

## 📊 策略配置

### 当前策略 (config/strategies.yaml)

#### 🎯 投资目标
- **目标**: 低浮动Meme币LP策略
- **最小TVL**: $100,000
- **最小24h交易量**: $50,000
- **每链最大池子数**: 5个

#### 🛡️ 风险管理
- **最大滑点**: 1%
- **紧急退出阈值**: 10%
- **止损阈值**: 15%
- **风险检查间隔**: 5分钟

#### 💰 资金分配
- **每池最大分配**: 20%
- **储备比例**: 10%
- **跨链比例**: 50%

#### 🔄 重平衡
- **重平衡间隔**: 24小时
- **重平衡阈值**: 2%

## 🎮 使用方式

### 交互式界面 (推荐)
```bash
python dyflow_launcher.py
```

界面提供：
1. **单次监控** - 扫描和分析池子
2. **持续监控** - 24/7自动运行
3. **市场总结** - 快速市场概况
4. **系统测试** - 验证所有组件
5. **配置查看** - 显示详细配置
6. **历史记录** - 查看执行历史

### 命令行方式
```bash
# 基于Agno Framework的系统
python dyflow_agno_system.py

# 原始系统 (如果需要)
python dyflow_main.py monitor          # 单次监控
python dyflow_main.py continuous 120   # 持续监控120分钟
python dyflow_main.py market           # 获取市场总结
```

## 📈 监控指标

### 池子指标
- **TVL** (总锁定价值)
- **24h交易量**
- **手续费率和收益**
- **流动性深度**
- **价格影响**

### 风险指标
- **无常损失**
- **价格波动率**
- **流动性风险**
- **对手方风险**

### AI分析
- **池子质量评分** (0-100分)
- **投资建议** (BUY/HOLD/AVOID)
- **风险等级** (LOW/MEDIUM/HIGH/VERY_HIGH)

## 🔧 系统要求

### 已验证的功能 ✅
- **本地AI推理** (Ollama Qwen2.5:3b)
- **多Agent协作**
- **DeFi专业知识**
- **工作流程模拟**
- **性能稳定性** (100%测试通过率)

### 技术栈
- **Python 3.9+**
- **Agno Framework** - AI Agent框架
- **Ollama** - 本地AI模型运行
- **Web3.py** - 区块链交互
- **Structlog** - 结构化日志

## 📁 重要文件和目录

### 配置文件
- `config/strategies.yaml` - 策略配置
- `config/config.yaml` - 系统配置
- `config/networks.yaml` - 网络配置

### 执行文件
- `dyflow_launcher.py` - 交互式启动器 (推荐)
- `dyflow_agno_system.py` - 基于Agno的系统
- `dyflow_main.py` - 原始系统

### 测试文件
- `test_agno_success.py` - 成功案例测试
- `test_agno_framework.py` - Agno集成测试
- `test_agno_agents.py` - Agent功能测试

### 数据目录
- `data/test_reports/` - 执行历史报告
- `logs/` - 系统日志
- `src/` - 源代码

## 🎯 典型使用流程

### 1. 首次使用
```bash
# 1. 测试系统
python test_agno_success.py

# 2. 启动交互式界面
python dyflow_launcher.py

# 3. 选择 "4. 🧪 测试系统" 验证所有组件
# 4. 选择 "1. 🔍 单次监控" 进行首次监控
```

### 2. 日常使用
```bash
# 启动交互式界面
python dyflow_launcher.py

# 选择 "2. 🔄 持续监控" 进行24/7监控
# 或选择 "3. 📊 市场总结" 获取快速概况
```

### 3. 监控结果
系统会显示：
- 📡 **数据收集**: 每链发现的池子数量
- 📊 **池子分析**: 平均评分和最佳池子推荐
- 🛡️ **风险评估**: 整体市场风险等级
- 💡 **AI建议**: 基于分析的投资建议

## 🔍 故障排除

### 常见问题
1. **Ollama连接失败**
   ```bash
   # 确保Ollama正在运行
   ollama list
   # 如果没有qwen2.5模型，下载它
   ollama pull qwen2.5:3b
   ```

2. **网络连接问题**
   - 检查BSC和Solana RPC连接
   - 验证Subgraph API密钥

3. **权限问题**
   ```bash
   # 确保有写入权限
   chmod +x dyflow_launcher.py
   ```

### 获取帮助
- 在交互式界面中选择 "7. 🆘 帮助文档"
- 查看 `logs/` 目录中的详细日志
- 运行 `python test_agno_success.py` 验证系统状态

## 🎉 开始使用

现在你可以开始使用DyFlow系统了！

**推荐的第一步**：
```bash
python dyflow_launcher.py
```

选择 "4. 🧪 测试系统" 确保一切正常，然后选择 "1. 🔍 单次监控" 开始你的第一次LP池子分析！

---

**🚀 DyFlow - 让AI为你的DeFi投资保驾护航！**
