#!/usr/bin/env python3
"""
测试Agno Framework基础功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

async def test_agno_agent():
    """测试Agno Agent基础功能"""
    print("🤖 测试Agno Agent...")
    
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        
        # 创建Agent
        agent = Agent(
            name="TestAgent",
            role="测试专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=[
                "你是一个测试专家。",
                "请简洁地回答问题。",
                "保持专业和友好的语调。"
            ],
            reasoning=False,
            show_tool_calls=False,
            markdown=True
        )
        
        print("✅ Agno Agent创建成功")
        
        # 测试Agent运行
        response = agent.run("你好，请简单介绍一下你自己")
        print(f"✅ Agent响应: {response.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Agno Agent测试失败: {e}")
        return False

async def test_agno_workflow():
    """测试Agno Workflow基础功能"""
    print("\n🔄 测试Agno Workflow...")

    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama

        # 简化测试 - 直接测试多Agent协作
        agent1 = Agent(
            name="Agent1",
            role="数据收集专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=["你负责收集数据", "请简洁回答"],
            reasoning=False,
            show_tool_calls=False,
            markdown=True
        )

        agent2 = Agent(
            name="Agent2",
            role="数据分析专家",
            model=Ollama(id="qwen2.5:3b"),
            instructions=["你负责分析数据", "请简洁回答"],
            reasoning=False,
            show_tool_calls=False,
            markdown=True
        )

        print("✅ 多Agent创建成功")

        # 模拟工作流：Agent1收集数据，Agent2分析数据
        data_result = agent1.run("请收集关于DeFi流动性挖矿的基础信息")
        analysis_result = agent2.run(f"请分析以下数据: {data_result.content[:200]}")

        print("✅ 多Agent协作测试成功")
        print(f"   数据收集: {data_result.content[:50]}...")
        print(f"   数据分析: {analysis_result.content[:50]}...")

        return True

    except Exception as e:
        print(f"❌ Agno Workflow测试失败: {e}")
        return False

async def test_dyflow_tools():
    """测试DyFlow工具集成"""
    print("\n🔧 测试DyFlow工具集成...")
    
    try:
        from src.tools.pancake_subgraph_tool import PancakeSubgraphTool
        from src.tools.meteora_dlmm_tool import MeteoraDLMMTool
        
        # 测试PancakeSwap工具
        pancake_config = {
            'subgraph_url': 'https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ',
            'api_key': '9731921233db132a98c2325878e6c153'
        }
        pancake_tool = PancakeSubgraphTool(pancake_config)
        bsc_pools = await pancake_tool.get_top_pools(limit=2)
        print(f"✅ PancakeSwap工具: 获取到 {len(bsc_pools)} 个BSC池子")

        # 测试Meteora工具
        meteora_config = {}
        meteora_tool = MeteoraDLMMTool(meteora_config)
        solana_pools = await meteora_tool.get_all_pools(limit=2)
        print(f"✅ Meteora工具: 获取到 {len(solana_pools)} 个Solana池子")
        
        return True
        
    except Exception as e:
        print(f"❌ DyFlow工具测试失败: {e}")
        return False

async def test_integrated_system():
    """测试集成系统"""
    print("\n🚀 测试集成系统...")
    
    try:
        from agno.agent import Agent
        from agno.workflow import Workflow
        from agno.models.ollama import Ollama
        from src.tools.pancake_subgraph_tool import PancakeSubgraphTool
        
        class DyFlowTestWorkflow(Workflow):
            description: str = "DyFlow测试工作流"
            
            def __init__(self):
                super().__init__()
                
                # 创建数据Agent
                self.data_agent = Agent(
                    name="DataAgent",
                    role="DeFi数据专家",
                    model=Ollama(id="qwen2.5:3b"),
                    instructions=[
                        "你是DeFi数据专家",
                        "负责分析LP池子数据",
                        "请提供简洁的分析结果"
                    ],
                    reasoning=False,
                    show_tool_calls=False,
                    markdown=True
                )
                
                # 初始化工具
                pancake_config = {
                    'subgraph_url': 'https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ',
                    'api_key': '9731921233db132a98c2325878e6c153'
                }
                self.pancake_tool = PancakeSubgraphTool(pancake_config)
            
            async def run(self):
                try:
                    # 1. 收集数据
                    pools = await self.pancake_tool.get_top_pools(limit=2)
                    
                    # 2. 准备数据给Agent
                    pool_data = []
                    for pool in pools:
                        pool_info = {
                            "tokens": f"{pool.token0.get('symbol', 'Unknown')}/{pool.token1.get('symbol', 'Unknown')}",
                            "tvl": pool.tvl_usd,
                            "volume_24h": pool.volume_usd_24h
                        }
                        pool_data.append(pool_info)
                    
                    # 3. Agent分析
                    analysis_prompt = f"""
                    请分析以下LP池子数据：
                    {pool_data}
                    
                    请提供：
                    1. 最佳池子推荐
                    2. 简要分析原因
                    """
                    
                    analysis_result = self.data_agent.run(analysis_prompt)
                    
                    return {
                        "workflow_id": f"dyflow_test_{int(datetime.now().timestamp())}",
                        "status": "completed",
                        "pools_analyzed": len(pools),
                        "analysis": analysis_result.content[:200],
                        "timestamp": datetime.now().isoformat()
                    }
                    
                except Exception as e:
                    return {
                        "workflow_id": f"dyflow_test_{int(datetime.now().timestamp())}",
                        "status": "failed",
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
        
        # 运行集成测试
        workflow = DyFlowTestWorkflow()
        print("✅ DyFlow集成工作流创建成功")
        
        result = await workflow.run()
        
        if result["status"] == "completed":
            print("✅ 集成系统测试成功")
            print(f"   分析了 {result['pools_analyzed']} 个池子")
            print(f"   AI分析: {result['analysis'][:100]}...")
            return True
        else:
            print(f"❌ 集成系统测试失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 集成系统测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 Agno Framework + DyFlow 集成测试")
    print("=" * 60)
    
    test_results = {}
    
    # 1. 测试Agno Agent
    test_results['agno_agent'] = await test_agno_agent()
    
    # 2. 测试Agno Workflow
    test_results['agno_workflow'] = await test_agno_workflow()
    
    # 3. 测试DyFlow工具
    test_results['dyflow_tools'] = await test_dyflow_tools()
    
    # 4. 测试集成系统
    test_results['integrated_system'] = await test_integrated_system()
    
    # 计算结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = passed_tests / total_tests * 100
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总测试数: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {total_tests - passed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print("\n🎉 Agno Framework集成测试成功！")
        print("💡 DyFlow系统已准备好基于Agno架构运行")
        return True
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
