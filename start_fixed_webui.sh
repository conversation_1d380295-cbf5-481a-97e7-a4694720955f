#!/bin/bash

echo "🚀 启动DyFlow 修复版WebUI Dashboard..."
echo "✅ 已修复的问题："
echo "   🔄 去掉了有问题的自动刷新"
echo "   🤖 保留了AI Agent聊天功能"
echo "   📊 数据正常显示"
echo "   💬 支持智能对话交互"
echo ""
echo "🌐 WebUI将在 http://localhost:8504 启动"
echo "📱 浏览器将自动打开"
echo ""
echo "💡 使用说明："
echo "   1. 点击'🔄 刷新数据'按钮手动更新数据"
echo "   2. 在'🤖 AI聊天'标签页与Agent对话"
echo "   3. 使用侧边栏的快捷指令按钮"
echo "   4. 试试问：'扫描最佳池子' 或 '查看持仓状态'"
echo ""
echo "🔄 按 Ctrl+C 停止服务"
echo "=" * 50

/usr/bin/python3 -m streamlit run dyflow_fixed_webui.py --server.port 8504 --server.address 0.0.0.0
