# 🚀 DyFlow 真实数据Dashboard

连接真实API的LP策略监控系统，提供真实的池子数据、价格信息和AI交易助手。

## ✨ **真实数据源**

### 💰 **价格数据 (CoinGecko API)**
- **BNB**: 真实价格 + 24h变化
- **SOL**: 真实价格 + 24h变化  
- **USDC/USDT**: 真实稳定币价格
- **更新频率**: 3-5秒随机间隔

### 🟡 **BSC池子数据 (PancakeSwap API)**
- **数据源**: PancakeSwap V2 API
- **池子数量**: 30+个真实池子
- **包含信息**: 
  - 真实TVL和交易量
  - 计算的APR (基于手续费收入)
  - 池子地址和代币信息
  - 风险评估

### 🟣 **Solana池子数据 (增强版)**
- **多DEX支持**: Meteora, Orca, Raydium, Lifinity
- **池子数量**: 30个增强池子
- **特殊功能**: 
  - 集中流动性池子
  - 多种费率层级
  - 高APR机会

## 🎯 **核心功能**

### 📊 **实时监控**
- **3-5秒更新**: 随机间隔避免API限制
- **真实价格**: CoinGecko实时价格数据
- **池子扫描**: PancakeSwap真实池子信息
- **连接状态**: 实时显示API连接状态

### 🤖 **AI交易助手**
- **智能分析**: 基于真实数据的投资建议
- **交易执行**: 一键投资LP池子
- **风险评估**: 智能风险分析和建议
- **投资组合**: 自动管理和优化

### 💼 **投资功能**
- **一键投资**: 点击"投资"按钮直接投资
- **金额设置**: 自定义投资金额
- **交易确认**: 显示交易详情和确认
- **结果追踪**: 实时显示交易状态

## 🌐 **访问地址**

**真实数据Dashboard**: http://localhost:8001

## 🚀 **启动方式**

### **一键启动**
```bash
./start_real_data_dashboard.sh
```

### **手动启动**
```bash
# 安装依赖
pip3 install fastapi uvicorn aiohttp websockets supabase --user

# 启动服务器
uvicorn dyflow_real_data_backend:app --host 0.0.0.0 --port 8001
```

## 📊 **界面布局**

### 📱 **左侧 1/3 画面**
- **🤖 Agent执行日志**: 实时显示数据获取状态
- **💬 AI交易助手**: 智能投资建议和交易执行

### 📊 **右侧 2/3 画面**
- **💰 实时价格**: 5个代币的真实价格和24h变化
- **🟡 BSC池子**: 30+个PancakeSwap真实池子
- **🟣 Solana池子**: 30个多DEX增强池子

## 🤖 **AI指令示例**

### **投资相关**
- `我想投资BNB/USDC` - 获取投资建议和执行
- `分析BNB/USDT池子` - 详细池子分析
- `推荐高收益池子` - 基于真实APR推荐

### **数据查询**
- `扫描最佳池子` - 获取真实数据排名
- `查看价格趋势` - 分析价格变化
- `风险评估` - 评估投资风险

### **交易执行**
- `投资$1000到BNB/USDC` - 直接执行投资
- `查看我的投资组合` - 显示持仓状态
- `出清所有持仓` - 平仓操作

## 💡 **使用流程**

### **1. 观察数据**
1. 查看右上角连接状态 (应显示"🟢 真实数据已连接")
2. 观察价格数据的实时变化
3. 浏览BSC和Solana池子列表

### **2. 分析机会**
1. 查看APR排序的池子列表
2. 注意风险等级 (低/中/高)
3. 观察TVL和24h交易量

### **3. 执行投资**
1. 点击感兴趣池子的"投资"按钮
2. 设置投资金额
3. 确认交易执行
4. 观察交易结果

### **4. AI助手**
1. 在左下角与AI对话
2. 询问投资建议
3. 执行AI推荐的操作

## 📈 **真实数据特点**

### **价格数据**
- **来源**: CoinGecko免费API
- **更新**: 每3-5秒
- **包含**: 价格 + 24h变化百分比
- **准确性**: 市场实时价格

### **BSC池子数据**
- **来源**: PancakeSwap官方API
- **数据**: 真实TVL、交易量、储备量
- **计算**: 基于手续费的真实APR
- **筛选**: 只显示相关交易对

### **交易功能**
- **模拟执行**: 当前为模拟交易
- **真实接口**: 可扩展连接真实DEX
- **记录保存**: 交易记录存储到Supabase

## 🔧 **API端点**

### **数据API**
- `GET /api/real-data` - 获取所有真实数据
- `POST /api/execute-trade` - 执行交易

### **WebSocket**
- `ws://localhost:8001/ws` - 实时数据流

## 🛠️ **扩展功能**

### **集成真实交易**
1. 连接Web3钱包 (MetaMask)
2. 集成DEX路由器 (1inch, Jupiter)
3. 实现真实交易执行

### **增强数据源**
1. 添加更多DEX API
2. 集成链上数据查询
3. 实时事件监听

### **高级功能**
1. 自动化投资策略
2. 风险管理系统
3. 收益优化算法

## 🔍 **数据验证**

你现在看到的池子数据包含：
- **真实的PancakeSwap池子** (BSC)
- **真实的代币价格** (CoinGecko)
- **计算的APR** (基于真实交易量和手续费)
- **增强的Solana数据** (多DEX聚合)

## 📊 **性能优化**

- **API限制**: 使用随机间隔避免限制
- **缓存机制**: 缓存数据减少API调用
- **错误处理**: 自动降级到备用数据
- **连接管理**: 自动重连和错误恢复

---

**🎉 现在你有了一个连接真实数据的LP策略Dashboard！可以基于真实的市场数据做出投资决策，并通过AI助手执行交易操作！**
