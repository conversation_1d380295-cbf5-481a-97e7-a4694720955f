#!/usr/bin/env python3
"""
Supabase集成模块 - 用于DyFlow实时数据存储和检索
"""

from supabase import create_client, Client
from datetime import datetime
import json
from typing import Dict, List, Optional

class SupabaseManager:
    """Supabase数据库管理器"""
    
    def __init__(self):
        self.url = "https://ikxobiwfymtxhumpntw.supabase.co"
        self.anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlreG9iaXdoZnltdHhodW1wbnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk4OTA2NTIsImV4cCI6MjA1NTQ2NjY1Mn0.erqkmEPvyuqU2KePB-Jt03bNvtfO4tiD6KxwNtq8HuA"
        self.service_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlreG9iaXdoZnltdHhodW1wbnR3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTg5MDY1MiwiZXhwIjoyMDU1NDY2NjUyfQ.P4vR_QS5GXPU1zh25qb9pgWmg82QEJoymiVO8-7-REE"
        
        # 使用service_key获得完整权限
        self.supabase: Client = create_client(self.url, self.service_key)
        
    async def init_tables(self):
        """初始化数据库表结构"""
        try:
            # 创建钱包表
            await self.create_wallets_table()
            
            # 创建LP持仓表
            await self.create_positions_table()
            
            # 创建池子表
            await self.create_pools_table()
            
            # 创建Agent日志表
            await self.create_agent_logs_table()
            
            # 创建交易日志表
            await self.create_trading_logs_table()
            
            print("✅ Supabase表结构初始化完成")
            
        except Exception as e:
            print(f"❌ 初始化表结构失败: {e}")
    
    def create_wallets_table(self):
        """创建钱包表"""
        # 注意：实际的表创建需要在Supabase Dashboard中进行
        # 这里只是数据操作的示例
        pass
    
    def create_positions_table(self):
        """创建LP持仓表"""
        pass
    
    def create_pools_table(self):
        """创建池子表"""
        pass
    
    def create_agent_logs_table(self):
        """创建Agent日志表"""
        pass
    
    def create_trading_logs_table(self):
        """创建交易日志表"""
        pass
    
    def save_wallet_data(self, wallet_data: Dict):
        """保存钱包数据"""
        try:
            result = self.supabase.table('wallets').upsert({
                'id': 'current',
                'data': wallet_data,
                'updated_at': datetime.now().isoformat()
            }).execute()
            return result
        except Exception as e:
            print(f"保存钱包数据失败: {e}")
            return None
    
    def save_positions_data(self, positions_data: List[Dict]):
        """保存LP持仓数据"""
        try:
            # 清除旧数据
            self.supabase.table('positions').delete().neq('id', 'never_match').execute()
            
            # 插入新数据
            for i, position in enumerate(positions_data):
                self.supabase.table('positions').insert({
                    'id': f'pos_{i}',
                    'chain': position['chain'],
                    'pair': position['pair'],
                    'liquidity': position['liquidity'],
                    'apr': position['apr'],
                    'pnl': position['pnl'],
                    'fees': position['fees'],
                    'status': position['status'],
                    'updated_at': datetime.now().isoformat()
                }).execute()
            
            return True
        except Exception as e:
            print(f"保存持仓数据失败: {e}")
            return False
    
    def save_pools_data(self, bsc_pools: List[Dict], solana_pools: List[Dict]):
        """保存池子数据"""
        try:
            # 清除旧数据
            self.supabase.table('pools').delete().neq('id', 'never_match').execute()
            
            # 保存BSC池子
            for i, pool in enumerate(bsc_pools):
                self.supabase.table('pools').insert({
                    'id': f'bsc_{i}',
                    'chain': 'BSC',
                    'pair': pool['pair'],
                    'protocol': pool['protocol'],
                    'tvl': pool['tvl'],
                    'volume_24h': pool['volume_24h'],
                    'apr': pool['apr'],
                    'risk_level': pool['risk_level'],
                    'recommendation': pool['recommendation'],
                    'updated_at': datetime.now().isoformat()
                }).execute()
            
            # 保存Solana池子
            for i, pool in enumerate(solana_pools):
                self.supabase.table('pools').insert({
                    'id': f'sol_{i}',
                    'chain': 'Solana',
                    'pair': pool['pair'],
                    'protocol': pool['protocol'],
                    'tvl': pool['tvl'],
                    'volume_24h': pool['volume_24h'],
                    'apr': pool['apr'],
                    'risk_level': pool['risk_level'],
                    'recommendation': pool['recommendation'],
                    'updated_at': datetime.now().isoformat()
                }).execute()
            
            return True
        except Exception as e:
            print(f"保存池子数据失败: {e}")
            return False
    
    def save_agent_log(self, log_data: Dict):
        """保存Agent日志"""
        try:
            result = self.supabase.table('agent_logs').insert({
                'time': log_data['time'],
                'level': log_data['level'],
                'agent': log_data['agent'],
                'message': log_data['message'],
                'created_at': datetime.now().isoformat()
            }).execute()
            return result
        except Exception as e:
            print(f"保存Agent日志失败: {e}")
            return None
    
    def save_trading_log(self, log_data: Dict):
        """保存交易日志"""
        try:
            result = self.supabase.table('trading_logs').insert({
                'time': log_data['time'],
                'operation': log_data['operation'],
                'pair': log_data['pair'],
                'amount': log_data['amount'],
                'status': log_data['status'],
                'created_at': datetime.now().isoformat()
            }).execute()
            return result
        except Exception as e:
            print(f"保存交易日志失败: {e}")
            return None
    
    def get_latest_data(self):
        """获取最新数据"""
        try:
            # 获取钱包数据
            wallets = self.supabase.table('wallets').select('*').eq('id', 'current').execute()
            
            # 获取持仓数据
            positions = self.supabase.table('positions').select('*').order('updated_at', desc=True).execute()
            
            # 获取池子数据
            pools = self.supabase.table('pools').select('*').order('apr', desc=True).execute()
            
            # 获取最新Agent日志
            agent_logs = self.supabase.table('agent_logs').select('*').order('created_at', desc=True).limit(10).execute()
            
            # 获取最新交易日志
            trading_logs = self.supabase.table('trading_logs').select('*').order('created_at', desc=True).limit(10).execute()
            
            return {
                'wallets': wallets.data[0]['data'] if wallets.data else {},
                'positions': positions.data,
                'pools': pools.data,
                'agent_logs': agent_logs.data,
                'trading_logs': trading_logs.data
            }
        except Exception as e:
            print(f"获取数据失败: {e}")
            return None
    
    def get_historical_data(self, table: str, hours: int = 24):
        """获取历史数据"""
        try:
            from_time = datetime.now() - timedelta(hours=hours)
            result = self.supabase.table(table).select('*').gte('created_at', from_time.isoformat()).execute()
            return result.data
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return []
    
    def cleanup_old_data(self, days: int = 7):
        """清理旧数据"""
        try:
            from datetime import timedelta
            cutoff_time = datetime.now() - timedelta(days=days)
            
            # 清理旧的Agent日志
            self.supabase.table('agent_logs').delete().lt('created_at', cutoff_time.isoformat()).execute()
            
            # 清理旧的交易日志
            self.supabase.table('trading_logs').delete().lt('created_at', cutoff_time.isoformat()).execute()
            
            print(f"✅ 清理了{days}天前的旧数据")
            return True
        except Exception as e:
            print(f"清理数据失败: {e}")
            return False

# 使用示例
if __name__ == "__main__":
    import asyncio
    
    async def test_supabase():
        """测试Supabase连接"""
        db = SupabaseManager()
        
        # 测试连接
        try:
            # 测试简单查询
            result = db.supabase.table('test').select('*').limit(1).execute()
            print("✅ Supabase连接成功")
            
            # 测试保存数据
            test_data = {
                'bsc': {'BNB': 2.45, 'USDC': 1250.30, 'total_value': 4457.75},
                'solana': {'SOL': 15.67, 'USDC': 2100.50, 'total_value': 4294.75}
            }
            
            # 这里可以测试各种数据操作
            print("📊 Supabase集成模块准备就绪")
            
        except Exception as e:
            print(f"❌ Supabase连接失败: {e}")
            print("💡 请检查API密钥和网络连接")
    
    asyncio.run(test_supabase())
