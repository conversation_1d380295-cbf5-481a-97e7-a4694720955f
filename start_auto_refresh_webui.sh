#!/bin/bash

echo "🚀 启动DyFlow 自动刷新WebUI Dashboard..."
echo "✨ 新功能特性："
echo "   🔄 真正的自动刷新 (每5秒)"
echo "   📊 全新布局设计"
echo "   🤖 左侧：Agent日志 + AI对话"
echo "   📈 右侧：所有数据信息展开"
echo ""
echo "🎨 布局说明："
echo "   📱 左侧 1/3 画面："
echo "      🤖 上方：Agent执行日志"
echo "      💬 下方：AI对话界面"
echo "   📊 右侧 2/3 画面："
echo "      💰 关键指标"
echo "      💼 钱包资产 & LP持仓"
echo "      🔍 BSC & Solana池子扫描"
echo "      📋 交易日志"
echo ""
echo "🌐 WebUI将在 http://localhost:8505 启动"
echo "📱 浏览器将自动打开"
echo ""
echo "💡 使用说明："
echo "   1. 勾选'🔄 自动刷新'开启5秒自动更新"
echo "   2. 在左下角与AI Agent对话"
echo "   3. 右侧查看所有实时数据"
echo "   4. 试试问：'扫描最佳池子' 或 '查看持仓状态'"
echo ""
echo "🔄 按 Ctrl+C 停止服务"
echo "=" * 50

/usr/bin/python3 -m streamlit run dyflow_auto_refresh_webui.py --server.port 8505 --server.address 0.0.0.0
