#!/usr/bin/env python3
"""
DyFlow WebUI 测试版本 - 最简单的版本来测试数据显示
"""

import streamlit as st
import pandas as pd
from datetime import datetime
import random

# 页面配置
st.set_page_config(
    page_title="DyFlow Test",
    page_icon="🚀",
    layout="wide"
)

def main():
    st.title("🚀 DyFlow WebUI 测试版本")
    st.write("测试数据是否能正常显示")
    
    # 测试基本数据显示
    st.header("📊 测试数据显示")
    
    # 测试1: 简单数据
    st.subheader("测试1: 基本指标")
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("总资产", "$35,452.50", "+$715.95")
    with col2:
        st.metric("LP价值", "$26,700.00", "+2.7%")
    with col3:
        st.metric("钱包余额", "$8,752.50")
    
    # 测试2: 简单表格
    st.subheader("测试2: 钱包数据表格")
    wallet_data = {
        "钱包": ["BSC主钱包", "Solana主钱包"],
        "主币": ["2.45 BNB", "15.67 SOL"],
        "USDC": ["1,250.30", "2,100.50"],
        "总价值": ["$4,457.75", "$4,294.75"]
    }
    df_wallet = pd.DataFrame(wallet_data)
    st.dataframe(df_wallet, use_container_width=True)
    
    # 测试3: LP持仓表格
    st.subheader("测试3: LP持仓数据")
    positions_data = {
        "链": ["BSC", "BSC", "Solana"],
        "交易对": ["BNB/USDC", "BNB/USDT", "SOL/USDC"],
        "流动性价值": ["$8,500", "$6,200", "$12,000"],
        "APR": ["24.5%", "21.2%", "32.8%"],
        "盈亏": ["+$320.50", "-$85.30", "+$480.75"],
        "状态": ["🟢 活跃", "🟡 监控", "🟢 活跃"]
    }
    df_positions = pd.DataFrame(positions_data)
    st.dataframe(df_positions, use_container_width=True)
    
    # 测试4: BSC池子数据
    st.subheader("测试4: BSC池子扫描")
    
    # 生成测试池子数据
    bsc_pools = []
    pairs = ["BNB/USDC", "BNB/USDT", "BNB/USD1"]
    protocols = ["PancakeSwap V3", "Uniswap V3", "SushiSwap"]
    
    for i in range(10):  # 只生成10个用于测试
        bsc_pools.append({
            "交易对": random.choice(pairs),
            "协议": random.choice(protocols),
            "TVL": f"${random.uniform(0.5, 5.0):.1f}M",
            "APR": f"{random.uniform(20, 80):.1f}%",
            "风险": random.choice(["低", "中", "高"]),
            "建议": random.choice(["BUY", "HOLD", "AVOID"])
        })
    
    df_bsc = pd.DataFrame(bsc_pools)
    st.dataframe(df_bsc, use_container_width=True)
    
    # 测试5: Solana池子数据
    st.subheader("测试5: Solana池子扫描")
    
    solana_pools = []
    protocols = ["Meteora DLMM", "Orca Whirlpool", "Raydium CLMM"]
    
    for i in range(10):  # 只生成10个用于测试
        solana_pools.append({
            "交易对": "SOL/USDC",
            "协议": random.choice(protocols),
            "TVL": f"${random.uniform(1.0, 6.0):.1f}M",
            "APR": f"{random.uniform(30, 100):.1f}%",
            "风险": random.choice(["低", "中", "高"]),
            "建议": random.choice(["BUY", "HOLD", "AVOID"])
        })
    
    df_sol = pd.DataFrame(solana_pools)
    st.dataframe(df_sol, use_container_width=True)
    
    # 测试6: 交易日志
    st.subheader("测试6: 交易日志")
    trading_logs = {
        "时间": ["20:24", "19:45", "18:30", "17:22"],
        "操作": ["调整范围", "开仓", "增加流动性", "收取手续费"],
        "交易对": ["BNB/USDC", "SOL/USDC", "BNB/USDC", "BNB/USDT"],
        "金额": ["$2,306", "$12,000", "$2,500", "$45.20"],
        "状态": ["⏳ 处理中", "✅ 成功", "✅ 成功", "✅ 成功"]
    }
    df_trading = pd.DataFrame(trading_logs)
    st.dataframe(df_trading, use_container_width=True)
    
    # 侧边栏测试
    with st.sidebar:
        st.header("⚙️ 控制面板")
        st.success("🟢 系统正常运行")
        st.info("🔗 BSC连接: ✅")
        st.info("🔗 Solana连接: ✅")
        
        if st.button("🔄 刷新数据"):
            st.rerun()
        
        # 显示当前时间
        st.write(f"🕒 当前时间: {datetime.now().strftime('%H:%M:%S')}")
    
    # 页面底部
    st.markdown("---")
    st.write(f"**最后更新:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    st.write("**状态:** 如果你能看到这些数据表格，说明WebUI工作正常！")

if __name__ == "__main__":
    main()
