#!/bin/bash

echo "🚀 启动DyFlow 动态WebUI Dashboard..."
echo "✨ 全新功能："
echo "   🔄 真正的实时数据更新"
echo "   🤖 AI Agent聊天助手"
echo "   📊 动态池子扫描"
echo "   💬 智能对话交互"
echo ""
echo "🌐 WebUI将在 http://localhost:8503 启动"
echo "📱 浏览器将自动打开"
echo ""
echo "💡 使用说明："
echo "   1. 开启自动刷新查看实时数据变化"
echo "   2. 在AI聊天标签页与Agent对话"
echo "   3. 试试问：'扫描最佳池子' 或 '查看持仓状态'"
echo "   4. 所有数据每秒都在动态更新"
echo ""
echo "🔄 按 Ctrl+C 停止服务"
echo "=" * 50

/usr/bin/python3 -m streamlit run dyflow_dynamic_webui.py --server.port 8503 --server.address 0.0.0.0
