#!/usr/bin/env python3
"""
DyFlow优化Dashboard - 重新规划布局
更大的池子扫描面板，简化系统信息，添加USD1交易对
"""

import asyncio
from datetime import datetime
import random
import threading
import queue

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.prompt import Prompt
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import box
from rich.align import Align

console = Console()

class DyFlowOptimizedDashboard:
    """DyFlow优化Dashboard"""
    
    def __init__(self):
        self.console = console
        self.user_input_queue = queue.Queue()
        self.chat_history = []
        
        # 系统数据
        self.system_data = {
            "timestamp": datetime.now(),
            "trading_logs": [
                {
                    "time": "2024-01-18 19:45:23",
                    "action": "开仓",
                    "pair": "SOL/USDC",
                    "amount": "$12,000",
                    "price": "$140.25",
                    "status": "✅ 成功",
                    "tx_hash": "5xKL...9mN2"
                },
                {
                    "time": "2024-01-18 18:30:15",
                    "action": "增加流动性",
                    "pair": "BNB/USDC",
                    "amount": "$2,500",
                    "price": "$680.50",
                    "status": "✅ 成功",
                    "tx_hash": "0xAB...CD12"
                },
                {
                    "time": "2024-01-18 17:22:08",
                    "action": "收取手续费",
                    "pair": "BNB/USDT",
                    "amount": "$45.20",
                    "price": "-",
                    "status": "✅ 成功",
                    "tx_hash": "0x12...EF34"
                },
                {
                    "time": "2024-01-18 16:15:42",
                    "action": "调整范围",
                    "pair": "SOL/USDC",
                    "amount": "$8,500",
                    "price": "$138.90",
                    "status": "⏳ 处理中",
                    "tx_hash": "7yMN...8pQ4"
                },
                {
                    "time": "2024-01-18 15:08:30",
                    "action": "平仓",
                    "pair": "BNB/USD1",
                    "amount": "$3,200",
                    "price": "$675.25",
                    "status": "❌ 失败",
                    "tx_hash": "0x56...GH78"
                }
            ],
            "wallets": {
                "BSC主钱包": {
                    "address": "******************************************",
                    "balances": {
                        "BNB": {"amount": 2.45, "usd_value": 1666.50},
                        "USDC": {"amount": 1250.30, "usd_value": 1250.30},
                        "USDT": {"amount": 890.75, "usd_value": 890.75},
                        "USD1": {"amount": 650.20, "usd_value": 650.20}
                    },
                    "total_usd": 4457.75
                },
                "Solana主钱包": {
                    "address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
                    "balances": {
                        "SOL": {"amount": 15.67, "usd_value": 2194.25},
                        "USDC": {"amount": 2100.50, "usd_value": 2100.50}
                    },
                    "total_usd": 4294.75
                }
            },
            "positions": {
                "BSC_BNB_USDC_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDC",
                    "liquidity_value": 8500.00,
                    "current_pnl": 320.50,
                    "pnl_percentage": 3.77,
                    "fees_earned": 45.20,
                    "current_apr": 24.5,
                    "status": "🟢 活跃",
                    "entry_time": "2024-01-15 10:30:00"
                },
                "BSC_BNB_USDT_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDT",
                    "liquidity_value": 6200.00,
                    "current_pnl": -85.30,
                    "pnl_percentage": -1.38,
                    "fees_earned": 28.90,
                    "current_apr": 21.2,
                    "status": "🟡 监控",
                    "entry_time": "2024-01-16 14:20:00"
                },
                "SOL_SOL_USDC_001": {
                    "chain": "Solana",
                    "pair": "SOL/USDC",
                    "liquidity_value": 12000.00,
                    "current_pnl": 480.75,
                    "pnl_percentage": 4.01,
                    "fees_earned": 67.40,
                    "current_apr": 32.8,
                    "status": "🟢 活跃",
                    "entry_time": "2024-01-17 09:15:00"
                }
            },
            "available_pools": {
                "BSC_BNB_USDC_V3_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDC",
                    "protocol": "PancakeSwap V3",
                    "tvl": 2500000.00,
                    "volume_24h": 850000.00,
                    "apr": 24.5,
                    "apy": 27.8,
                    "fee_tier": 0.25,
                    "risk_score": 25,
                    "recommendation": "BUY"
                },
                "BSC_BNB_USDT_V3_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDT",
                    "protocol": "PancakeSwap V3",
                    "tvl": 1800000.00,
                    "volume_24h": 620000.00,
                    "apr": 21.2,
                    "apy": 23.6,
                    "fee_tier": 0.25,
                    "risk_score": 28,
                    "recommendation": "HOLD"
                },
                "BSC_BNB_USD1_V3_001": {
                    "chain": "BSC",
                    "pair": "BNB/USD1",
                    "protocol": "PancakeSwap V3",
                    "tvl": 980000.00,
                    "volume_24h": 320000.00,
                    "apr": 28.7,
                    "apy": 33.0,
                    "fee_tier": 0.30,
                    "risk_score": 22,
                    "recommendation": "BUY"
                },
                "SOL_SOL_USDC_DLMM_001": {
                    "chain": "Solana",
                    "pair": "SOL/USDC",
                    "protocol": "Meteora DLMM",
                    "tvl": 3200000.00,
                    "volume_24h": 1200000.00,
                    "apr": 32.8,
                    "apy": 38.9,
                    "fee_tier": 0.30,
                    "risk_score": 18,
                    "recommendation": "BUY"
                },
                "SOL_SOL_USDC_DLMM_002": {
                    "chain": "Solana",
                    "pair": "SOL/USDC",
                    "protocol": "Meteora DLMM",
                    "tvl": 1650000.00,
                    "volume_24h": 580000.00,
                    "apr": 28.9,
                    "apy": 33.5,
                    "fee_tier": 0.30,
                    "risk_score": 22,
                    "recommendation": "BUY"
                }
            }
        }
    
    def create_header_panel(self):
        """创建简化的头部面板"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 计算总资产
        total_wallet_value = sum(wallet["total_usd"] for wallet in self.system_data["wallets"].values())
        total_lp_value = sum(pos["liquidity_value"] for pos in self.system_data["positions"].values())
        total_pnl = sum(pos["current_pnl"] for pos in self.system_data["positions"].values())
        total_fees = sum(pos["fees_earned"] for pos in self.system_data["positions"].values())
        total_value = total_wallet_value + total_lp_value
        
        header_content = f"""
[bold blue]🚀 DyFlow 实时监控Dashboard[/bold blue]                                                    [dim]{current_time}[/dim]

[green]💰 总资产: ${total_value:,.2f}[/green]  |  [blue]📊 LP价值: ${total_lp_value:,.2f}[/blue]  |  [yellow]💵 钱包余额: ${total_wallet_value:,.2f}[/yellow]  |  [magenta]📈 总盈亏: ${total_pnl:+,.2f}[/magenta]  |  [cyan]💎 手续费: ${total_fees:,.2f}[/cyan]
        """.strip()
        
        return Panel(
            header_content,
            box=box.DOUBLE,
            style="blue"
        )
    
    def create_wallets_table(self):
        """创建钱包状态表"""
        table = Table(title="💼 钱包资产", box=box.ROUNDED)
        table.add_column("钱包", style="cyan", width=12)
        table.add_column("地址", style="yellow", width=12)
        table.add_column("主币", style="green", justify="right", width=12)
        table.add_column("USDC", style="blue", justify="right", width=10)
        table.add_column("USDT", style="magenta", justify="right", width=10)
        table.add_column("USD1", style="bright_blue", justify="right", width=10)
        table.add_column("总价值", style="bold green", justify="right", width=12)
        
        for wallet_name, wallet_info in self.system_data["wallets"].items():
            address = wallet_info["address"]
            short_address = f"{address[:6]}...{address[-4:]}"
            
            balances = wallet_info["balances"]
            if "BNB" in balances:
                main_coin = f"{balances['BNB']['amount']:.2f} BNB"
                usdt_amount = f"{balances['USDT']['amount']:.2f}"
                usd1_amount = f"{balances.get('USD1', {'amount': 0})['amount']:.2f}"
            else:
                main_coin = f"{balances['SOL']['amount']:.2f} SOL"
                usdt_amount = "-"
                usd1_amount = "-"
            
            table.add_row(
                wallet_name,
                short_address,
                main_coin,
                f"{balances['USDC']['amount']:.2f}",
                usdt_amount,
                usd1_amount,
                f"${wallet_info['total_usd']:,.2f}"
            )
        
        return table
    
    def create_positions_table(self):
        """创建LP持仓表 - 移除健康度"""
        table = Table(title="📊 LP持仓状态", box=box.ROUNDED)
        table.add_column("链", style="cyan", width=8)
        table.add_column("交易对", style="yellow", width=12)
        table.add_column("流动性价值", style="green", justify="right", width=12)
        table.add_column("APR", style="magenta", justify="right", width=8)
        table.add_column("盈亏", justify="right", width=12)
        table.add_column("手续费", style="blue", justify="right", width=10)
        table.add_column("状态", justify="center", width=10)
        table.add_column("持仓时间", style="dim", width=12)
        
        for pos_id, pos_info in self.system_data["positions"].items():
            pnl = pos_info["current_pnl"]
            pnl_color = "green" if pnl >= 0 else "red"
            pnl_text = f"+${pnl:.2f}" if pnl >= 0 else f"${pnl:.2f}"
            
            apr = pos_info["current_apr"]
            apr_color = "green" if apr >= 30 else "yellow" if apr >= 20 else "red"
            
            # 计算持仓时间
            entry_time = datetime.strptime(pos_info["entry_time"], "%Y-%m-%d %H:%M:%S")
            holding_days = (datetime.now() - entry_time).days
            
            table.add_row(
                pos_info["chain"],
                pos_info["pair"],
                f"${pos_info['liquidity_value']:,.0f}",
                Text(f"{apr:.1f}%", style=apr_color),
                Text(pnl_text, style=pnl_color),
                f"${pos_info['fees_earned']:.2f}",
                pos_info["status"],
                f"{holding_days}天"
            )
        
        return table
    
    def create_pool_scanner_panel(self):
        """创建更大的池子扫描面板"""
        table = Table(title="🔍 可用池子扫描 (专注交易对筛选)", box=box.ROUNDED, show_header=True)
        table.add_column("链", style="cyan", width=8)
        table.add_column("交易对", style="yellow", width=12)
        table.add_column("协议", style="blue", width=15)
        table.add_column("TVL", style="green", justify="right", width=12)
        table.add_column("24h交易量", style="bright_blue", justify="right", width=12)
        table.add_column("APR", style="magenta", justify="right", width=8)
        table.add_column("APY", style="bright_magenta", justify="right", width=8)
        table.add_column("手续费率", style="cyan", justify="right", width=8)
        table.add_column("风险", justify="center", width=6)
        table.add_column("建议", justify="center", width=8)
        
        # 筛选条件：专注交易对 (包含USD1)
        target_pairs = ["BNB/USDC", "BNB/USDT", "BNB/USD1", "SOL/USDC"]
        filtered_pools = []
        
        for pool_id, pool_info in self.system_data["available_pools"].items():
            if pool_info["pair"] in target_pairs:
                if pool_info["tvl"] >= 100000 and pool_info["volume_24h"] >= 50000:
                    filtered_pools.append(pool_info)
        
        # 按APR排序
        filtered_pools.sort(key=lambda x: x["apr"], reverse=True)
        
        for pool in filtered_pools:
            # 风险等级
            risk_score = pool["risk_score"]
            if risk_score <= 20:
                risk_level = "低"
                risk_color = "green"
            elif risk_score <= 35:
                risk_level = "中"
                risk_color = "yellow"
            else:
                risk_level = "高"
                risk_color = "red"
            
            # 建议颜色
            rec = pool["recommendation"]
            rec_color = {
                "BUY": "green",
                "HOLD": "yellow",
                "AVOID": "red"
            }.get(rec, "white")
            
            table.add_row(
                pool["chain"],
                pool["pair"],
                pool["protocol"],
                f"${pool['tvl']/1000000:.1f}M",
                f"${pool['volume_24h']/1000:.0f}K",
                f"{pool['apr']:.1f}%",
                f"{pool['apy']:.1f}%",
                f"{pool['fee_tier']:.2f}%",
                Text(risk_level, style=risk_color),
                Text(rec, style=rec_color)
            )
        
        return table

    def create_trading_logs_panel(self):
        """创建交易日志面板"""
        table = Table(title="📋 交易日志 (最近执行记录)", box=box.ROUNDED, show_header=True)
        table.add_column("时间", style="dim", width=16)
        table.add_column("操作", style="cyan", width=10)
        table.add_column("交易对", style="yellow", width=10)
        table.add_column("金额", style="green", justify="right", width=10)
        table.add_column("价格", style="blue", justify="right", width=10)
        table.add_column("状态", justify="center", width=8)
        table.add_column("交易哈希", style="dim", width=12)

        for log in self.system_data["trading_logs"]:
            # 状态颜色
            status = log["status"]
            if "成功" in status:
                status_color = "green"
            elif "处理中" in status:
                status_color = "yellow"
            else:
                status_color = "red"

            # 操作颜色
            action = log["action"]
            if action in ["开仓", "增加流动性"]:
                action_color = "green"
            elif action in ["平仓", "减少流动性"]:
                action_color = "red"
            else:
                action_color = "blue"

            table.add_row(
                log["time"].split(" ")[1],  # 只显示时间部分
                Text(action, style=action_color),
                log["pair"],
                log["amount"],
                log["price"],
                Text(status, style=status_color),
                log["tx_hash"]
            )

        return table
    
    def create_dashboard_layout(self):
        """创建优化的Dashboard布局"""
        layout = Layout()

        # 主要布局：头部 + 主体 + 底部
        layout.split_column(
            Layout(name="header", size=5),
            Layout(name="main"),
            Layout(name="footer", size=6)
        )

        # 主体分为左右两部分
        layout["main"].split_row(
            Layout(name="left", ratio=2),   # 左侧：钱包+持仓+扫描
            Layout(name="right", ratio=1)   # 右侧：交易日志
        )

        # 左侧分为三部分
        layout["left"].split_column(
            Layout(name="wallets", ratio=1),
            Layout(name="positions", ratio=2),
            Layout(name="pool_scanner", ratio=2)  # 池子扫描和持仓放同一边
        )

        # 右侧就是交易日志
        layout["right"].update(self.create_trading_logs_panel())

        # 填充左侧内容
        layout["header"].update(self.create_header_panel())
        layout["wallets"].update(self.create_wallets_table())
        layout["positions"].update(self.create_positions_table())
        layout["pool_scanner"].update(self.create_pool_scanner_panel())

        # 底部状态栏 - 简化
        footer_content = """
[bold green]系统状态:[/bold green] 🟢 正常运行 | [bold blue]连接状态:[/bold blue] BSC✅ Solana✅ AI✅ | [bold yellow]监控模式:[/bold yellow] 🟢 启用

[dim]💬 在下方输入AI指令 | 支持: 出清持仓, 扫描池子, 查看USD1详情 | 按Ctrl+C退出[/dim]
        """.strip()

        layout["footer"].update(Panel(
            footer_content,
            title="💡 系统状态 & AI指令输入",
            style="yellow"
        ))

        return layout
    
    def update_data(self):
        """更新实时数据"""
        # 更新时间戳
        self.system_data["timestamp"] = datetime.now()

        # 更新持仓数据
        for pos_id in self.system_data["positions"]:
            pos = self.system_data["positions"][pos_id]

            # 更新盈亏
            change = random.uniform(-5, 8)
            pos["current_pnl"] += change
            pos["pnl_percentage"] = (pos["current_pnl"] / pos["liquidity_value"]) * 100

            # 更新手续费
            pos["fees_earned"] += random.uniform(0, 0.3)

            # 更新APR
            apr_change = random.uniform(-0.5, 1.0)
            pos["current_apr"] = max(5, pos["current_apr"] + apr_change)

        # 更新可用池子数据
        for pool_id in self.system_data["available_pools"]:
            pool = self.system_data["available_pools"][pool_id]

            # 更新APR/APY
            apr_change = random.uniform(-1, 2)
            pool["apr"] = max(5, pool["apr"] + apr_change)
            pool["apy"] = pool["apr"] * 1.15

            # 更新TVL和交易量
            tvl_change = random.uniform(-0.03, 0.05)
            pool["tvl"] = max(50000, pool["tvl"] * (1 + tvl_change))

            volume_change = random.uniform(-0.08, 0.12)
            pool["volume_24h"] = max(10000, pool["volume_24h"] * (1 + volume_change))

        # 随机更新交易日志状态
        if random.random() < 0.1:  # 10%概率更新日志
            for log in self.system_data["trading_logs"]:
                if "处理中" in log["status"]:
                    log["status"] = "✅ 成功"
                    break

        # 偶尔添加新的交易日志
        if random.random() < 0.05:  # 5%概率添加新日志
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            actions = ["收取手续费", "调整范围", "重新平衡"]
            pairs = ["BNB/USDC", "BNB/USDT", "SOL/USDC", "BNB/USD1"]

            new_log = {
                "time": current_time,
                "action": random.choice(actions),
                "pair": random.choice(pairs),
                "amount": f"${random.randint(100, 5000)}",
                "price": f"${random.randint(100, 800)}.{random.randint(10, 99)}",
                "status": "⏳ 处理中",
                "tx_hash": f"0x{random.randint(10, 99)}...{random.randint(10, 99)}"
            }

            # 添加到开头，保持最新的在前
            self.system_data["trading_logs"].insert(0, new_log)

            # 保持最多10条记录
            if len(self.system_data["trading_logs"]) > 10:
                self.system_data["trading_logs"] = self.system_data["trading_logs"][:10]
    
    async def process_ai_command(self, command: str) -> str:
        """处理AI指令"""
        command_lower = command.lower()
        
        if '出清' in command_lower or '清仓' in command_lower:
            response = "✅ 正在执行出清操作...\n\n"
            response += "🔄 关闭BNB/USDC持仓 → 获得12.5 BNB\n"
            response += "🔄 关闭BNB/USDT持仓 → 获得9.2 BNB\n"
            response += "🔄 关闭SOL/USDC持仓 → 获得85.5 SOL\n\n"
            response += "✅ 出清完成！总计: 21.7 BNB + 85.5 SOL"

            # 添加交易日志
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            for pair in ["BNB/USDC", "BNB/USDT", "SOL/USDC"]:
                log = {
                    "time": current_time,
                    "action": "平仓",
                    "pair": pair,
                    "amount": "$8,500" if "BNB" in pair else "$12,000",
                    "price": "$680.50" if "BNB" in pair else "$140.25",
                    "status": "✅ 成功",
                    "tx_hash": f"0x{random.randint(10, 99)}...{random.randint(10, 99)}"
                }
                self.system_data["trading_logs"].insert(0, log)

            # 清空持仓
            self.system_data["positions"] = {}
            
        elif '扫描' in command_lower or '最佳' in command_lower:
            # 获取最佳池子
            best_pools = list(self.system_data["available_pools"].values())
            best_pools.sort(key=lambda x: x["apr"], reverse=True)
            
            response = "🔍 最佳池子扫描结果:\n\n"
            for i, pool in enumerate(best_pools[:3], 1):
                response += f"{i}. {pool['pair']} ({pool['chain']})\n"
                response += f"   💰 TVL: ${pool['tvl']/1000000:.1f}M\n"
                response += f"   📈 APR: {pool['apr']:.1f}% | APY: {pool['apy']:.1f}%\n"
                response += f"   🔄 24h交易量: ${pool['volume_24h']/1000:.0f}K\n"
                response += f"   🛡️ 风险: {pool['risk_score']}/100\n"
                response += f"   💡 建议: {pool['recommendation']}\n\n"
                
        elif 'usd1' in command_lower or 'USD1' in command_lower:
            response = "💰 BNB/USD1 池子详情:\n\n"
            usd1_pool = None
            for pool in self.system_data["available_pools"].values():
                if pool["pair"] == "BNB/USD1":
                    usd1_pool = pool
                    break
            
            if usd1_pool:
                response += f"💰 TVL: ${usd1_pool['tvl']/1000000:.1f}M\n"
                response += f"📈 APR: {usd1_pool['apr']:.1f}% | APY: {usd1_pool['apy']:.1f}%\n"
                response += f"🔄 24h交易量: ${usd1_pool['volume_24h']/1000:.0f}K\n"
                response += f"💎 手续费率: {usd1_pool['fee_tier']:.2f}%\n"
                response += f"🛡️ 风险评分: {usd1_pool['risk_score']}/100\n"
                response += f"💡 建议: {usd1_pool['recommendation']}\n"
                response += f"🎯 USD1是新兴稳定币，收益率较高但需注意风险"
            else:
                response += "❌ 未找到BNB/USD1池子信息"
                
        else:
            response = "🤖 收到指令，正在处理..."
        
        return response
    
    async def run_dashboard(self):
        """运行优化Dashboard"""
        with Live(
            self.create_dashboard_layout(),
            refresh_per_second=2,
            console=self.console
        ) as live:
            
            # 启动输入线程
            def input_thread():
                while True:
                    try:
                        user_input = input()
                        self.user_input_queue.put(user_input)
                    except EOFError:
                        break
            
            input_thread_obj = threading.Thread(target=input_thread, daemon=True)
            input_thread_obj.start()
            
            while True:
                try:
                    # 更新数据
                    self.update_data()
                    
                    # 检查用户输入
                    try:
                        user_input = self.user_input_queue.get_nowait()
                        if user_input.lower() in ['quit', 'exit', '退出']:
                            break
                        
                        # 处理AI指令
                        response = await self.process_ai_command(user_input)
                        self.chat_history.append({"role": "user", "content": user_input})
                        self.chat_history.append({"role": "assistant", "content": response})
                        
                    except queue.Empty:
                        pass
                    
                    # 更新显示
                    live.update(self.create_dashboard_layout())
                    await asyncio.sleep(0.5)
                    
                except KeyboardInterrupt:
                    break

async def main():
    """主函数"""
    dashboard = DyFlowOptimizedDashboard()
    console.print("[bold green]🚀 DyFlow 优化Dashboard 启动中...[/bold green]")
    console.print("[yellow]💡 重新规划布局：更大的池子扫描面板，简化系统信息，新增USD1交易对[/yellow]")
    console.print()
    
    await dashboard.run_dashboard()
    
    console.print("\n[bold green]👋 感谢使用DyFlow Dashboard！[/bold green]")

if __name__ == "__main__":
    asyncio.run(main())
