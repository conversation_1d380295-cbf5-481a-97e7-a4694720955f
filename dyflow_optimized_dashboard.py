#!/usr/bin/env python3
"""
DyFlow优化Dashboard - 重新规划布局
更大的池子扫描面板，简化系统信息，添加USD1交易对
"""

import asyncio
from datetime import datetime
import random
import threading
import queue

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.prompt import Prompt
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import box
from rich.align import Align

console = Console()

class DyFlowOptimizedDashboard:
    """DyFlow优化Dashboard"""

    def generate_bsc_pools(self):
        """生成25个BSC池子数据 - 包含高中低风险"""
        pools = {}
        pairs = ["BNB/USDC", "BNB/USDT", "BNB/USD1"]
        protocols = ["PancakeSwap V3", "PancakeSwap V2", "Uniswap V3", "SushiSwap"]

        for i in range(25):
            pair = random.choice(pairs)
            protocol = random.choice(protocols)

            # 生成不同风险等级的池子
            if i < 8:  # 低风险池子 (8个)
                risk_score = random.randint(10, 20)
                apr = random.uniform(15, 30)
                tvl = random.uniform(1000000, 5000000)
                volume = random.uniform(500000, 2000000)
                recommendation = "BUY"
            elif i < 17:  # 中风险池子 (9个)
                risk_score = random.randint(21, 35)
                apr = random.uniform(25, 45)
                tvl = random.uniform(500000, 2000000)
                volume = random.uniform(200000, 1000000)
                recommendation = random.choice(["BUY", "HOLD"])
            else:  # 高风险池子 (8个)
                risk_score = random.randint(36, 50)
                apr = random.uniform(40, 80)
                tvl = random.uniform(100000, 800000)
                volume = random.uniform(50000, 500000)
                recommendation = random.choice(["HOLD", "AVOID"])

            pool_id = f"BSC_{pair.replace('/', '_')}_{protocol.replace(' ', '_')}_{i+1:03d}"
            pools[pool_id] = {
                "chain": "BSC",
                "pair": pair,
                "protocol": protocol,
                "tvl": tvl,
                "volume_24h": volume,
                "apr": apr,
                "apy": apr * 1.15,
                "fee_tier": random.choice([0.05, 0.25, 0.30, 1.0]),
                "risk_score": risk_score,
                "recommendation": recommendation
            }

        return pools

    def generate_solana_pools(self):
        """生成25个Solana池子数据 - 包含高中低风险"""
        pools = {}
        pairs = ["SOL/USDC"]
        protocols = ["Meteora DLMM", "Orca Whirlpool", "Raydium CLMM", "Lifinity"]

        for i in range(25):
            pair = random.choice(pairs)
            protocol = random.choice(protocols)

            # 生成不同风险等级的池子
            if i < 8:  # 低风险池子 (8个)
                risk_score = random.randint(10, 20)
                apr = random.uniform(20, 35)
                tvl = random.uniform(1500000, 6000000)
                volume = random.uniform(800000, 3000000)
                recommendation = "BUY"
            elif i < 17:  # 中风险池子 (9个)
                risk_score = random.randint(21, 35)
                apr = random.uniform(30, 55)
                tvl = random.uniform(600000, 2500000)
                volume = random.uniform(300000, 1500000)
                recommendation = random.choice(["BUY", "HOLD"])
            else:  # 高风险池子 (8个)
                risk_score = random.randint(36, 50)
                apr = random.uniform(50, 100)
                tvl = random.uniform(150000, 1000000)
                volume = random.uniform(80000, 800000)
                recommendation = random.choice(["HOLD", "AVOID"])

            pool_id = f"SOL_{pair.replace('/', '_')}_{protocol.replace(' ', '_')}_{i+1:03d}"
            pools[pool_id] = {
                "chain": "Solana",
                "pair": pair,
                "protocol": protocol,
                "tvl": tvl,
                "volume_24h": volume,
                "apr": apr,
                "apy": apr * 1.15,
                "fee_tier": random.choice([0.25, 0.30, 0.50]),
                "risk_score": risk_score,
                "recommendation": recommendation
            }

        return pools

    def __init__(self):
        self.console = console
        self.user_input_queue = queue.Queue()
        self.chat_history = []
        
        # 系统数据
        self.system_data = {
            "timestamp": datetime.now(),
            "agent_logs": [
                {
                    "time": "2024-01-18 20:11:25",
                    "level": "INFO",
                    "agent": "Risk Sentinel",
                    "message": "监控到BNB/USDT持仓盈亏转正，风险等级降低",
                    "action": "调整监控频率"
                },
                {
                    "time": "2024-01-18 20:11:20",
                    "level": "SUCCESS",
                    "agent": "LP Manager",
                    "message": "成功收取SOL/USDC池子手续费 $278",
                    "action": "手续费收取"
                },
                {
                    "time": "2024-01-18 20:11:15",
                    "level": "WARNING",
                    "agent": "Pool Scanner",
                    "message": "发现BNB/USD1池子APR异常上涨至41.9%",
                    "action": "深度分析"
                },
                {
                    "time": "2024-01-18 20:11:10",
                    "level": "INFO",
                    "agent": "Price Monitor",
                    "message": "SOL价格突破$140，触发重新平衡条件",
                    "action": "价格监控"
                },
                {
                    "time": "2024-01-18 20:11:05",
                    "level": "ERROR",
                    "agent": "Transaction Manager",
                    "message": "BNB/USD1平仓交易失败，Gas费用不足",
                    "action": "重试交易"
                },
                {
                    "time": "2024-01-18 20:11:00",
                    "level": "SUCCESS",
                    "agent": "Yield Optimizer",
                    "message": "自动调整SOL/USDC价格范围，预期APR提升5%",
                    "action": "范围优化"
                },
                {
                    "time": "2024-01-18 20:10:55",
                    "level": "INFO",
                    "agent": "Market Analyzer",
                    "message": "检测到Solana网络拥堵，延迟执行交易",
                    "action": "网络监控"
                },
                {
                    "time": "2024-01-18 20:10:50",
                    "level": "WARNING",
                    "agent": "Risk Sentinel",
                    "message": "无常损失超过阈值-3%，建议关注",
                    "action": "风险评估"
                }
            ],
            "trading_logs": [
                {
                    "time": "2024-01-18 19:45:23",
                    "action": "开仓",
                    "pair": "SOL/USDC",
                    "amount": "$12,000",
                    "price": "$140.25",
                    "status": "✅ 成功",
                    "tx_hash": "5xKL...9mN2"
                },
                {
                    "time": "2024-01-18 18:30:15",
                    "action": "增加流动性",
                    "pair": "BNB/USDC",
                    "amount": "$2,500",
                    "price": "$680.50",
                    "status": "✅ 成功",
                    "tx_hash": "0xAB...CD12"
                },
                {
                    "time": "2024-01-18 17:22:08",
                    "action": "收取手续费",
                    "pair": "BNB/USDT",
                    "amount": "$45.20",
                    "price": "-",
                    "status": "✅ 成功",
                    "tx_hash": "0x12...EF34"
                },
                {
                    "time": "2024-01-18 16:15:42",
                    "action": "调整范围",
                    "pair": "SOL/USDC",
                    "amount": "$8,500",
                    "price": "$138.90",
                    "status": "⏳ 处理中",
                    "tx_hash": "7yMN...8pQ4"
                },
                {
                    "time": "2024-01-18 15:08:30",
                    "action": "平仓",
                    "pair": "BNB/USD1",
                    "amount": "$3,200",
                    "price": "$675.25",
                    "status": "❌ 失败",
                    "tx_hash": "0x56...GH78"
                }
            ],
            "wallets": {
                "BSC主钱包": {
                    "address": "******************************************",
                    "balances": {
                        "BNB": {"amount": 2.45, "usd_value": 1666.50},
                        "USDC": {"amount": 1250.30, "usd_value": 1250.30},
                        "USDT": {"amount": 890.75, "usd_value": 890.75},
                        "USD1": {"amount": 650.20, "usd_value": 650.20}
                    },
                    "total_usd": 4457.75
                },
                "Solana主钱包": {
                    "address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
                    "balances": {
                        "SOL": {"amount": 15.67, "usd_value": 2194.25},
                        "USDC": {"amount": 2100.50, "usd_value": 2100.50}
                    },
                    "total_usd": 4294.75
                }
            },
            "positions": {
                "BSC_BNB_USDC_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDC",
                    "liquidity_value": 8500.00,
                    "current_pnl": 320.50,
                    "pnl_percentage": 3.77,
                    "fees_earned": 45.20,
                    "current_apr": 24.5,
                    "status": "🟢 活跃",
                    "entry_time": "2024-01-15 10:30:00"
                },
                "BSC_BNB_USDT_001": {
                    "chain": "BSC",
                    "pair": "BNB/USDT",
                    "liquidity_value": 6200.00,
                    "current_pnl": -85.30,
                    "pnl_percentage": -1.38,
                    "fees_earned": 28.90,
                    "current_apr": 21.2,
                    "status": "🟡 监控",
                    "entry_time": "2024-01-16 14:20:00"
                },
                "SOL_SOL_USDC_001": {
                    "chain": "Solana",
                    "pair": "SOL/USDC",
                    "liquidity_value": 12000.00,
                    "current_pnl": 480.75,
                    "pnl_percentage": 4.01,
                    "fees_earned": 67.40,
                    "current_apr": 32.8,
                    "status": "🟢 活跃",
                    "entry_time": "2024-01-17 09:15:00"
                }
            },
            "bsc_pools": self.generate_bsc_pools(),
            "solana_pools": self.generate_solana_pools()
        }
    
    def create_header_panel(self):
        """创建简化的头部面板"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 计算总资产
        total_wallet_value = sum(wallet["total_usd"] for wallet in self.system_data["wallets"].values())
        total_lp_value = sum(pos["liquidity_value"] for pos in self.system_data["positions"].values())
        total_pnl = sum(pos["current_pnl"] for pos in self.system_data["positions"].values())
        total_fees = sum(pos["fees_earned"] for pos in self.system_data["positions"].values())
        total_value = total_wallet_value + total_lp_value
        
        header_content = f"""
[bold blue]🚀 DyFlow 实时监控Dashboard[/bold blue]                                                    [dim]{current_time}[/dim]

[green]💰 总资产: ${total_value:,.2f}[/green]  |  [blue]📊 LP价值: ${total_lp_value:,.2f}[/blue]  |  [yellow]💵 钱包余额: ${total_wallet_value:,.2f}[/yellow]  |  [magenta]📈 总盈亏: ${total_pnl:+,.2f}[/magenta]  |  [cyan]💎 手续费: ${total_fees:,.2f}[/cyan]
        """.strip()
        
        return Panel(
            header_content,
            box=box.DOUBLE,
            style="blue"
        )
    
    def create_wallets_table(self):
        """创建钱包状态表"""
        table = Table(title="💼 钱包资产", box=box.ROUNDED)
        table.add_column("钱包", style="cyan", width=10)
        table.add_column("地址", style="yellow", width=10)
        table.add_column("主币", style="green", justify="right", width=10)
        table.add_column("USDC", style="blue", justify="right", width=8)
        table.add_column("USDT", style="magenta", justify="right", width=8)
        table.add_column("USD1", style="bright_blue", justify="right", width=8)
        table.add_column("总价值", style="bold green", justify="right", width=10)
        
        for wallet_name, wallet_info in self.system_data["wallets"].items():
            address = wallet_info["address"]
            short_address = f"{address[:6]}...{address[-4:]}"
            
            balances = wallet_info["balances"]
            if "BNB" in balances:
                main_coin = f"{balances['BNB']['amount']:.2f} BNB"
                usdt_amount = f"{balances['USDT']['amount']:.2f}"
                usd1_amount = f"{balances.get('USD1', {'amount': 0})['amount']:.2f}"
            else:
                main_coin = f"{balances['SOL']['amount']:.2f} SOL"
                usdt_amount = "-"
                usd1_amount = "-"
            
            table.add_row(
                wallet_name,
                short_address,
                main_coin,
                f"{balances['USDC']['amount']:.2f}",
                usdt_amount,
                usd1_amount,
                f"${wallet_info['total_usd']:,.2f}"
            )
        
        return table
    
    def create_positions_table(self):
        """创建LP持仓表 - 移除健康度"""
        table = Table(title="📊 LP持仓状态", box=box.ROUNDED)
        table.add_column("链", style="cyan", width=6)
        table.add_column("交易对", style="yellow", width=10)
        table.add_column("流动性价值", style="green", justify="right", width=10)
        table.add_column("APR", style="magenta", justify="right", width=6)
        table.add_column("盈亏", justify="right", width=10)
        table.add_column("手续费", style="blue", justify="right", width=8)
        table.add_column("状态", justify="center", width=8)
        table.add_column("持仓时间", style="dim", width=8)
        
        for pos_id, pos_info in self.system_data["positions"].items():
            pnl = pos_info["current_pnl"]
            pnl_color = "green" if pnl >= 0 else "red"
            pnl_text = f"+${pnl:.2f}" if pnl >= 0 else f"${pnl:.2f}"
            
            apr = pos_info["current_apr"]
            apr_color = "green" if apr >= 30 else "yellow" if apr >= 20 else "red"
            
            # 计算持仓时间
            entry_time = datetime.strptime(pos_info["entry_time"], "%Y-%m-%d %H:%M:%S")
            holding_days = (datetime.now() - entry_time).days
            
            table.add_row(
                pos_info["chain"],
                pos_info["pair"],
                f"${pos_info['liquidity_value']:,.0f}",
                Text(f"{apr:.1f}%", style=apr_color),
                Text(pnl_text, style=pnl_color),
                f"${pos_info['fees_earned']:.2f}",
                pos_info["status"],
                f"{holding_days}天"
            )
        
        return table
    
    def create_bsc_pools_panel(self):
        """创建BSC池子扫描面板"""
        table = Table(title="🟡 BSC池子扫描 (25个池子 - 高中低风险)", box=box.ROUNDED, show_header=True)
        table.add_column("交易对", style="yellow", width=10)
        table.add_column("协议", style="blue", width=12)
        table.add_column("TVL", style="green", justify="right", width=8)
        table.add_column("24h量", style="bright_blue", justify="right", width=8)
        table.add_column("APR", style="magenta", justify="right", width=6)
        table.add_column("风险", justify="center", width=4)
        table.add_column("建议", justify="center", width=6)

        # 获取BSC池子并按APR排序
        bsc_pools = list(self.system_data["bsc_pools"].values())
        bsc_pools.sort(key=lambda x: x["apr"], reverse=True)

        # 显示前15个最佳池子
        for pool in bsc_pools[:15]:
            # 风险等级
            risk_score = pool["risk_score"]
            if risk_score <= 20:
                risk_level = "低"
                risk_color = "green"
            elif risk_score <= 35:
                risk_level = "中"
                risk_color = "yellow"
            else:
                risk_level = "高"
                risk_color = "red"

            # 建议颜色
            rec = pool["recommendation"]
            rec_color = {
                "BUY": "green",
                "HOLD": "yellow",
                "AVOID": "red"
            }.get(rec, "white")

            table.add_row(
                pool["pair"],
                pool["protocol"][:12],  # 截断协议名
                f"${pool['tvl']/1000000:.1f}M",
                f"${pool['volume_24h']/1000:.0f}K",
                f"{pool['apr']:.0f}%",
                Text(risk_level, style=risk_color),
                Text(rec, style=rec_color)
            )

        return table

    def create_solana_pools_panel(self):
        """创建Solana池子扫描面板"""
        table = Table(title="🟣 Solana池子扫描 (25个池子 - 高中低风险)", box=box.ROUNDED, show_header=True)
        table.add_column("交易对", style="yellow", width=10)
        table.add_column("协议", style="blue", width=12)
        table.add_column("TVL", style="green", justify="right", width=8)
        table.add_column("24h量", style="bright_blue", justify="right", width=8)
        table.add_column("APR", style="magenta", justify="right", width=6)
        table.add_column("风险", justify="center", width=4)
        table.add_column("建议", justify="center", width=6)

        # 获取Solana池子并按APR排序
        solana_pools = list(self.system_data["solana_pools"].values())
        solana_pools.sort(key=lambda x: x["apr"], reverse=True)

        # 显示前15个最佳池子
        for pool in solana_pools[:15]:
            # 风险等级
            risk_score = pool["risk_score"]
            if risk_score <= 20:
                risk_level = "低"
                risk_color = "green"
            elif risk_score <= 35:
                risk_level = "中"
                risk_color = "yellow"
            else:
                risk_level = "高"
                risk_color = "red"

            # 建议颜色
            rec = pool["recommendation"]
            rec_color = {
                "BUY": "green",
                "HOLD": "yellow",
                "AVOID": "red"
            }.get(rec, "white")

            table.add_row(
                pool["pair"],
                pool["protocol"][:12],  # 截断协议名
                f"${pool['tvl']/1000000:.1f}M",
                f"${pool['volume_24h']/1000:.0f}K",
                f"{pool['apr']:.0f}%",
                Text(risk_level, style=risk_color),
                Text(rec, style=rec_color)
            )

        return table

    def create_trading_logs_panel(self):
        """创建交易日志面板"""
        table = Table(title="📋 交易日志 (最近执行记录)", box=box.ROUNDED, show_header=True)
        table.add_column("时间", style="dim", width=8)
        table.add_column("操作", style="cyan", width=8)
        table.add_column("交易对", style="yellow", width=8)
        table.add_column("金额", style="green", justify="right", width=8)
        table.add_column("价格", style="blue", justify="right", width=8)
        table.add_column("状态", justify="center", width=6)
        table.add_column("哈希", style="dim", width=8)

        for log in self.system_data["trading_logs"]:
            # 状态颜色
            status = log["status"]
            if "成功" in status:
                status_color = "green"
            elif "处理中" in status:
                status_color = "yellow"
            else:
                status_color = "red"

            # 操作颜色
            action = log["action"]
            if action in ["开仓", "增加流动性"]:
                action_color = "green"
            elif action in ["平仓", "减少流动性"]:
                action_color = "red"
            else:
                action_color = "blue"

            table.add_row(
                log["time"].split(" ")[1][:5],  # 只显示时:分
                Text(action, style=action_color),
                log["pair"],
                log["amount"][:6] + "..." if len(log["amount"]) > 8 else log["amount"],
                log["price"][:6] + "..." if len(log["price"]) > 8 else log["price"],
                Text(status[:4], style=status_color),
                log["tx_hash"][:6] + "..."
            )

        return table

    def create_agent_logs_panel(self):
        """创建Agent日志面板"""
        table = Table(title="🤖 Agent执行日志 (智能代理活动)", box=box.ROUNDED, show_header=True)
        table.add_column("时间", style="dim", width=8)
        table.add_column("等级", justify="center", width=8)
        table.add_column("Agent", style="cyan", width=15)
        table.add_column("消息", style="white", width=35)
        table.add_column("动作", style="yellow", width=12)

        for log in self.system_data["agent_logs"]:
            # 等级颜色
            level = log["level"]
            if level == "SUCCESS":
                level_color = "green"
                level_icon = "✅"
            elif level == "WARNING":
                level_color = "yellow"
                level_icon = "⚠️"
            elif level == "ERROR":
                level_color = "red"
                level_icon = "❌"
            else:  # INFO
                level_color = "blue"
                level_icon = "ℹ️"

            # Agent颜色
            agent = log["agent"]
            if "Risk" in agent:
                agent_color = "red"
            elif "LP Manager" in agent or "Yield" in agent:
                agent_color = "green"
            elif "Scanner" in agent or "Monitor" in agent:
                agent_color = "blue"
            else:
                agent_color = "cyan"

            table.add_row(
                log["time"].split(" ")[1][:5],  # 只显示时:分
                Text(f"{level_icon} {level}", style=level_color),
                Text(agent, style=agent_color),
                log["message"][:35] + "..." if len(log["message"]) > 35 else log["message"],
                log["action"]
            )

        return table

    def create_dashboard_layout(self):
        """创建优化的Dashboard布局 - 统一大小配置"""
        layout = Layout()

        # 主要布局：头部 + 主体 + 底部
        layout.split_column(
            Layout(name="header", size=5),
            Layout(name="main"),
            Layout(name="footer", size=5)  # 统一底部高度
        )

        # 主体分为左右两部分 - 调整比例让右侧更大
        layout["main"].split_row(
            Layout(name="left", ratio=3),   # 左侧：钱包+持仓+扫描
            Layout(name="right", ratio=2)   # 右侧：交易日志+Agent日志
        )

        # 左侧分为四部分 - 统一比例
        layout["left"].split_column(
            Layout(name="wallets", ratio=1),      # 钱包资产
            Layout(name="positions", ratio=2),    # LP持仓状态
            Layout(name="bsc_pools", ratio=2),    # BSC池子扫描
            Layout(name="solana_pools", ratio=2)  # Solana池子扫描
        )

        # 右侧分为两部分 - 交易日志和Agent日志
        layout["right"].split_column(
            Layout(name="trading_logs", ratio=1),  # 交易日志
            Layout(name="agent_logs", ratio=1)     # Agent日志
        )

        # 填充所有内容
        layout["header"].update(self.create_header_panel())
        layout["wallets"].update(self.create_wallets_table())
        layout["positions"].update(self.create_positions_table())
        layout["bsc_pools"].update(self.create_bsc_pools_panel())
        layout["solana_pools"].update(self.create_solana_pools_panel())
        layout["trading_logs"].update(self.create_trading_logs_panel())
        layout["agent_logs"].update(self.create_agent_logs_panel())

        # 底部状态栏 - 简化
        footer_content = """
[bold green]系统状态:[/bold green] 🟢 正常运行 | [bold blue]连接状态:[/bold blue] BSC✅ Solana✅ AI✅ | [bold yellow]监控模式:[/bold yellow] 🟢 启用

[dim]💬 在下方输入AI指令 | 支持: 出清持仓, 扫描池子, 查看USD1详情 | 按Ctrl+C退出[/dim]
        """.strip()

        layout["footer"].update(Panel(
            footer_content,
            title="💡 系统状态 & AI指令输入",
            style="yellow"
        ))

        return layout
    
    def update_data(self):
        """更新实时数据"""
        # 更新时间戳
        self.system_data["timestamp"] = datetime.now()

        # 更新持仓数据
        for pos_id in self.system_data["positions"]:
            pos = self.system_data["positions"][pos_id]

            # 更新盈亏
            change = random.uniform(-5, 8)
            pos["current_pnl"] += change
            pos["pnl_percentage"] = (pos["current_pnl"] / pos["liquidity_value"]) * 100

            # 更新手续费
            pos["fees_earned"] += random.uniform(0, 0.3)

            # 更新APR
            apr_change = random.uniform(-0.5, 1.0)
            pos["current_apr"] = max(5, pos["current_apr"] + apr_change)

        # 更新BSC池子数据
        for pool_id in self.system_data["bsc_pools"]:
            pool = self.system_data["bsc_pools"][pool_id]

            # 更新APR/APY
            apr_change = random.uniform(-2, 3)
            pool["apr"] = max(5, pool["apr"] + apr_change)
            pool["apy"] = pool["apr"] * 1.15

            # 更新TVL和交易量
            tvl_change = random.uniform(-0.05, 0.08)
            pool["tvl"] = max(50000, pool["tvl"] * (1 + tvl_change))

            volume_change = random.uniform(-0.1, 0.15)
            pool["volume_24h"] = max(10000, pool["volume_24h"] * (1 + volume_change))

            # 更新风险评分
            risk_change = random.uniform(-1, 1)
            pool["risk_score"] = max(10, min(50, pool["risk_score"] + risk_change))

            # 根据APR和风险更新建议
            if pool["apr"] >= 50 and pool["risk_score"] <= 25:
                pool["recommendation"] = "BUY"
            elif pool["apr"] >= 30 and pool["risk_score"] <= 35:
                pool["recommendation"] = "HOLD"
            elif pool["risk_score"] > 40:
                pool["recommendation"] = "AVOID"

        # 更新Solana池子数据
        for pool_id in self.system_data["solana_pools"]:
            pool = self.system_data["solana_pools"][pool_id]

            # 更新APR/APY
            apr_change = random.uniform(-2, 4)
            pool["apr"] = max(5, pool["apr"] + apr_change)
            pool["apy"] = pool["apr"] * 1.15

            # 更新TVL和交易量
            tvl_change = random.uniform(-0.04, 0.1)
            pool["tvl"] = max(50000, pool["tvl"] * (1 + tvl_change))

            volume_change = random.uniform(-0.12, 0.18)
            pool["volume_24h"] = max(10000, pool["volume_24h"] * (1 + volume_change))

            # 更新风险评分
            risk_change = random.uniform(-1, 1)
            pool["risk_score"] = max(10, min(50, pool["risk_score"] + risk_change))

            # 根据APR和风险更新建议
            if pool["apr"] >= 60 and pool["risk_score"] <= 25:
                pool["recommendation"] = "BUY"
            elif pool["apr"] >= 40 and pool["risk_score"] <= 35:
                pool["recommendation"] = "HOLD"
            elif pool["risk_score"] > 40:
                pool["recommendation"] = "AVOID"

        # 随机更新交易日志状态
        if random.random() < 0.1:  # 10%概率更新日志
            for log in self.system_data["trading_logs"]:
                if "处理中" in log["status"]:
                    log["status"] = "✅ 成功"
                    break

        # 偶尔添加新的交易日志
        if random.random() < 0.05:  # 5%概率添加新日志
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            actions = ["收取手续费", "调整范围", "重新平衡"]
            pairs = ["BNB/USDC", "BNB/USDT", "SOL/USDC", "BNB/USD1"]

            new_log = {
                "time": current_time,
                "action": random.choice(actions),
                "pair": random.choice(pairs),
                "amount": f"${random.randint(100, 5000)}",
                "price": f"${random.randint(100, 800)}.{random.randint(10, 99)}",
                "status": "⏳ 处理中",
                "tx_hash": f"0x{random.randint(10, 99)}...{random.randint(10, 99)}"
            }

            # 添加到开头，保持最新的在前
            self.system_data["trading_logs"].insert(0, new_log)

            # 保持最多10条记录
            if len(self.system_data["trading_logs"]) > 10:
                self.system_data["trading_logs"] = self.system_data["trading_logs"][:10]

        # 偶尔添加新的Agent日志
        if random.random() < 0.08:  # 8%概率添加新Agent日志
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Agent日志模板
            agent_templates = [
                {
                    "level": "INFO",
                    "agent": "Risk Sentinel",
                    "message": "持仓风险评估完成，所有持仓在安全范围内",
                    "action": "风险监控"
                },
                {
                    "level": "SUCCESS",
                    "agent": "LP Manager",
                    "message": "自动收取手续费操作成功",
                    "action": "手续费收取"
                },
                {
                    "level": "WARNING",
                    "agent": "Pool Scanner",
                    "message": "检测到高APR池子，建议深度分析",
                    "action": "池子分析"
                },
                {
                    "level": "INFO",
                    "agent": "Price Monitor",
                    "message": "价格波动在正常范围内",
                    "action": "价格监控"
                },
                {
                    "level": "SUCCESS",
                    "agent": "Yield Optimizer",
                    "message": "成功优化价格范围，APR提升2%",
                    "action": "收益优化"
                },
                {
                    "level": "WARNING",
                    "agent": "Market Analyzer",
                    "message": "检测到网络拥堵，延迟执行交易",
                    "action": "网络分析"
                }
            ]

            new_agent_log = random.choice(agent_templates).copy()
            new_agent_log["time"] = current_time

            # 添加到开头，保持最新的在前
            self.system_data["agent_logs"].insert(0, new_agent_log)

            # 保持最多8条记录
            if len(self.system_data["agent_logs"]) > 8:
                self.system_data["agent_logs"] = self.system_data["agent_logs"][:8]
    
    async def process_ai_command(self, command: str) -> str:
        """处理AI指令"""
        command_lower = command.lower()
        
        if '出清' in command_lower or '清仓' in command_lower:
            response = "✅ 正在执行出清操作...\n\n"
            response += "🔄 关闭BNB/USDC持仓 → 获得12.5 BNB\n"
            response += "🔄 关闭BNB/USDT持仓 → 获得9.2 BNB\n"
            response += "🔄 关闭SOL/USDC持仓 → 获得85.5 SOL\n\n"
            response += "✅ 出清完成！总计: 21.7 BNB + 85.5 SOL"

            # 添加交易日志
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            for pair in ["BNB/USDC", "BNB/USDT", "SOL/USDC"]:
                log = {
                    "time": current_time,
                    "action": "平仓",
                    "pair": pair,
                    "amount": "$8,500" if "BNB" in pair else "$12,000",
                    "price": "$680.50" if "BNB" in pair else "$140.25",
                    "status": "✅ 成功",
                    "tx_hash": f"0x{random.randint(10, 99)}...{random.randint(10, 99)}"
                }
                self.system_data["trading_logs"].insert(0, log)

            # 清空持仓
            self.system_data["positions"] = {}
            
        elif '扫描' in command_lower or '最佳' in command_lower:
            # 获取最佳池子 - 合并BSC和Solana
            all_pools = []
            all_pools.extend(self.system_data["bsc_pools"].values())
            all_pools.extend(self.system_data["solana_pools"].values())
            all_pools.sort(key=lambda x: x["apr"], reverse=True)

            response = "🔍 最佳池子扫描结果 (BSC + Solana):\n\n"
            for i, pool in enumerate(all_pools[:5], 1):
                response += f"{i}. {pool['pair']} ({pool['chain']})\n"
                response += f"   🏛️ 协议: {pool['protocol']}\n"
                response += f"   💰 TVL: ${pool['tvl']/1000000:.1f}M\n"
                response += f"   📈 APR: {pool['apr']:.1f}% | APY: {pool['apy']:.1f}%\n"
                response += f"   🔄 24h交易量: ${pool['volume_24h']/1000:.0f}K\n"
                response += f"   🛡️ 风险: {pool['risk_score']:.0f}/100\n"
                response += f"   💡 建议: {pool['recommendation']}\n\n"
                
        elif 'usd1' in command_lower or 'USD1' in command_lower:
            response = "💰 BNB/USD1 池子详情:\n\n"
            usd1_pools = []
            for pool in self.system_data["bsc_pools"].values():
                if pool["pair"] == "BNB/USD1":
                    usd1_pools.append(pool)

            if usd1_pools:
                # 按APR排序，显示最佳的3个
                usd1_pools.sort(key=lambda x: x["apr"], reverse=True)
                for i, pool in enumerate(usd1_pools[:3], 1):
                    response += f"🏆 第{i}名 - {pool['protocol']}\n"
                    response += f"💰 TVL: ${pool['tvl']/1000000:.1f}M\n"
                    response += f"📈 APR: {pool['apr']:.1f}% | APY: {pool['apy']:.1f}%\n"
                    response += f"🔄 24h交易量: ${pool['volume_24h']/1000:.0f}K\n"
                    response += f"💎 手续费率: {pool['fee_tier']:.2f}%\n"
                    response += f"🛡️ 风险评分: {pool['risk_score']:.0f}/100\n"
                    response += f"💡 建议: {pool['recommendation']}\n\n"
                response += "🎯 USD1是新兴稳定币，收益率较高但需注意风险"
            else:
                response += "❌ 未找到BNB/USD1池子信息"
                
        else:
            response = "🤖 收到指令，正在处理..."
        
        return response
    
    async def run_dashboard(self):
        """运行优化Dashboard"""
        with Live(
            self.create_dashboard_layout(),
            refresh_per_second=2,
            console=self.console
        ) as live:
            
            # 启动输入线程
            def input_thread():
                while True:
                    try:
                        user_input = input()
                        self.user_input_queue.put(user_input)
                    except EOFError:
                        break
            
            input_thread_obj = threading.Thread(target=input_thread, daemon=True)
            input_thread_obj.start()
            
            while True:
                try:
                    # 更新数据
                    self.update_data()
                    
                    # 检查用户输入
                    try:
                        user_input = self.user_input_queue.get_nowait()
                        if user_input.lower() in ['quit', 'exit', '退出']:
                            break
                        
                        # 处理AI指令
                        response = await self.process_ai_command(user_input)
                        self.chat_history.append({"role": "user", "content": user_input})
                        self.chat_history.append({"role": "assistant", "content": response})
                        
                    except queue.Empty:
                        pass
                    
                    # 更新显示
                    live.update(self.create_dashboard_layout())
                    await asyncio.sleep(0.5)
                    
                except KeyboardInterrupt:
                    break

async def main():
    """主函数"""
    dashboard = DyFlowOptimizedDashboard()
    console.print("[bold green]🚀 DyFlow 优化Dashboard 启动中...[/bold green]")
    console.print("[yellow]💡 重新规划布局：更大的池子扫描面板，简化系统信息，新增USD1交易对[/yellow]")
    console.print()
    
    await dashboard.run_dashboard()
    
    console.print("\n[bold green]👋 感谢使用DyFlow Dashboard！[/bold green]")

if __name__ == "__main__":
    asyncio.run(main())
