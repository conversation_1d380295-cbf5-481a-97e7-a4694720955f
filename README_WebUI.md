# 🚀 DyFlow WebUI Dashboard

基于Streamlit的Web界面版本，提供完整的LP策略监控和池子扫描功能。

## ✨ 功能特性

### 📊 **实时监控Dashboard**
- **💰 总资产监控**: 实时显示总资产、LP价值、钱包余额、总盈亏、手续费收入
- **💼 钱包管理**: BSC和Solana钱包资产详情
- **📊 LP持仓状态**: 实时APR、盈亏、手续费、持仓时间
- **🔄 自动刷新**: 支持1-10秒自动刷新间隔

### 🔍 **池子扫描系统**
- **🟡 BSC池子扫描**: 25个池子，包含BNB/USDC, BNB/USDT, BNB/USD1
- **🟣 Solana池子扫描**: 25个池子，主要SOL/USDC交易对
- **🎯 风险分级**: 高中低风险池子分布 (8个低风险 + 9个中风险 + 8个高风险)
- **📈 实时APR**: 动态更新的年化收益率
- **💡 智能建议**: BUY/HOLD/AVOID投资建议

### 🤖 **AI指令交互**
- **💬 智能对话**: 支持自然语言指令
- **🔍 池子扫描**: "扫描最佳池子" 获取推荐
- **💰 资产管理**: "出清持仓" 执行操作
- **📝 对话历史**: 保存最近3次对话记录

### 📋 **日志系统**
- **📋 交易日志**: 最近交易执行记录
- **🤖 Agent日志**: 智能代理活动监控
- **⚠️ 状态监控**: 成功/警告/错误状态追踪

## 🚀 快速启动

### 方法1: 使用启动脚本 (推荐)
```bash
./start_webui.sh
```

### 方法2: 手动启动
```bash
# 安装依赖
pip3 install streamlit pandas plotly numpy --user

# 启动WebUI
python3 -m streamlit run dyflow_webui.py --server.port 8501
```

## 🌐 访问地址

启动后访问: **http://localhost:8501**

## 📱 界面说明

### 🎛️ **侧边栏控制面板**
- **🔄 自动刷新**: 开启/关闭自动刷新
- **⏱️ 刷新间隔**: 1-10秒可调
- **📡 系统状态**: BSC/Solana/AI连接状态
- **💬 AI指令**: 输入自然语言指令
- **💭 对话历史**: 查看最近对话

### 📊 **主要标签页**
1. **💼 钱包 & 持仓**: 钱包资产和LP持仓状态
2. **🟡 BSC池子**: BSC链上25个池子扫描
3. **🟣 Solana池子**: Solana链上25个池子扫描  
4. **📋 交易日志**: 最近交易执行记录
5. **🤖 Agent日志**: 智能代理活动日志

## 🎯 使用示例

### AI指令示例
- `扫描最佳池子` - 获取BSC+Solana最佳5个池子
- `出清持仓` - 执行所有持仓平仓操作
- `查看USD1详情` - 获取BNB/USD1池子信息

### 数据解读
- **绿色数值**: 盈利/成功状态
- **红色数值**: 亏损/错误状态
- **黄色标记**: 警告/监控状态
- **风险等级**: 低(绿)/中(黄)/高(红)

## 🔧 技术架构

- **前端框架**: Streamlit
- **数据处理**: Pandas
- **图表展示**: Plotly
- **实时更新**: 自动刷新机制
- **响应式设计**: 支持不同屏幕尺寸

## 📈 数据特性

- **实时更新**: 所有数据每秒动态变化
- **模拟数据**: 当前使用高质量模拟数据
- **多链支持**: BSC和Solana双链监控
- **风险管理**: 智能风险评估和建议

## 🛠️ 自定义配置

可以通过修改 `dyflow_webui.py` 来自定义:
- 刷新间隔范围
- 显示的池子数量
- 风险等级阈值
- AI指令响应逻辑

## 📞 支持

如有问题或建议，请联系开发团队。

---
**DyFlow WebUI v1.0** - 24/7 自动化单边LP策略系统
