#!/usr/bin/env python3
"""
测试DyFlow Workflow完整执行
验证端到端的工作流程功能
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

async def test_agno_scheduler():
    """测试AGNO调度器基础功能"""
    print("🔧 测试AGNO调度器...")
    
    try:
        from src.core.agno_scheduler import AGNOScheduler, AgentConfig, AgentStatus
        from src.agents.base_agent import BaseAgent, AgentConfig as BaseAgentConfig
        
        # 创建模拟配置和数据库
        config = {"test": True}
        database = None  # 简化测试
        
        # 创建调度器
        scheduler = AGNOScheduler(config, database)
        print("✅ AGNO调度器创建成功")
        
        # 创建测试Agent
        agent_config = BaseAgentConfig(
            name="TestAgent",
            model_provider="ollama",
            model_name="qwen2.5:3b",
            enable_memory=False
        )
        
        test_agent = BaseAgent(agent_config)
        await test_agent.initialize()
        
        # 注册Agent到调度器
        scheduler.register_agent(
            "test_agent",
            test_agent,
            dependencies=[],
            critical=False,
            max_retries=1,
            timeout_seconds=30
        )
        print("✅ Agent注册成功")
        
        # 执行简单工作流
        workflow_result = await scheduler.execute_workflow(["test_agent"])
        
        if workflow_result.status in ["completed", "partial"]:
            print("✅ 工作流执行成功")
            print(f"   状态: {workflow_result.status}")
            print(f"   完成Agent数: {len(workflow_result.completed_agents)}")
            return True
        else:
            print(f"❌ 工作流执行失败: {workflow_result.status}")
            return False
            
    except Exception as e:
        print(f"❌ AGNO调度器测试失败: {e}")
        return False

async def test_lp_monitoring_workflow():
    """测试LP监控工作流"""
    print("\n🔄 测试LP监控工作流...")
    
    try:
        # 检查Agno Framework是否可用
        try:
            from agno import Workflow
            agno_available = True
        except ImportError:
            agno_available = False
            print("⚠️  Agno Framework不可用，跳过Agno工作流测试")
            return True  # 不算失败，因为我们已经有替代方案
        
        if agno_available:
            from src.workflows.lp_monitoring_workflow import LPMonitoringWorkflow
            
            # 创建工作流实例
            workflow = LPMonitoringWorkflow()
            print("✅ LP监控工作流创建成功")
            
            # 执行简化的监控
            result = workflow.monitor_pools(chains=['bsc'], max_pools=2)
            
            if result.get('status') in ['completed', 'partial']:
                print("✅ LP监控工作流执行成功")
                print(f"   工作流ID: {result.get('workflow_id', 'N/A')}")
                return True
            else:
                print(f"❌ LP监控工作流执行失败: {result.get('error', '未知错误')}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ LP监控工作流测试失败: {e}")
        return False

async def test_dyflow_main_system():
    """测试DyFlow主系统"""
    print("\n🚀 测试DyFlow主系统...")
    
    try:
        from dyflow_main import DyFlowSystem
        
        # 创建系统实例
        dyflow = DyFlowSystem()
        print("✅ DyFlow系统创建成功")
        
        # 初始化系统
        init_success = await dyflow.initialize()
        if init_success:
            print("✅ DyFlow系统初始化成功")
        else:
            print("⚠️  DyFlow系统初始化部分成功（可能缺少某些组件）")
        
        # 执行简化的监控周期
        print("🔍 执行监控周期...")
        result = await dyflow.run_monitoring_cycle(chains=['bsc'], max_pools=2)
        
        if result.get('status') in ['completed', 'partial']:
            print("✅ 监控周期执行成功")
            print(f"   状态: {result.get('status')}")
            print(f"   工作流ID: {result.get('workflow_id', 'N/A')}")
            
            # 显示结果摘要
            results = result.get('results', {})
            if results:
                print("📊 执行结果:")
                for key, value in results.items():
                    if isinstance(value, dict) and 'status' in value:
                        print(f"   {key}: {value['status']}")
                    else:
                        print(f"   {key}: 已完成")
            
            return True
        else:
            print(f"❌ 监控周期执行失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ DyFlow主系统测试失败: {e}")
        return False

async def test_agent_integration():
    """测试Agent集成"""
    print("\n🤖 测试Agent集成...")
    
    try:
        from src.agents.planner_agno import PlannerAgnoAgent
        from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent
        from src.agents.scorer_v2_agno import ScorerV2AgnoAgent
        
        # 创建Agent配置
        agent_configs = {
            'planner': {
                'name': 'TestPlanner',
                'model_provider': 'ollama',
                'model_name': 'qwen2.5:3b',
                'enable_memory': False
            },
            'risk_sentinel': {
                'name': 'TestRiskSentinel',
                'model_provider': 'ollama',
                'model_name': 'qwen2.5:3b',
                'enable_memory': False
            },
            'scorer': {
                'name': 'TestScorer',
                'model_provider': 'ollama',
                'model_name': 'qwen2.5:3b',
                'enable_memory': False
            }
        }
        
        agents = {}
        
        # 创建和初始化Agent
        agents['planner'] = PlannerAgnoAgent(agent_configs['planner'])
        agents['risk_sentinel'] = RiskSentinelAgnoAgent(agent_configs['risk_sentinel'])
        agents['scorer'] = ScorerV2AgnoAgent(agent_configs['scorer'])
        
        print("✅ 所有Agent创建成功")
        
        # 测试Agent基础功能
        test_success = 0
        for name, agent in agents.items():
            try:
                # 简单的状态检查
                status = agent.get_status()
                if status:
                    print(f"✅ {name} Agent状态正常")
                    test_success += 1
                else:
                    print(f"⚠️  {name} Agent状态异常")
            except Exception as e:
                print(f"❌ {name} Agent测试失败: {e}")
        
        success_rate = test_success / len(agents) * 100
        print(f"📊 Agent集成成功率: {success_rate:.1f}%")
        
        return success_rate >= 80  # 80%以上算成功
        
    except Exception as e:
        print(f"❌ Agent集成测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始DyFlow Workflow执行测试")
    print("=" * 60)
    
    test_results = {}
    
    # 1. 测试AGNO调度器
    test_results['agno_scheduler'] = await test_agno_scheduler()
    
    # 2. 测试LP监控工作流
    test_results['lp_monitoring_workflow'] = await test_lp_monitoring_workflow()
    
    # 3. 测试Agent集成
    test_results['agent_integration'] = await test_agent_integration()
    
    # 4. 测试DyFlow主系统
    test_results['dyflow_main_system'] = await test_dyflow_main_system()
    
    # 计算总体结果
    print("\n" + "=" * 60)
    print("📊 Workflow执行测试结果")
    print("=" * 60)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = passed_tests / total_tests * 100
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总测试数: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {total_tests - passed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print("\n🎉 Workflow执行测试总体成功！")
        print("💡 你的DyFlow系统workflow功能基本正常")
        return True
    else:
        print("\n⚠️  Workflow执行测试需要改进")
        print("💡 建议检查失败的组件并进行修复")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
