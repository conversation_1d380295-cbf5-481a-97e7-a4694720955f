#!/usr/bin/env python3
"""
DyFlow 修复版WebUI - 去掉有问题的自动刷新，保留AI聊天功能
"""

import streamlit as st
import pandas as pd
from datetime import datetime
import random
import time

# 页面配置
st.set_page_config(
    page_title="DyFlow - 动态LP监控Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

class DyFlowFixedUI:
    """修复版WebUI类"""
    
    def __init__(self):
        self.initialize_session_state()
    
    def initialize_session_state(self):
        """初始化会话状态"""
        if 'chat_messages' not in st.session_state:
            st.session_state.chat_messages = [
                {"role": "assistant", "content": "🤖 你好！我是DyFlow AI助手，可以帮你监控LP策略、扫描池子、执行交易操作。\n\n试试问我：\n- '扫描最佳池子'\n- '查看持仓状态'\n- '风险评估'\n- 'USD1详情'"}
            ]
        if 'refresh_counter' not in st.session_state:
            st.session_state.refresh_counter = 0
    
    def generate_dynamic_data(self):
        """生成动态数据"""
        # 基于当前时间生成变化的数据
        time_factor = datetime.now().second / 60.0
        
        # 钱包数据
        wallets = {
            "BSC主钱包": {
                "地址": "0x1234...5678",
                "BNB": f"{2.45 + random.uniform(-0.1, 0.1):.2f} BNB",
                "USDC": f"{1250.30 + random.uniform(-50, 50):.2f}",
                "USDT": f"{890.75 + random.uniform(-30, 30):.2f}",
                "USD1": f"{650.20 + random.uniform(-20, 20):.2f}",
                "总价值": f"${4457.75 + random.uniform(-200, 200):.2f}"
            },
            "Solana主钱包": {
                "地址": "9WzDXw...AWWM",
                "SOL": f"{15.67 + random.uniform(-0.5, 0.5):.2f} SOL",
                "USDC": f"{2100.50 + random.uniform(-100, 100):.2f}",
                "USDT": "-",
                "USD1": "-",
                "总价值": f"${4294.75 + random.uniform(-150, 150):.2f}"
            }
        }
        
        # LP持仓数据
        positions = [
            {
                "链": "BSC",
                "交易对": "BNB/USDC",
                "流动性价值": "$8,500",
                "APR": f"{24.5 + random.uniform(-2, 3):.1f}%",
                "盈亏": f"+${320.50 + random.uniform(-20, 30):.2f}",
                "手续费": f"${45.20 + random.uniform(0, 5):.2f}",
                "状态": "🟢 活跃",
                "持仓时间": "515天"
            },
            {
                "链": "BSC",
                "交易对": "BNB/USDT",
                "流动性价值": "$6,200",
                "APR": f"{21.2 + random.uniform(-2, 3):.1f}%",
                "盈亏": f"-${85.30 - random.uniform(-10, 20):.2f}",
                "手续费": f"${28.90 + random.uniform(0, 3):.2f}",
                "状态": "🟡 监控",
                "持仓时间": "514天"
            },
            {
                "链": "Solana",
                "交易对": "SOL/USDC",
                "流动性价值": "$12,000",
                "APR": f"{32.8 + random.uniform(-2, 5):.1f}%",
                "盈亏": f"+${480.75 + random.uniform(-30, 50):.2f}",
                "手续费": f"${67.40 + random.uniform(0, 8):.2f}",
                "状态": "🟢 活跃",
                "持仓时间": "513天"
            }
        ]
        
        return wallets, positions
    
    def generate_pools(self, chain: str, count: int = 15):
        """生成池子数据"""
        pools = []
        
        if chain == "BSC":
            pairs = ["BNB/USDC", "BNB/USDT", "BNB/USD1"]
            protocols = ["PancakeSwap V3", "PancakeSwap V2", "Uniswap V3", "SushiSwap"]
        else:  # Solana
            pairs = ["SOL/USDC"]
            protocols = ["Meteora DLMM", "Orca Whirlpool", "Raydium CLMM", "Lifinity"]
        
        for i in range(count):
            # 动态APR
            base_apr = 20 + i * 2 + random.uniform(-5, 15)
            
            # 风险等级
            if i < 5:
                risk_level = "🟢 低"
            elif i < 10:
                risk_level = "🟡 中"
            else:
                risk_level = "🔴 高"
            
            pools.append({
                "交易对": random.choice(pairs),
                "协议": random.choice(protocols),
                "TVL": f"${random.uniform(0.5, 5.0):.1f}M",
                "24h交易量": f"${random.uniform(100, 2000):.0f}K",
                "APR": f"{base_apr:.1f}%",
                "风险": risk_level,
                "建议": random.choice(["🟢 BUY", "🟡 HOLD", "🔴 AVOID"])
            })
        
        # 按APR排序
        pools.sort(key=lambda x: float(x["APR"].replace("%", "")), reverse=True)
        return pools
    
    def process_chat_message(self, user_input: str) -> str:
        """处理聊天消息"""
        user_input_lower = user_input.lower()
        
        if "扫描" in user_input_lower or "池子" in user_input_lower or "最佳" in user_input_lower:
            bsc_pools = self.generate_pools("BSC", 3)
            sol_pools = self.generate_pools("Solana", 2)
            
            response = "🔍 **最佳池子扫描结果** (实时数据):\n\n"
            response += "**🟡 BSC热门池子:**\n"
            for i, pool in enumerate(bsc_pools, 1):
                response += f"{i}. {pool['交易对']} ({pool['协议']})\n"
                response += f"   📈 APR: {pool['APR']} | 💰 TVL: {pool['TVL']}\n"
                response += f"   🎯 建议: {pool['建议']}\n\n"
            
            response += "**🟣 Solana热门池子:**\n"
            for i, pool in enumerate(sol_pools, 1):
                response += f"{i}. {pool['交易对']} ({pool['协议']})\n"
                response += f"   📈 APR: {pool['APR']} | 💰 TVL: {pool['TVL']}\n"
                response += f"   🎯 建议: {pool['建议']}\n\n"
                
        elif "持仓" in user_input_lower or "状态" in user_input_lower:
            _, positions = self.generate_dynamic_data()
            response = "📊 **当前LP持仓状态** (实时数据):\n\n"
            for pos in positions:
                response += f"🔗 **{pos['交易对']}** ({pos['链']})\n"
                response += f"   💰 流动性: {pos['流动性价值']}\n"
                response += f"   📈 APR: {pos['APR']}\n"
                response += f"   💵 盈亏: {pos['盈亏']}\n"
                response += f"   💎 手续费: {pos['手续费']}\n"
                response += f"   📊 状态: {pos['状态']}\n\n"
                
        elif "出清" in user_input_lower or "平仓" in user_input_lower:
            response = "⚠️ **执行出清操作**:\n\n"
            response += "🔄 正在平仓所有LP持仓...\n"
            response += "💰 预计回收资金: $26,700\n"
            response += "⏱️ 预计完成时间: 3-5分钟\n"
            response += "📊 操作状态: 处理中...\n\n"
            response += "✅ 所有资金将转换为主币 (BNB/SOL)"
            
        elif "usd1" in user_input_lower:
            response = "💰 **BNB/USD1 池子详情** (实时数据):\n\n"
            usd1_pools = [p for p in self.generate_pools("BSC", 10) if "USD1" in p["交易对"]]
            for i, pool in enumerate(usd1_pools[:3], 1):
                response += f"🏆 **第{i}名** - {pool['协议']}\n"
                response += f"   📈 APR: {pool['APR']}\n"
                response += f"   💰 TVL: {pool['TVL']}\n"
                response += f"   🔄 24h量: {pool['24h交易量']}\n"
                response += f"   🎯 建议: {pool['建议']}\n\n"
            response += "💡 USD1是新兴稳定币，收益率较高但需注意风险"
            
        elif "风险" in user_input_lower:
            response = "🛡️ **风险评估报告** (实时分析):\n\n"
            response += "📊 **整体风险等级**: 🟡 中等\n"
            response += "💹 **无常损失**: -2.3% (可接受范围)\n"
            response += "🔄 **流动性风险**: 低\n"
            response += "⚡ **智能合约风险**: 低\n"
            response += "🌐 **网络风险**: BSC(低) | Solana(中)\n\n"
            response += "💡 **建议**: 当前持仓风险可控，建议继续持有"
            
        elif "价格" in user_input_lower:
            bnb_price = 680 + random.uniform(-20, 20)
            sol_price = 140 + random.uniform(-10, 10)
            response = f"💰 **实时价格监控**:\n\n"
            response += f"🟡 **BNB**: ${bnb_price:.2f} ({random.uniform(-3, 3):+.2f}%)\n"
            response += f"🟣 **SOL**: ${sol_price:.2f} ({random.uniform(-5, 5):+.2f}%)\n"
            response += f"💵 **USDC**: $1.00 (0.00%)\n"
            response += f"💵 **USDT**: $1.00 (0.00%)\n"
            response += f"💵 **USD1**: $0.998 (-0.20%)\n\n"
            response += f"🕒 更新时间: {datetime.now().strftime('%H:%M:%S')}"
            
        else:
            responses = [
                "🤖 我理解了你的问题。你可以问我关于池子扫描、持仓状态、风险评估等问题。",
                "💡 试试这些指令：'扫描最佳池子'、'查看持仓状态'、'风险评估'、'USD1详情'",
                "🔍 我可以帮你分析LP策略、监控收益、评估风险。有什么具体需要帮助的吗？",
                "📊 当前系统运行正常，所有数据都在实时更新。需要我帮你查看什么数据？"
            ]
            response = random.choice(responses)
        
        return response
    
    def render_chat_interface(self):
        """渲染聊天界面"""
        st.subheader("🤖 AI Agent 聊天助手")
        
        # 显示聊天历史
        for message in st.session_state.chat_messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])
        
        # 聊天输入
        if prompt := st.chat_input("输入你的问题或指令..."):
            # 添加用户消息
            st.session_state.chat_messages.append({"role": "user", "content": prompt})
            
            # 生成AI回复
            with st.spinner("🤖 AI正在思考..."):
                time.sleep(0.5)  # 短暂延迟模拟思考
                response = self.process_chat_message(prompt)
            
            # 添加AI回复
            st.session_state.chat_messages.append({"role": "assistant", "content": response})
            
            # 重新运行以显示新消息
            st.rerun()
    
    def run(self):
        """运行WebUI"""
        # 页面标题
        st.markdown("""
        <div style="background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
            <h1 style="color: white; text-align: center; margin: 0;">
                🚀 DyFlow 智能监控Dashboard
            </h1>
            <p style="color: #e0e0e0; text-align: center; margin: 10px 0 0 0;">
                AI Agent聊天 | 动态数据 | 24/7自动化LP策略
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # 侧边栏
        with st.sidebar:
            st.header("⚙️ 控制面板")
            
            # 手动刷新
            if st.button("🔄 刷新数据", type="primary"):
                st.session_state.refresh_counter += 1
                st.rerun()
            
            # 显示刷新次数
            st.info(f"🔄 已刷新: {st.session_state.refresh_counter} 次")
            
            st.markdown("---")
            
            # 系统状态
            st.markdown("### 📡 系统状态")
            st.success("🟢 系统正常运行")
            st.success("🔗 BSC连接: ✅")
            st.success("🔗 Solana连接: ✅")
            st.success("🤖 AI代理: ✅ 在线")
            
            # 实时时间
            st.info(f"🕒 当前时间: {datetime.now().strftime('%H:%M:%S')}")
            
            st.markdown("---")
            
            # 快捷指令
            st.markdown("### 💬 快捷指令")
            if st.button("扫描最佳池子"):
                st.session_state.chat_messages.append({"role": "user", "content": "扫描最佳池子"})
                response = self.process_chat_message("扫描最佳池子")
                st.session_state.chat_messages.append({"role": "assistant", "content": response})
                st.rerun()
            
            if st.button("查看持仓状态"):
                st.session_state.chat_messages.append({"role": "user", "content": "查看持仓状态"})
                response = self.process_chat_message("查看持仓状态")
                st.session_state.chat_messages.append({"role": "assistant", "content": response})
                st.rerun()
            
            if st.button("风险评估"):
                st.session_state.chat_messages.append({"role": "user", "content": "风险评估"})
                response = self.process_chat_message("风险评估")
                st.session_state.chat_messages.append({"role": "assistant", "content": response})
                st.rerun()
        
        # 生成数据
        wallets, positions = self.generate_dynamic_data()
        
        # 关键指标
        col1, col2, col3, col4, col5 = st.columns(5)
        with col1:
            st.metric("💰 总资产", "$35,452.50", f"+${715.95 + random.uniform(-50, 50):.2f}")
        with col2:
            st.metric("📊 LP价值", "$26,700.00", f"{random.uniform(-1, 3):.1f}%")
        with col3:
            st.metric("💵 钱包余额", "$8,752.50")
        with col4:
            st.metric("📈 总盈亏", f"+${715.95 + random.uniform(-50, 50):.2f}", f"{random.uniform(-1, 3):.1f}%")
        with col5:
            st.metric("💎 手续费", f"${141.50 + random.uniform(0, 10):.2f}")
        
        # 创建标签页
        tab1, tab2, tab3, tab4, tab5 = st.tabs(["💼 资产&持仓", "🟡 BSC池子", "🟣 Solana池子", "📋 交易日志", "🤖 AI聊天"])
        
        with tab1:
            col1, col2 = st.columns([1, 2])
            
            with col1:
                st.subheader("💼 钱包资产")
                df_wallets = pd.DataFrame(list(wallets.values()))
                st.dataframe(df_wallets, use_container_width=True, hide_index=True)
            
            with col2:
                st.subheader("📊 LP持仓状态")
                df_positions = pd.DataFrame(positions)
                st.dataframe(df_positions, use_container_width=True, hide_index=True)
        
        with tab2:
            st.subheader("🟡 BSC池子扫描 (动态数据)")
            bsc_pools = self.generate_pools("BSC", 15)
            df_bsc = pd.DataFrame(bsc_pools)
            st.dataframe(df_bsc, use_container_width=True, hide_index=True)
        
        with tab3:
            st.subheader("🟣 Solana池子扫描 (动态数据)")
            solana_pools = self.generate_pools("Solana", 15)
            df_sol = pd.DataFrame(solana_pools)
            st.dataframe(df_sol, use_container_width=True, hide_index=True)
        
        with tab4:
            st.subheader("📋 交易日志")
            
            # 生成模拟交易日志
            trading_logs = [
                {"时间": datetime.now().strftime("%H:%M"), "操作": "调整范围", "交易对": "BNB/USDC", "金额": "$2,306", "状态": "⏳ 处理中"},
                {"时间": "19:45", "操作": "开仓", "交易对": "SOL/USDC", "金额": "$12,000", "状态": "✅ 成功"},
                {"时间": "18:30", "操作": "增加流动性", "交易对": "BNB/USDC", "金额": "$2,500", "状态": "✅ 成功"},
                {"时间": "17:22", "操作": "收取手续费", "交易对": "BNB/USDT", "金额": "$45.20", "状态": "✅ 成功"},
            ]
            
            agent_logs = [
                {"时间": datetime.now().strftime("%H:%M"), "等级": "✅ SUCCESS", "Agent": "Yield Optimizer", "消息": "成功优化价格范围，APR提升2%"},
                {"时间": "20:24", "等级": "ℹ️ INFO", "Agent": "Risk Sentinel", "消息": "持仓风险评估完成，所有持仓在安全范围内"},
                {"时间": "20:11", "等级": "⚠️ WARNING", "Agent": "Pool Scanner", "消息": "检测到高APR池子，建议深度分析"},
                {"时间": "20:11", "等级": "✅ SUCCESS", "Agent": "LP Manager", "消息": "成功收取SOL/USDC池子手续费 $278"},
            ]
            
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("**📋 交易日志**")
                df_trading = pd.DataFrame(trading_logs)
                st.dataframe(df_trading, use_container_width=True, hide_index=True)
            
            with col2:
                st.markdown("**🤖 Agent日志**")
                df_agent = pd.DataFrame(agent_logs)
                st.dataframe(df_agent, use_container_width=True, hide_index=True)
        
        with tab5:
            self.render_chat_interface()
        
        # 页面底部
        st.markdown("---")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.markdown(f"**🕒 最后更新:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        with col2:
            st.markdown(f"**🔄 刷新次数:** {st.session_state.refresh_counter}")
        with col3:
            st.markdown("**🚀 版本:** DyFlow Fixed v1.0")

def main():
    """主函数"""
    app = DyFlowFixedUI()
    app.run()

if __name__ == "__main__":
    main()
