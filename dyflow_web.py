#!/usr/bin/env python3
"""
DyFlow Web UI - 基于Flask的Web界面
提供Web版本的钱包管理和监控界面
"""

import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

try:
    from flask import Flask, render_template_string, jsonify, request
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

app = Flask(__name__)

# 模拟数据
demo_data = {
    "portfolio": {
        "total_value": 43001.25,
        "total_lp": 27000.00,
        "total_pnl": 650.00,
        "active_wallets": 2,
        "total_positions": 3
    },
    "wallets": [
        {
            "name": "账户A",
            "chain": "BSC",
            "address": "******************************************",
            "total_assets": 25000.50,
            "lp_value": 15000.00,
            "delta_risk": 1200.00,
            "max_loss": 3750.00,
            "risk_level": "MEDIUM"
        },
        {
            "name": "账户A", 
            "chain": "Solana",
            "address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
            "total_assets": 18000.75,
            "lp_value": 12000.00,
            "delta_risk": 600.00,
            "max_loss": 1800.00,
            "risk_level": "LOW"
        }
    ],
    "positions": [
        {
            "chain": "BSC",
            "pair": "BNB/USDC",
            "value": 8000,
            "pnl": 320,
            "risk": "MEDIUM",
            "exit_token": "BNB"
        },
        {
            "chain": "BSC", 
            "pair": "BNB/USDT",
            "value": 7000,
            "pnl": -150,
            "risk": "LOW",
            "exit_token": "BNB"
        },
        {
            "chain": "Solana",
            "pair": "SOL/USDC", 
            "value": 12000,
            "pnl": 480,
            "risk": "LOW",
            "exit_token": "SOL"
        }
    ],
    "monitoring_results": [
        {
            "chain": "BSC",
            "pair": "BNB/USDC",
            "tvl": "$2,500,000",
            "volume_24h": "$850,000",
            "ai_score": "8.5/10",
            "recommendation": "BUY"
        },
        {
            "chain": "BSC",
            "pair": "BNB/USDT", 
            "tvl": "$1,800,000",
            "volume_24h": "$620,000",
            "ai_score": "7.8/10",
            "recommendation": "HOLD"
        },
        {
            "chain": "Solana",
            "pair": "SOL/USDC",
            "tvl": "$3,200,000", 
            "volume_24h": "$1,200,000",
            "ai_score": "9.1/10",
            "recommendation": "BUY"
        }
    ]
}

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DyFlow - 专业LP策略系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .risk-low { @apply bg-green-100 text-green-800; }
        .risk-medium { @apply bg-yellow-100 text-yellow-800; }
        .risk-high { @apply bg-red-100 text-red-800; }
        .rec-buy { @apply bg-green-100 text-green-800; }
        .rec-hold { @apply bg-yellow-100 text-yellow-800; }
        .rec-avoid { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 头部 -->
    <header class="bg-blue-600 text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <h1 class="text-3xl font-bold">🚀 DyFlow Pro</h1>
            <p class="text-blue-100">专业LP策略系统 - Rich UI | 钱包管理 | 专注交易对策略</p>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8">
        <!-- 投资组合概览 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">💼 投资组合概览</h2>
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-sm font-medium text-gray-500">总资产价值</h3>
                    <p class="text-2xl font-bold text-green-600">${{ portfolio.total_value | number_format }}</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-sm font-medium text-gray-500">LP持仓价值</h3>
                    <p class="text-2xl font-bold text-blue-600">${{ portfolio.total_lp | number_format }}</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-sm font-medium text-gray-500">总盈亏</h3>
                    <p class="text-2xl font-bold {% if portfolio.total_pnl >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                        {% if portfolio.total_pnl >= 0 %}+{% endif %}{{'${:,.2f}'.format(portfolio.total_pnl)}}
                    </p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-sm font-medium text-gray-500">活跃钱包</h3>
                    <p class="text-2xl font-bold text-gray-900">{{ portfolio.active_wallets }}</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-sm font-medium text-gray-500">持仓数量</h3>
                    <p class="text-2xl font-bold text-gray-900">{{ portfolio.total_positions }}</p>
                </div>
            </div>
        </section>

        <!-- 钱包详情 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">💼 钱包资产详情</h2>
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <table class="min-w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">账户</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">链</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">地址</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">总资产</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">LP价值</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">Delta风险</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">最大损失</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase">风险等级</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for wallet in wallets %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ wallet.name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ wallet.chain.upper() }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                                {{ wallet.address[:8] }}...{{ wallet.address[-6:] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                ${{ '{:,.2f}'.format(wallet.total_assets) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                ${{ '{:,.2f}'.format(wallet.lp_value) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                ${{ '{:,.2f}'.format(wallet.delta_risk) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                ${{ '{:,.2f}'.format(wallet.max_loss) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full risk-{{ wallet.risk_level.lower() }}">
                                    {{ wallet.risk_level }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </section>

        <!-- 专注交易对持仓 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">🎯 专注交易对持仓</h2>
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <table class="min-w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">链</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">交易对</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">持仓价值</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">盈亏</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase">风险等级</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase">退出代币</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for position in positions %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ position.chain.upper() }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ position.pair }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                ${{ '{:,.2f}'.format(position.value) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right">
                                <span class="{% if position.pnl >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                    {% if position.pnl >= 0 %}+{% endif %}{{'${:,.2f}'.format(position.pnl)}}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full risk-{{ position.risk.lower() }}">
                                    {{ position.risk }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center font-semibold">
                                {{ position.exit_token }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </section>

        <!-- 监控结果 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">📊 专注交易对监控结果</h2>
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <table class="min-w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">链</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">交易对</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">TVL</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">24h交易量</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">AI评分</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase">建议</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for result in monitoring_results %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ result.chain.upper() }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ result.pair }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ result.tvl }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ result.volume_24h }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-semibold">{{ result.ai_score }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full rec-{{ result.recommendation.lower() }}">
                                    {{ result.recommendation }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </section>

        <!-- AI建议 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">💡 AI投资建议</h2>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-start">
                    <div class="text-blue-600 text-2xl mr-3">🤖</div>
                    <div>
                        <h3 class="text-lg font-semibold text-blue-900 mb-2">AI分析建议</h3>
                        <ul class="text-blue-800 space-y-1">
                            <li>• BNB/USDC池子表现优异，建议增加配置</li>
                            <li>• SOL/USDC池子TVL和交易量都很健康，强烈推荐</li>
                            <li>• BNB/USDT池子稳定，可以保持当前持仓</li>
                            <li>• 整体风险可控，建议继续执行当前策略</li>
                            <li>• 退出时记得转换为BNB(BSC)或SOL(Solana)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 DyFlow Pro - 专业LP策略系统</p>
            <p class="text-gray-400 mt-2">基于Agno Framework | 本地AI驱动 | 专注交易对策略</p>
        </div>
    </footer>

    <script>
        // 自动刷新数据
        setInterval(() => {
            location.reload();
        }, 30000); // 30秒刷新一次
    </script>
</body>
</html>
"""

@app.route('/')
def dashboard():
    """主仪表板"""
    return render_template_string(HTML_TEMPLATE, **demo_data)

@app.route('/api/portfolio')
def api_portfolio():
    """API: 获取投资组合数据"""
    return jsonify(demo_data)

@app.route('/api/monitoring')
def api_monitoring():
    """API: 获取监控结果"""
    return jsonify(demo_data['monitoring_results'])

def run_web_ui():
    """运行Web UI"""
    if not FLASK_AVAILABLE:
        print("❌ Flask未安装，请运行: pip install flask")
        return
    
    print("🚀 启动DyFlow Web UI...")
    print("📱 访问地址: http://localhost:5000")
    print("💡 按Ctrl+C停止服务")
    
    app.run(host='0.0.0.0', port=5000, debug=True)

if __name__ == "__main__":
    run_web_ui()
