#!/usr/bin/env python3
"""
DyFlow WebUI Dashboard - 简化版本
确保数据正确显示的版本
"""

import streamlit as st
import pandas as pd
from datetime import datetime
import random
import time

# 页面配置
st.set_page_config(
    page_title="DyFlow - LP策略监控Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

def generate_bsc_pools():
    """生成BSC池子数据"""
    pools = []
    pairs = ["BNB/USDC", "BNB/USDT", "BNB/USD1"]
    protocols = ["PancakeSwap V3", "PancakeSwap V2", "Uniswap V3", "SushiSwap"]
    
    for i in range(25):
        pair = random.choice(pairs)
        protocol = random.choice(protocols)
        
        # 生成不同风险等级的池子
        if i < 8:  # 低风险
            risk_level = "低"
            apr = random.uniform(15, 30)
            tvl = random.uniform(1000000, 5000000)
            volume = random.uniform(500000, 2000000)
            recommendation = "BUY"
        elif i < 17:  # 中风险
            risk_level = "中"
            apr = random.uniform(25, 45)
            tvl = random.uniform(500000, 2000000)
            volume = random.uniform(200000, 1000000)
            recommendation = random.choice(["BUY", "HOLD"])
        else:  # 高风险
            risk_level = "高"
            apr = random.uniform(40, 80)
            tvl = random.uniform(100000, 800000)
            volume = random.uniform(50000, 500000)
            recommendation = random.choice(["HOLD", "AVOID"])
        
        pools.append({
            "交易对": pair,
            "协议": protocol,
            "TVL": f"${tvl/1000000:.1f}M",
            "24h交易量": f"${volume/1000:.0f}K",
            "APR": f"{apr:.1f}%",
            "风险等级": risk_level,
            "建议": recommendation
        })
    
    return sorted(pools, key=lambda x: float(x["APR"].replace("%", "")), reverse=True)

def generate_solana_pools():
    """生成Solana池子数据"""
    pools = []
    protocols = ["Meteora DLMM", "Orca Whirlpool", "Raydium CLMM", "Lifinity"]
    
    for i in range(25):
        protocol = random.choice(protocols)
        
        # 生成不同风险等级的池子
        if i < 8:  # 低风险
            risk_level = "低"
            apr = random.uniform(20, 35)
            tvl = random.uniform(1500000, 6000000)
            volume = random.uniform(800000, 3000000)
            recommendation = "BUY"
        elif i < 17:  # 中风险
            risk_level = "中"
            apr = random.uniform(30, 55)
            tvl = random.uniform(600000, 2500000)
            volume = random.uniform(300000, 1500000)
            recommendation = random.choice(["BUY", "HOLD"])
        else:  # 高风险
            risk_level = "高"
            apr = random.uniform(50, 100)
            tvl = random.uniform(150000, 1000000)
            volume = random.uniform(80000, 800000)
            recommendation = random.choice(["HOLD", "AVOID"])
        
        pools.append({
            "交易对": "SOL/USDC",
            "协议": protocol,
            "TVL": f"${tvl/1000000:.1f}M",
            "24h交易量": f"${volume/1000:.0f}K",
            "APR": f"{apr:.1f}%",
            "风险等级": risk_level,
            "建议": recommendation
        })
    
    return sorted(pools, key=lambda x: float(x["APR"].replace("%", "")), reverse=True)

def main():
    """主函数"""
    # 页面标题
    st.markdown("""
    <div style="background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
        <h1 style="color: white; text-align: center; margin: 0;">
            🚀 DyFlow 实时监控Dashboard
        </h1>
        <p style="color: #e0e0e0; text-align: center; margin: 10px 0 0 0;">
            24/7 自动化单边LP策略系统 - BSC & Solana
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.header("⚙️ 控制面板")
        
        # 自动刷新
        auto_refresh = st.checkbox("🔄 自动刷新", value=False)
        if auto_refresh:
            refresh_interval = st.slider("刷新间隔 (秒)", 1, 10, 3)
            time.sleep(refresh_interval)
            st.rerun()
        
        # 手动刷新
        if st.button("🔄 立即刷新"):
            st.rerun()
        
        st.markdown("---")
        
        # 系统状态
        st.markdown("### 📡 系统状态")
        st.success("🟢 系统正常运行")
        st.info("🔗 BSC连接: ✅")
        st.info("🔗 Solana连接: ✅")
        st.info("🤖 AI代理: ✅")
    
    # 关键指标
    col1, col2, col3, col4, col5 = st.columns(5)
    with col1:
        st.metric("💰 总资产", "$35,452.50", "+$715.95")
    with col2:
        st.metric("📊 LP价值", "$26,700.00", "+2.7%")
    with col3:
        st.metric("💵 钱包余额", "$8,752.50")
    with col4:
        st.metric("📈 总盈亏", "+$715.95", "+2.7%")
    with col5:
        st.metric("💎 手续费", "$141.50")
    
    # 钱包资产
    st.subheader("💼 钱包资产")
    wallet_data = [
        {
            "钱包": "BSC主钱包",
            "地址": "0x1234...5678",
            "主币": "2.45 BNB",
            "USDC": "1,250.30",
            "USDT": "890.75",
            "USD1": "650.20",
            "总价值": "$4,457.75"
        },
        {
            "钱包": "Solana主钱包",
            "地址": "9WzDXw...AWWM",
            "主币": "15.67 SOL",
            "USDC": "2,100.50",
            "USDT": "-",
            "USD1": "-",
            "总价值": "$4,294.75"
        }
    ]
    df_wallets = pd.DataFrame(wallet_data)
    st.dataframe(df_wallets, use_container_width=True, hide_index=True)
    
    # LP持仓状态
    st.subheader("📊 LP持仓状态")
    positions_data = [
        {
            "链": "BSC",
            "交易对": "BNB/USDC",
            "流动性价值": "$8,500",
            "APR": f"{24.5 + random.uniform(-2, 3):.1f}%",
            "盈亏": f"+${320.50 + random.uniform(-20, 30):.2f}",
            "手续费": f"${45.20 + random.uniform(0, 5):.2f}",
            "状态": "🟢 活跃",
            "持仓时间": "515天"
        },
        {
            "链": "BSC",
            "交易对": "BNB/USDT",
            "流动性价值": "$6,200",
            "APR": f"{21.2 + random.uniform(-2, 3):.1f}%",
            "盈亏": f"-${85.30 - random.uniform(-10, 20):.2f}",
            "手续费": f"${28.90 + random.uniform(0, 3):.2f}",
            "状态": "🟡 监控",
            "持仓时间": "514天"
        },
        {
            "链": "Solana",
            "交易对": "SOL/USDC",
            "流动性价值": "$12,000",
            "APR": f"{32.8 + random.uniform(-2, 5):.1f}%",
            "盈亏": f"+${480.75 + random.uniform(-30, 50):.2f}",
            "手续费": f"${67.40 + random.uniform(0, 8):.2f}",
            "状态": "🟢 活跃",
            "持仓时间": "513天"
        }
    ]
    df_positions = pd.DataFrame(positions_data)
    st.dataframe(df_positions, use_container_width=True, hide_index=True)
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["🟡 BSC池子", "🟣 Solana池子", "📋 交易日志", "🤖 Agent日志"])
    
    with tab1:
        st.subheader("🟡 BSC池子扫描 (25个池子 - 高中低风险)")
        bsc_pools = generate_bsc_pools()
        df_bsc = pd.DataFrame(bsc_pools[:15])
        st.dataframe(df_bsc, use_container_width=True, hide_index=True)
    
    with tab2:
        st.subheader("🟣 Solana池子扫描 (25个池子 - 高中低风险)")
        solana_pools = generate_solana_pools()
        df_sol = pd.DataFrame(solana_pools[:15])
        st.dataframe(df_sol, use_container_width=True, hide_index=True)
    
    with tab3:
        st.subheader("📋 交易日志 (最近执行记录)")
        trading_logs = [
            {"时间": "20:24", "操作": "调整范围", "交易对": "BNB/USDC", "金额": "$2,306", "状态": "⏳ 处理中"},
            {"时间": "19:45", "操作": "开仓", "交易对": "SOL/USDC", "金额": "$12,000", "状态": "✅ 成功"},
            {"时间": "18:30", "操作": "增加流动性", "交易对": "BNB/USDC", "金额": "$2,500", "状态": "✅ 成功"},
            {"时间": "17:22", "操作": "收取手续费", "交易对": "BNB/USDT", "金额": "$45.20", "状态": "✅ 成功"},
            {"时间": "16:15", "操作": "调整范围", "交易对": "SOL/USDC", "金额": "$8,500", "状态": "✅ 成功"},
        ]
        df_trading = pd.DataFrame(trading_logs)
        st.dataframe(df_trading, use_container_width=True, hide_index=True)
    
    with tab4:
        st.subheader("🤖 Agent执行日志 (智能代理活动)")
        agent_logs = [
            {"时间": "20:24", "等级": "✅ SUCCESS", "Agent": "Yield Optimizer", "消息": "成功优化价格范围，APR提升2%"},
            {"时间": "20:24", "等级": "ℹ️ INFO", "Agent": "Risk Sentinel", "消息": "持仓风险评估完成，所有持仓在安全范围内"},
            {"时间": "20:24", "等级": "⚠️ WARNING", "Agent": "Pool Scanner", "消息": "检测到高APR池子，建议深度分析"},
            {"时间": "20:11", "等级": "✅ SUCCESS", "Agent": "LP Manager", "消息": "成功收取SOL/USDC池子手续费 $278"},
            {"时间": "20:11", "等级": "❌ ERROR", "Agent": "Transaction Manager", "消息": "BNB/USD1平仓交易失败，Gas费用不足"},
        ]
        df_agent = pd.DataFrame(agent_logs)
        st.dataframe(df_agent, use_container_width=True, hide_index=True)
    
    # 页面底部信息
    st.markdown("---")
    col1, col2, col3 = st.columns(3)
    with col1:
        st.markdown("**🕒 最后更新:** " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    with col2:
        st.markdown("**📊 支持的交易对:** BNB/USDC, BNB/USDT, BNB/USD1, SOL/USDC")
    with col3:
        st.markdown("**🔧 版本:** DyFlow WebUI v1.0")

if __name__ == "__main__":
    main()
