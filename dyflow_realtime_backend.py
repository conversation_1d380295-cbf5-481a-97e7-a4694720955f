#!/usr/bin/env python3
"""
DyFlow 实时后端 - FastAPI + WebSocket + Supabase
提供真正的实时数据更新
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import asyncio
import json
import random
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict
import uvicorn
from supabase import create_client, Client
import os

app = FastAPI(title="DyFlow Realtime Dashboard")

# Supabase配置
SUPABASE_URL = "https://ikxobiwfymtxhumpntw.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlreG9iaXdoZnltdHhodW1wbnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk4OTA2NTIsImV4cCI6MjA1NTQ2NjY1Mn0.erqkmEPvyuqU2KePB-Jt03bNvtfO4tiD6KxwNtq8HuA"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlreG9iaXdoZnltdHhodW1wbnR3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTg5MDY1MiwiZXhwIjoyMDU1NDY2NjUyfQ.P4vR_QS5GXPU1zh25qb9pgWmg82QEJoymiVO8-7-REE"

# 初始化Supabase客户端
supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # 连接已断开，移除
                self.active_connections.remove(connection)

manager = ConnectionManager()

class DyFlowDataGenerator:
    """实时数据生成器"""
    
    def __init__(self):
        self.base_data = self.initialize_base_data()
    
    def initialize_base_data(self):
        """初始化基础数据"""
        return {
            "wallets": {
                "bsc": {"BNB": 2.45, "USDC": 1250.30, "USDT": 890.75, "USD1": 650.20},
                "solana": {"SOL": 15.67, "USDC": 2100.50}
            },
            "positions": [
                {"chain": "BSC", "pair": "BNB/USDC", "liquidity": 8500, "base_apr": 24.5, "base_pnl": 320.50, "base_fees": 45.20},
                {"chain": "BSC", "pair": "BNB/USDT", "liquidity": 6200, "base_apr": 21.2, "base_pnl": -85.30, "base_fees": 28.90},
                {"chain": "Solana", "pair": "SOL/USDC", "liquidity": 12000, "base_apr": 32.8, "base_pnl": 480.75, "base_fees": 67.40}
            ]
        }
    
    def generate_realtime_data(self):
        """生成实时数据"""
        current_time = datetime.now()
        
        # 价格数据
        bnb_price = 680 + random.uniform(-20, 20)
        sol_price = 140 + random.uniform(-10, 10)
        
        # 钱包数据
        wallets = {
            "bsc": {
                "address": "0x1234...5678",
                "BNB": round(self.base_data["wallets"]["bsc"]["BNB"] + random.uniform(-0.1, 0.1), 2),
                "USDC": round(self.base_data["wallets"]["bsc"]["USDC"] + random.uniform(-50, 50), 2),
                "USDT": round(self.base_data["wallets"]["bsc"]["USDT"] + random.uniform(-30, 30), 2),
                "USD1": round(self.base_data["wallets"]["bsc"]["USD1"] + random.uniform(-20, 20), 2),
                "total_value": 0
            },
            "solana": {
                "address": "9WzDXw...AWWM",
                "SOL": round(self.base_data["wallets"]["solana"]["SOL"] + random.uniform(-0.5, 0.5), 2),
                "USDC": round(self.base_data["wallets"]["solana"]["USDC"] + random.uniform(-100, 100), 2),
                "total_value": 0
            }
        }
        
        # 计算钱包总价值
        wallets["bsc"]["total_value"] = round(
            wallets["bsc"]["BNB"] * bnb_price + 
            wallets["bsc"]["USDC"] + 
            wallets["bsc"]["USDT"] + 
            wallets["bsc"]["USD1"], 2
        )
        
        wallets["solana"]["total_value"] = round(
            wallets["solana"]["SOL"] * sol_price + 
            wallets["solana"]["USDC"], 2
        )
        
        # LP持仓数据
        positions = []
        for pos in self.base_data["positions"]:
            apr_change = random.uniform(-3, 5)
            pnl_change = random.uniform(-30, 50)
            fees_change = random.uniform(0, 8)
            
            positions.append({
                "chain": pos["chain"],
                "pair": pos["pair"],
                "liquidity": pos["liquidity"],
                "apr": round(pos["base_apr"] + apr_change, 1),
                "pnl": round(pos["base_pnl"] + pnl_change, 2),
                "fees": round(pos["base_fees"] + fees_change, 2),
                "status": "🟢 活跃" if pos["base_pnl"] > 0 else "🟡 监控"
            })
        
        # 池子数据
        bsc_pools = self.generate_pools("BSC", 12)
        solana_pools = self.generate_pools("Solana", 12)
        
        # Agent日志
        agent_logs = self.generate_agent_logs()
        
        # 交易日志
        trading_logs = self.generate_trading_logs()
        
        return {
            "timestamp": current_time.isoformat(),
            "prices": {"BNB": bnb_price, "SOL": sol_price, "USDC": 1.0, "USDT": 1.0, "USD1": 0.998},
            "wallets": wallets,
            "positions": positions,
            "bsc_pools": bsc_pools,
            "solana_pools": solana_pools,
            "agent_logs": agent_logs,
            "trading_logs": trading_logs,
            "metrics": {
                "total_assets": wallets["bsc"]["total_value"] + wallets["solana"]["total_value"] + sum(p["liquidity"] for p in positions),
                "total_lp": sum(p["liquidity"] for p in positions),
                "total_pnl": sum(p["pnl"] for p in positions),
                "total_fees": sum(p["fees"] for p in positions)
            }
        }
    
    def generate_pools(self, chain: str, count: int):
        """生成池子数据"""
        pools = []
        
        if chain == "BSC":
            pairs = ["BNB/USDC", "BNB/USDT", "BNB/USD1"]
            protocols = ["PancakeSwap V3", "PancakeSwap V2", "Uniswap V3", "SushiSwap"]
        else:
            pairs = ["SOL/USDC"]
            protocols = ["Meteora DLMM", "Orca Whirlpool", "Raydium CLMM", "Lifinity"]
        
        for i in range(count):
            base_apr = 20 + i * 3 + random.uniform(-8, 20)
            risk_level = "低" if i < 4 else "中" if i < 8 else "高"
            
            pools.append({
                "pair": random.choice(pairs),
                "protocol": random.choice(protocols),
                "tvl": round(random.uniform(0.5, 8.0), 1),
                "volume_24h": round(random.uniform(100, 3000), 0),
                "apr": round(base_apr, 1),
                "risk_level": risk_level,
                "recommendation": random.choice(["BUY", "HOLD", "AVOID"])
            })
        
        # 按APR排序
        pools.sort(key=lambda x: x["apr"], reverse=True)
        return pools
    
    def generate_agent_logs(self):
        """生成Agent日志"""
        agents = ["LP Manager", "Risk Sentinel", "Pool Scanner", "Yield Optimizer", "Price Monitor"]
        levels = ["SUCCESS", "WARNING", "INFO", "ERROR"]
        messages = [
            "成功优化价格范围，APR提升2.5%",
            "检测到高APR池子，建议深度分析",
            "持仓风险评估完成，所有持仓在安全范围内",
            "自动收取手续费操作成功",
            "发现套利机会，正在执行交易",
            "网络拥堵检测，延迟执行交易",
            "价格突破关键阻力位，触发重新平衡",
            "BNB/USD1池子APR异常上涨",
            "SOL价格突破$145，触发重新平衡",
            "成功收取池子手续费"
        ]
        
        logs = []
        for i in range(6):
            time_offset = datetime.now() - timedelta(minutes=random.randint(1, 30))
            logs.append({
                "time": time_offset.strftime("%H:%M"),
                "level": random.choice(levels),
                "agent": random.choice(agents),
                "message": random.choice(messages)
            })
        
        return sorted(logs, key=lambda x: x["time"], reverse=True)
    
    def generate_trading_logs(self):
        """生成交易日志"""
        operations = ["开仓", "平仓", "调整范围", "收取手续费", "增加流动性"]
        pairs = ["BNB/USDC", "BNB/USDT", "SOL/USDC", "BNB/USD1"]
        statuses = ["✅ 成功", "⏳ 处理中", "❌ 失败"]
        
        logs = []
        for i in range(5):
            time_offset = datetime.now() - timedelta(minutes=random.randint(1, 120))
            logs.append({
                "time": time_offset.strftime("%H:%M"),
                "operation": random.choice(operations),
                "pair": random.choice(pairs),
                "amount": f"${random.randint(1000, 15000):,}",
                "status": random.choice(statuses)
            })
        
        return sorted(logs, key=lambda x: x["time"], reverse=True)
    
    def process_chat_message(self, message: str):
        """处理聊天消息"""
        message_lower = message.lower()
        
        if "扫描" in message_lower or "池子" in message_lower:
            data = self.generate_realtime_data()
            bsc_top = data["bsc_pools"][:3]
            sol_top = data["solana_pools"][:3]
            
            response = "🔍 **最佳池子扫描结果**:\n\n"
            response += "**🟡 BSC Top 3:**\n"
            for i, pool in enumerate(bsc_top, 1):
                response += f"{i}. {pool['pair']} - APR: {pool['apr']}%\n"
            
            response += "\n**🟣 Solana Top 3:**\n"
            for i, pool in enumerate(sol_top, 1):
                response += f"{i}. {pool['pair']} - APR: {pool['apr']}%\n"
                
        elif "持仓" in message_lower:
            data = self.generate_realtime_data()
            response = "📊 **当前LP持仓状态**:\n\n"
            for pos in data["positions"]:
                response += f"🔗 {pos['pair']} ({pos['chain']})\n"
                response += f"   APR: {pos['apr']}% | 盈亏: ${pos['pnl']:+.2f}\n\n"
                
        elif "风险" in message_lower:
            response = "🛡️ **风险评估**:\n\n"
            response += "📊 整体风险: 🟡 中等\n"
            response += "💹 无常损失: -2.1%\n"
            response += "🔄 流动性风险: 低\n"
            response += "💡 建议: 持仓风险可控"
            
        elif "价格" in message_lower:
            data = self.generate_realtime_data()
            prices = data["prices"]
            response = f"💰 **实时价格**:\n\n"
            response += f"🟡 BNB: ${prices['BNB']:.2f}\n"
            response += f"🟣 SOL: ${prices['SOL']:.2f}\n"
            response += f"💵 USDC: ${prices['USDC']:.3f}\n"
            response += f"💵 USD1: ${prices['USD1']:.3f}"
            
        else:
            responses = [
                "🤖 我可以帮你扫描池子、查看持仓、评估风险等。",
                "💡 试试：'扫描最佳池子'、'查看持仓状态'、'风险评估'",
                "🔍 需要我帮你分析什么数据？",
            ]
            response = random.choice(responses)
        
        return {
            "type": "chat_response",
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "ai_response": response
        }

# 全局数据生成器
data_generator = DyFlowDataGenerator()

@app.get("/", response_class=HTMLResponse)
async def get_dashboard():
    """返回Dashboard HTML页面"""
    try:
        with open("dyflow_realtime_dashboard.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
        <head><title>DyFlow Dashboard</title></head>
        <body>
        <h1>DyFlow Dashboard</h1>
        <p>HTML文件未找到，请先创建 dyflow_realtime_dashboard.html</p>
        <p>WebSocket连接: ws://localhost:8000/ws</p>
        <p>API端点: http://localhost:8000/api/data</p>
        </body>
        </html>
        """)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点"""
    await manager.connect(websocket)
    try:
        while True:
            # 接收客户端消息
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=0.1)
                message_data = json.loads(data)
                
                if message_data["type"] == "chat_message":
                    # 处理聊天消息
                    response = data_generator.process_chat_message(message_data["message"])
                    await manager.send_personal_message(json.dumps(response), websocket)
                    
            except asyncio.TimeoutError:
                pass
            except json.JSONDecodeError:
                pass
            
            # 发送实时数据更新
            realtime_data = data_generator.generate_realtime_data()
            realtime_data["type"] = "data_update"
            
            await manager.send_personal_message(json.dumps(realtime_data), websocket)
            
            # 等待2秒后发送下一次更新
            await asyncio.sleep(2)
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.get("/api/data")
async def get_current_data():
    """获取当前数据的REST API"""
    return data_generator.generate_realtime_data()

@app.post("/api/chat")
async def chat_endpoint(message: dict):
    """聊天API端点"""
    return data_generator.process_chat_message(message.get("message", ""))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
