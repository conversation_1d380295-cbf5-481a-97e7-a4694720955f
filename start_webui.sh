#!/bin/bash

echo "🚀 启动DyFlow WebUI Dashboard..."
echo "📦 检查依赖..."

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖包..."
/usr/bin/python3 -m pip install streamlit pandas plotly numpy --user

# 启动Streamlit应用
echo "🌐 启动WebUI..."
echo "📱 浏览器将自动打开 http://localhost:8501"
echo "🔄 支持实时数据更新和AI指令交互"
echo ""
echo "✨ 功能特性:"
echo "   💼 实时钱包和LP持仓监控"
echo "   🟡 BSC池子扫描 (25个池子)"
echo "   🟣 Solana池子扫描 (25个池子)"
echo "   📋 交易日志和Agent日志"
echo "   🤖 AI指令交互"
echo "   🔄 自动刷新功能"
echo ""

/usr/bin/python3 -m streamlit run dyflow_webui.py --server.port 8501 --server.address 0.0.0.0
