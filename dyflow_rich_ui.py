#!/usr/bin/env python3
"""
DyFlow Rich UI - 专业的命令行界面
使用Rich库创建美观的CLI界面
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import json

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.columns import Columns
from rich.align import Align
from rich import box
from rich.tree import Tree

console = Console()

class DyFlowRichUI:
    """DyFlow Rich UI - 专业的命令行界面"""
    
    def __init__(self):
        self.console = console
        self.session_id = f"dyflow_{int(datetime.now().timestamp())}"
        
    def show_header(self):
        """显示头部信息"""
        header_text = Text()
        header_text.append("DyFlow", style="bold blue")
        header_text.append(" - 24/7 自动化LP策略系统", style="bold white")
        
        header_panel = Panel(
            Align.center(header_text),
            box=box.DOUBLE,
            style="blue",
            subtitle="基于Agno Framework | 本地AI驱动"
        )
        
        self.console.print(header_panel)
        self.console.print()
    
    def show_wallet_overview(self, wallet_data: Dict[str, Any]):
        """显示钱包概览"""
        # 创建钱包表格
        wallet_table = Table(title="💼 钱包资产概览", box=box.ROUNDED)
        wallet_table.add_column("账户", style="cyan", no_wrap=True)
        wallet_table.add_column("链", style="magenta")
        wallet_table.add_column("地址", style="yellow", max_width=20)
        wallet_table.add_column("总资产", style="green", justify="right")
        wallet_table.add_column("LP价值", style="blue", justify="right")
        wallet_table.add_column("风险等级", style="red", justify="center")
        
        total_value = 0
        total_lp_value = 0
        
        for account_name, account_data in wallet_data.items():
            for chain, chain_data in account_data.get('chains', {}).items():
                address = chain_data.get('address', 'N/A')
                assets = chain_data.get('total_assets', 0)
                lp_value = chain_data.get('lp_value', 0)
                risk_level = chain_data.get('risk_level', 'UNKNOWN')
                
                # 风险等级颜色
                risk_color = {
                    'LOW': 'green',
                    'MEDIUM': 'yellow', 
                    'HIGH': 'red',
                    'CRITICAL': 'bright_red'
                }.get(risk_level, 'white')
                
                wallet_table.add_row(
                    account_name,
                    chain.upper(),
                    f"{address[:8]}...{address[-6:]}" if len(address) > 20 else address,
                    f"${assets:,.2f}",
                    f"${lp_value:,.2f}",
                    Text(risk_level, style=risk_color)
                )
                
                total_value += assets
                total_lp_value += lp_value
        
        # 添加总计行
        wallet_table.add_section()
        wallet_table.add_row(
            "总计", "", "", 
            f"${total_value:,.2f}", 
            f"${total_lp_value:,.2f}", 
            ""
        )
        
        return wallet_table
    
    def show_strategy_config(self):
        """显示策略配置"""
        config_table = Table(title="⚙️ 策略配置", box=box.ROUNDED)
        config_table.add_column("配置项", style="cyan")
        config_table.add_column("BSC设置", style="yellow")
        config_table.add_column("Solana设置", style="magenta")
        
        config_table.add_row("目标交易对", "BNB/USDC, BNB/USDT", "SOL/USDC")
        config_table.add_row("最小TVL", "$100,000", "$100,000")
        config_table.add_row("最小24h交易量", "$50,000", "$50,000")
        config_table.add_row("最大池子数", "5", "5")
        config_table.add_row("止损阈值", "15%", "15%")
        config_table.add_row("紧急退出", "10%", "10%")
        config_table.add_row("退出代币", "BNB", "SOL")
        
        return config_table
    
    def show_risk_dashboard(self, risk_data: Dict[str, Any]):
        """显示风险仪表板"""
        risk_table = Table(title="🛡️ 风险监控仪表板", box=box.ROUNDED)
        risk_table.add_column("风险指标", style="cyan")
        risk_table.add_column("当前值", style="yellow", justify="right")
        risk_table.add_column("阈值", style="red", justify="right")
        risk_table.add_column("状态", style="green", justify="center")
        
        # 示例风险数据
        risk_metrics = [
            ("总Delta风险", risk_data.get('total_delta', 0), 10000, "NORMAL"),
            ("最大可能损失", risk_data.get('max_loss', 0), 50000, "WARNING"),
            ("无常损失", risk_data.get('impermanent_loss', 0), 8, "NORMAL"),
            ("流动性风险", risk_data.get('liquidity_risk', 0), 15, "NORMAL"),
            ("价格波动率", risk_data.get('volatility', 0), 25, "HIGH")
        ]
        
        for metric, current, threshold, status in risk_metrics:
            status_color = {
                'NORMAL': 'green',
                'WARNING': 'yellow',
                'HIGH': 'red',
                'CRITICAL': 'bright_red'
            }.get(status, 'white')
            
            if metric in ["总Delta风险", "最大可能损失"]:
                current_str = f"${current:,.2f}"
                threshold_str = f"${threshold:,.2f}"
            else:
                current_str = f"{current:.2f}%"
                threshold_str = f"{threshold:.2f}%"
            
            risk_table.add_row(
                metric,
                current_str,
                threshold_str,
                Text(status, style=status_color)
            )
        
        return risk_table
    
    def show_main_menu(self):
        """显示主菜单"""
        menu_options = [
            "🔍 池子监控",
            "💼 钱包管理", 
            "📊 风险分析",
            "⚙️ 策略配置",
            "📈 历史记录",
            "🧪 系统测试",
            "🆘 帮助文档",
            "🚪 退出系统"
        ]
        
        menu_text = "\n".join([f"{i+1}. {option}" for i, option in enumerate(menu_options)])
        
        menu_panel = Panel(
            menu_text,
            title="🎮 主菜单",
            box=box.ROUNDED,
            style="blue"
        )
        
        return menu_panel
    
    def show_pool_monitoring_results(self, results: Dict[str, Any]):
        """显示池子监控结果"""
        if not results:
            return Panel("❌ 暂无监控结果", title="监控结果", style="red")
        
        # 创建结果表格
        results_table = Table(title="📊 池子监控结果", box=box.ROUNDED)
        results_table.add_column("链", style="cyan")
        results_table.add_column("池子名称", style="yellow")
        results_table.add_column("TVL", style="green", justify="right")
        results_table.add_column("24h交易量", style="blue", justify="right")
        results_table.add_column("评分", style="magenta", justify="right")
        results_table.add_column("建议", style="red", justify="center")
        
        # 解析结果数据
        for chain, chain_data in results.get('results', {}).get('pool_analysis', {}).items():
            if 'error' not in chain_data:
                top_pools = chain_data.get('top_pools', [])
                for pool in top_pools[:3]:  # 显示前3个
                    recommendation = pool.get('recommendation', 'HOLD')
                    rec_color = {
                        'BUY': 'green',
                        'HOLD': 'yellow',
                        'AVOID': 'red'
                    }.get(recommendation, 'white')
                    
                    results_table.add_row(
                        chain.upper(),
                        pool.get('pool_name', 'Unknown'),
                        f"${pool.get('tvl_score', 0)*100000:,.0f}",  # 模拟TVL
                        f"${pool.get('volume_score', 0)*50000:,.0f}",  # 模拟交易量
                        f"{pool.get('total_score', 0):.1f}/10",
                        Text(recommendation, style=rec_color)
                    )
        
        return results_table
    
    def create_dashboard_layout(self, wallet_data: Dict, risk_data: Dict, monitoring_results: Dict = None):
        """创建仪表板布局"""
        layout = Layout()
        
        # 分割布局
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        layout["body"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        layout["left"].split_column(
            Layout(name="wallet", ratio=2),
            Layout(name="strategy", ratio=1)
        )
        
        layout["right"].split_column(
            Layout(name="risk", ratio=1),
            Layout(name="monitoring", ratio=2)
        )
        
        # 填充内容
        layout["header"].update(Panel("🚀 DyFlow 实时监控仪表板", style="bold blue"))
        layout["wallet"].update(self.show_wallet_overview(wallet_data))
        layout["strategy"].update(self.show_strategy_config())
        layout["risk"].update(self.show_risk_dashboard(risk_data))
        
        if monitoring_results:
            layout["monitoring"].update(self.show_pool_monitoring_results(monitoring_results))
        else:
            layout["monitoring"].update(Panel("等待监控数据...", title="池子监控", style="yellow"))
        
        layout["footer"].update(Panel(f"会话ID: {self.session_id} | 更新时间: {datetime.now().strftime('%H:%M:%S')}", style="dim"))
        
        return layout
    
    async def run_with_progress(self, task_name: str, task_func, *args, **kwargs):
        """带进度条运行任务"""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task(f"[cyan]{task_name}...", total=None)
            
            try:
                result = await task_func(*args, **kwargs)
                progress.update(task, description=f"[green]✅ {task_name}完成")
                return result
            except Exception as e:
                progress.update(task, description=f"[red]❌ {task_name}失败: {str(e)}")
                return None
    
    def get_user_choice(self, prompt_text: str, choices: List[str]) -> str:
        """获取用户选择"""
        return Prompt.ask(
            prompt_text,
            choices=choices,
            default=choices[0] if choices else None
        )
    
    def confirm_action(self, message: str) -> bool:
        """确认操作"""
        return Confirm.ask(message)
    
    async def show_live_dashboard(self, wallet_data: Dict, risk_data: Dict):
        """显示实时仪表板"""
        monitoring_results = None
        
        with Live(
            self.create_dashboard_layout(wallet_data, risk_data, monitoring_results),
            refresh_per_second=1,
            console=self.console
        ) as live:
            
            # 模拟实时更新
            for i in range(10):
                await asyncio.sleep(2)
                
                # 更新风险数据 (模拟)
                risk_data['total_delta'] += (i * 100)
                risk_data['volatility'] = 15 + (i * 0.5)
                
                # 第5秒后添加监控结果
                if i == 5:
                    monitoring_results = {
                        'results': {
                            'pool_analysis': {
                                'bsc': {
                                    'top_pools': [
                                        {
                                            'pool_name': 'BNB/USDC',
                                            'tvl_score': 8.5,
                                            'volume_score': 7.2,
                                            'total_score': 7.8,
                                            'recommendation': 'BUY'
                                        }
                                    ]
                                }
                            }
                        }
                    }
                
                live.update(self.create_dashboard_layout(wallet_data, risk_data, monitoring_results))
    
    async def run(self):
        """运行Rich UI"""
        self.show_header()
        
        # 示例数据
        wallet_data = {
            "账户A": {
                "chains": {
                    "bsc": {
                        "address": "******************************************",
                        "total_assets": 25000.50,
                        "lp_value": 15000.00,
                        "risk_level": "MEDIUM"
                    },
                    "solana": {
                        "address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
                        "total_assets": 18000.75,
                        "lp_value": 12000.00,
                        "risk_level": "LOW"
                    }
                }
            }
        }
        
        risk_data = {
            "total_delta": 2500.00,
            "max_loss": 8500.00,
            "impermanent_loss": 3.2,
            "liquidity_risk": 8.5,
            "volatility": 18.7
        }
        
        while True:
            try:
                # 显示主菜单
                self.console.print(self.show_main_menu())
                
                choice = self.get_user_choice(
                    "\n[bold cyan]请选择操作",
                    ["1", "2", "3", "4", "5", "6", "7", "8"]
                )
                
                if choice == "1":
                    self.console.print("\n[bold green]🔍 启动池子监控...")
                    await self.show_live_dashboard(wallet_data, risk_data)
                    
                elif choice == "2":
                    self.console.print("\n[bold blue]💼 钱包管理功能开发中...")
                    self.console.print(self.show_wallet_overview(wallet_data))
                    
                elif choice == "3":
                    self.console.print("\n[bold yellow]📊 风险分析...")
                    self.console.print(self.show_risk_dashboard(risk_data))
                    
                elif choice == "4":
                    self.console.print("\n[bold magenta]⚙️ 策略配置...")
                    self.console.print(self.show_strategy_config())
                    
                elif choice == "5":
                    self.console.print("\n[bold cyan]📈 历史记录功能开发中...")
                    
                elif choice == "6":
                    self.console.print("\n[bold green]🧪 运行系统测试...")
                    from test_agno_success import main as test_main
                    await self.run_with_progress("系统测试", test_main)
                    
                elif choice == "7":
                    self.console.print("\n[bold blue]🆘 帮助文档...")
                    help_text = """
                    DyFlow 使用帮助:
                    1. 池子监控 - 实时监控BSC和Solana LP池子
                    2. 钱包管理 - 管理多个钱包和资产
                    3. 风险分析 - 实时风险监控和预警
                    4. 策略配置 - 自定义投资策略参数
                    5. 历史记录 - 查看历史交易和收益
                    6. 系统测试 - 验证所有组件功能
                    """
                    self.console.print(Panel(help_text, title="帮助文档", style="blue"))
                    
                elif choice == "8":
                    if self.confirm_action("[bold red]确定要退出DyFlow系统吗?"):
                        self.console.print("\n[bold green]👋 感谢使用DyFlow，再见！")
                        break
                
                if choice != "8":
                    Prompt.ask("\n[dim]按回车键继续...")
                    self.console.clear()
                    self.show_header()
                
            except KeyboardInterrupt:
                if self.confirm_action("\n[bold red]确定要退出吗?"):
                    self.console.print("\n[bold green]👋 再见！")
                    break
            except Exception as e:
                self.console.print(f"\n[bold red]❌ 错误: {e}")
                Prompt.ask("[dim]按回车键继续...")

async def main():
    """主函数"""
    ui = DyFlowRichUI()
    await ui.run()

if __name__ == "__main__":
    # 安装rich库
    try:
        import rich
    except ImportError:
        print("正在安装Rich库...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "rich"])
        import rich

    asyncio.run(main())
