#!/usr/bin/env python3
"""
简单的Web3测试脚本
"""

def test_web3_import():
    """测试Web3导入"""
    try:
        from web3 import Web3

        # 测试中间件导入
        try:
            from web3.middleware.geth_poa import geth_poa_middleware
            print("✅ 使用新版本geth_poa_middleware")
        except ImportError:
            try:
                from web3.middleware import geth_poa_middleware
                print("✅ 使用旧版本geth_poa_middleware")
            except ImportError:
                print("⚠️  geth_poa_middleware不可用，使用空中间件")
                def geth_poa_middleware(make_request, web3):
                    return make_request

        print("✅ Web3导入成功")
        print(f"Web3版本: {Web3.__version__ if hasattr(Web3, '__version__') else 'Unknown'}")

        # 测试创建Web3实例
        w3 = Web3(Web3.HTTPProvider('https://bsc-dataseed1.binance.org/'))
        w3.middleware_onion.inject(geth_poa_middleware, layer=0)
        print("✅ Web3实例创建成功")

        # 测试连接
        try:
            is_connected = w3.is_connected()
            print(f"✅ BSC连接状态: {is_connected}")
        except Exception as e:
            print(f"⚠️  BSC连接测试失败: {e}")

        return True

    except ImportError as e:
        print(f"❌ Web3导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ Web3测试失败: {e}")
        return False

def test_aiohttp_import():
    """测试aiohttp导入"""
    try:
        import aiohttp
        print("✅ aiohttp导入成功")
        print(f"aiohttp版本: {aiohttp.__version__}")
        return True
    except ImportError as e:
        print(f"❌ aiohttp导入失败: {e}")
        return False

def test_solana_import():
    """测试Solana导入"""
    try:
        from solders.transaction import Transaction
        from solana.rpc.async_api import AsyncClient
        print("✅ Solana模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ Solana导入失败: {e}")
        return False

def test_agno_import():
    """测试Agno导入"""
    try:
        from agno.agent import Agent
        from agno.models.openai import OpenAIChat
        print("✅ Agno Framework导入成功")
        return True
    except ImportError as e:
        print(f"❌ Agno Framework导入失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 开始简单依赖测试")
    print("=" * 40)
    
    results = []
    results.append(("Web3", test_web3_import()))
    results.append(("aiohttp", test_aiohttp_import()))
    results.append(("Solana", test_solana_import()))
    results.append(("Agno", test_agno_import()))
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\n成功率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
