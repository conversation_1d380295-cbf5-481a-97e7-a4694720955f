#!/bin/bash

echo "🚀 启动DyFlow WebUI Dashboard (简化版本)..."
echo "📦 这个版本确保数据正确显示"
echo ""
echo "🌐 WebUI将在 http://localhost:8501 启动"
echo "📱 浏览器将自动打开"
echo ""
echo "✨ 功能特性:"
echo "   💼 实时钱包和LP持仓监控"
echo "   🟡 BSC池子扫描 (25个池子)"
echo "   🟣 Solana池子扫描 (25个池子)"
echo "   📋 交易日志和Agent日志"
echo "   🔄 自动刷新功能"
echo ""
echo "🔄 按 Ctrl+C 停止服务"
echo "=" * 50

/usr/bin/python3 -m streamlit run dyflow_webui_simple.py --server.port 8501 --server.address 0.0.0.0
