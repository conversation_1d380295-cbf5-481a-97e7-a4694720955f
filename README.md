# DyFlow v3 + Agno Framework: Low-Float Meme Coin LP Strategy

## 🚀 Overview

DyFlow v3 is an advanced, AI-powered automated liquidity provision strategy system specifically designed for low-float meme coins on BSC (PancakeSwap v3) and Solana (Meteora DLMM v2). Built on the Agno Framework, it provides 24/7 automated single-sided LP strategies with intelligent risk management and profit optimization.

## ✨ Key Features

### 🤖 AI-Powered Decision Making
- **Agno Framework Integration**: Advanced AI agents for strategy planning, risk assessment, and market analysis
- **Multi-Model Support**: OpenAI GPT-4 and Anthropic Claude for diverse AI perspectives
- **Structured Reasoning**: AI agents provide detailed reasoning for all decisions
- **Memory & Learning**: Persistent memory for continuous strategy improvement

### 🎯 Strategy Components
- **Token Discovery**: Automated scanning for low-float meme coins with high potential
- **Single-Sided LP**: Optimized single-token liquidity provision with automatic range management
- **DCA Exit Strategy**: Intelligent profit-taking with dollar-cost averaging
- **Risk Management**: IL fuse, VaR calculation, and emergency exit mechanisms
- **Portfolio Management**: Holistic portfolio optimization and rebalancing

### 🔗 Multi-Chain Support
- **BSC**: PancakeSwap v3 integration with Zap functionality
- **Solana**: Meteora DLMM v2 with Jupiter swap aggregation
- **Cross-Chain**: Unified strategy execution across both chains

### 📊 Advanced Risk Management
- **IL Fuse**: Automatic exit when impermanent loss exceeds -8%
- **VaR Calculation**: 95% confidence Value at Risk monitoring
- **Circuit Breakers**: Fail-safe mechanisms for agent failures
- **Real-time Monitoring**: Prometheus metrics and Grafana dashboards

## 🛠️ Installation

### Prerequisites
- Python 3.9+
- API keys for OpenAI/Anthropic
- Supabase account for database
- RPC endpoints for BSC and Solana

### Setup

1. **Clone the repository**
```bash
git clone https://github.com/proerror77/dyflow.git
cd dyflow
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your API keys and configuration
```

4. **Run the workflow**
```bash
python workflows/low_float_workflow.py
```

## ⚙️ Configuration

Key parameters in `config/config.yaml`:

```yaml
strategy:
  discovery:
    min_tvl_usd: 20000
    max_fdv_usd: 1000000
    max_circulating_ratio: 0.05

  risk:
    il_fuse_threshold: -8.0
    var_fuse_threshold: 4.0
    max_position_size_percentage: 15.0

  dca:
    trigger_gain_percentage: 20.0
    step_size_percentage: 10.0
    max_orders: 10
```

## 📈 Performance Metrics

### Target KPIs
- **Time-to-LP**: ≤ 60 seconds from pool discovery
- **IL Fuse**: Exit when IL ≥ -8%
- **Weekly Returns**: ≥ 4% target
- **Uptime**: ≥ 99% availability

## 🔒 Security & Risk Management

### Multi-layer Risk Controls
- **IL Fuse**: Automatic exit when impermanent loss exceeds -8%
- **VaR Limits**: 95% confidence Value at Risk monitoring (4% threshold)
- **Position Size Limits**: Maximum 15% allocation per position
- **Circuit Breakers**: Automatic shutdown on repeated failures
- **Emergency Exit**: Manual and automatic emergency procedures

## 🧪 Testing

```bash
# Unit tests
pytest tests/unit/

# Integration tests
pytest tests/integration/

# Load testing
pytest tests/load/
```

## 📄 License

MIT License - see LICENSE file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Cryptocurrency trading involves substantial risk of loss. Use at your own risk and never invest more than you can afford to lose.

---

**Built with ❤️ using the Agno Framework for next-generation AI-powered DeFi strategies.**
