"""
Base Agent - DyFlow v3 + Agno Framework
基础Agent类，为所有DyFlow Agent提供通用功能
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import json

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    from agno.models.anthropic import Claude
    from agno.tools.reasoning import ReasoningTools
    # from agno.storage.sqlite import SqliteStorage  # 暂时注释掉，因为可能不可用
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Agent:
        pass
    class BaseModel:
        pass

# Ollama imports
try:
    import requests
    import json
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False

# ========== Ollama Model Class ==========

class OllamaModel:
    """Ollama本地模型适配器"""

    def __init__(self, model_name: str = "qwen2.5:3b", base_url: str = "http://localhost:11434"):
        self.model_name = model_name
        self.base_url = base_url
        self.api_url = f"{base_url}/api/generate"

    def run(self, prompt: str, **kwargs) -> str:
        """运行模型推理"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "top_p": kwargs.get("top_p", 0.9),
                    "max_tokens": kwargs.get("max_tokens", 2048)
                }
            }

            response = requests.post(self.api_url, json=payload, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result.get("response", "")

        except Exception as e:
            print(f"Ollama模型错误: {str(e)}")
            return f"Error: {str(e)}"

    async def arun(self, prompt: str, **kwargs) -> str:
        """异步运行模型推理"""
        # 简化实现，实际可以使用aiohttp
        return self.run(prompt, **kwargs)

# ========== Ollama Model Class ==========

class OllamaModel:
    """Ollama本地模型适配器"""

    def __init__(self, model_name: str = "qwen3:latest", base_url: str = "http://localhost:11434"):
        self.model_name = model_name
        self.base_url = base_url
        self.api_url = f"{base_url}/api/generate"

    def run(self, prompt: str, **kwargs) -> str:
        """运行模型推理"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "top_p": kwargs.get("top_p", 0.9),
                    "max_tokens": kwargs.get("max_tokens", 2048)
                }
            }

            response = requests.post(self.api_url, json=payload, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result.get("response", "")

        except Exception as e:
            logger.error("ollama_model_error", error=str(e))
            return f"Error: {str(e)}"

    async def arun(self, prompt: str, **kwargs) -> str:
        """异步运行模型推理"""
        # 简化实现，实际可以使用aiohttp
        return self.run(prompt, **kwargs)

from ..utils.exceptions import DyFlowException
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)

# ========== Base Models ==========

@dataclass
class AgentConfig:
    """Agent配置"""
    name: str
    model_provider: str = "openai"
    model_name: str = "gpt-4o-mini"
    max_retries: int = 3
    timeout_seconds: int = 60
    enable_reasoning: bool = False
    enable_memory: bool = True
    memory_db_path: Optional[str] = None

@dataclass
class AgentResult:
    """Agent执行结果"""
    agent_name: str
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: Optional[str] = None

# ========== Base Agent Class ==========

class BaseAgent:
    """DyFlow基础Agent类"""
    
    def __init__(self, config: Union[Dict[str, Any], AgentConfig]):
        """初始化基础Agent"""
        if isinstance(config, dict):
            self.config = AgentConfig(**config)
        else:
            self.config = config
        
        self.logger = structlog.get_logger(f"{__name__}.{self.config.name}")
        self.initialized = False
        self.agno_agent: Optional[Agent] = None
        self.ollama_model: Optional[OllamaModel] = None

        # 状态管理
        self.last_execution_time: Optional[datetime] = None
        self.execution_count = 0
        self.error_count = 0
        
    async def initialize(self) -> bool:
        """初始化Agent"""
        try:
            # 如果使用Ollama，直接创建简化的Agent
            if self.config.model_provider.lower() == "ollama":
                if OLLAMA_AVAILABLE:
                    self.ollama_model = OllamaModel(model_name=self.config.model_name)
                    self.initialized = True
                    self.logger.info("ollama_agent_initialized",
                                   name=self.config.name,
                                   model=self.config.model_name)
                    return True
                else:
                    self.logger.warning("ollama_not_available")
                    return False

            # 使用Agno Framework
            if not AGNO_AVAILABLE:
                self.logger.warning("agno_framework_not_available")
                return False

            # 创建Agno Agent
            model = self._create_model()
            storage = self._create_storage() if self.config.enable_memory else None

            self.agno_agent = Agent(
                name=self.config.name,
                role=self._get_agent_role(),
                model=model,
                instructions=self._get_agent_instructions(),
                reasoning=self.config.enable_reasoning,
                storage=storage,
                show_tool_calls=True,
                markdown=True
            )

            # 添加工具
            tools = await self._get_agent_tools()
            if tools:
                for tool in tools:
                    self.agno_agent.add_tool(tool)

            self.initialized = True
            self.logger.info("agent_initialized",
                           name=self.config.name,
                           model=f"{self.config.model_provider}:{self.config.model_name}")
            return True

        except Exception as e:
            self.logger.error("agent_initialization_failed", error=str(e))
            return False
    
    def _create_model(self):
        """创建模型实例"""
        if self.config.model_provider.lower() == "openai":
            return OpenAIChat(id=self.config.model_name)
        elif self.config.model_provider.lower() == "anthropic":
            return Claude(id=self.config.model_name)
        elif self.config.model_provider.lower() == "ollama":
            if OLLAMA_AVAILABLE:
                return OllamaModel(model_name=self.config.model_name)
            else:
                raise DyFlowException("Ollama not available - requests library not installed")
        else:
            raise DyFlowException(f"Unsupported model provider: {self.config.model_provider}")
    
    def _create_storage(self) -> Optional[Any]:
        """创建存储实例"""
        if not self.config.enable_memory:
            return None

        # 暂时禁用存储功能，因为SqliteStorage可能不可用
        logger.warning("storage_disabled", reason="SqliteStorage not available")
        return None
    
    def _get_agent_role(self) -> str:
        """获取Agent角色描述 - 子类应重写"""
        return f"{self.config.name} Agent"
    
    def _get_agent_instructions(self) -> List[str]:
        """获取Agent指令 - 子类应重写"""
        return [
            f"你是DyFlow系统的{self.config.name} Agent。",
            "请根据用户请求执行相应的任务。",
            "始终提供准确、有用的信息。"
        ]
    
    async def _get_agent_tools(self) -> List[Any]:
        """获取Agent工具 - 子类应重写"""
        return []
    
    async def execute(self, task: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """执行Agent任务"""
        start_time = datetime.now()
        
        try:
            if not self.initialized:
                await self.initialize()

            # 检查Agent是否正确初始化
            if not self.agno_agent and not self.ollama_model:
                raise DyFlowException("Agent not properly initialized")
            
            self.logger.info("agent_execution_started", 
                           task=task[:100] + "..." if len(task) > 100 else task)
            
            # 构建完整的提示
            full_prompt = self._build_prompt(task, context)

            # 执行Agent
            if hasattr(self, 'ollama_model') and self.ollama_model:
                # 使用Ollama模型
                response = await self.ollama_model.arun(full_prompt)
            else:
                # 使用Agno Agent
                response = self.agno_agent.run(full_prompt)

            # 处理响应
            result_data = await self._process_response(response, context)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 更新状态
            self.last_execution_time = datetime.now()
            self.execution_count += 1
            
            result = AgentResult(
                agent_name=self.config.name,
                success=True,
                data=result_data,
                execution_time=execution_time,
                timestamp=datetime.now().isoformat()
            )
            
            self.logger.info("agent_execution_completed", 
                           execution_time=execution_time,
                           execution_count=self.execution_count)
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.error_count += 1
            
            self.logger.error("agent_execution_failed", 
                            error=str(e),
                            execution_time=execution_time,
                            error_count=self.error_count)
            
            return AgentResult(
                agent_name=self.config.name,
                success=False,
                error=str(e),
                execution_time=execution_time,
                timestamp=datetime.now().isoformat()
            )
    
    def _build_prompt(self, task: str, context: Optional[Dict[str, Any]] = None) -> str:
        """构建完整的提示 - 子类可重写"""
        prompt_parts = [task]
        
        if context:
            prompt_parts.append("\n上下文信息:")
            for key, value in context.items():
                if isinstance(value, (dict, list)):
                    prompt_parts.append(f"{key}: {json.dumps(value, ensure_ascii=False, indent=2)}")
                else:
                    prompt_parts.append(f"{key}: {value}")
        
        return "\n".join(prompt_parts)
    
    async def _process_response(self, response: Any, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理Agent响应 - 子类可重写"""
        if hasattr(response, 'content'):
            return {"response": response.content}
        else:
            return {"response": str(response)}
    
    def get_status(self) -> Dict[str, Any]:
        """获取Agent状态"""
        return {
            "name": self.config.name,
            "initialized": self.initialized,
            "execution_count": self.execution_count,
            "error_count": self.error_count,
            "last_execution_time": self.last_execution_time.isoformat() if self.last_execution_time else None,
            "success_rate": (self.execution_count - self.error_count) / max(self.execution_count, 1) * 100
        }
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.agno_agent and hasattr(self.agno_agent, 'storage') and self.agno_agent.storage:
                # 关闭存储连接
                pass
            
            self.logger.info("agent_cleanup_completed")
            
        except Exception as e:
            self.logger.error("agent_cleanup_failed", error=str(e))
