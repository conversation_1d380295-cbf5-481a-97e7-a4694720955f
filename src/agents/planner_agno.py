"""
Planner Agno Agent - Dy-Flow v3 + Agno Framework
基於 Agno Framework 的增強版策略規劃系統
集成 AI 推理、知識庫支持和智能決策功能
"""

import asyncio
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import json
import math

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    from agno.models.anthropic import Claude
    from agno.tools.reasoning import ReasoningTools
    from agno.storage.sqlite import SqliteStorage
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
    
    # 可選工具導入
    try:
        from agno.tools.duckduckgo import DuckDuckGoTools
        DUCKDUCKGO_AVAILABLE = True
    except ImportError:
        DUCKDUCKGO_AVAILABLE = False
        DuckDuckGoTools = None
        
except ImportError:
    AGNO_AVAILABLE = False
    DUCKDUCKGO_AVAILABLE = False
    # 定義替代類
    class BaseModel:
        pass
    class Agent:
        pass
    DuckDuckGoTools = None

# Dy-Flow v3 imports
from .base_agent import BaseAgent
# from .planner import PlannerAgent  # 暂时注释掉，因为可能不存在
# from ..utils.models_v3 import PoolScore, Plan, AgentResult, StrategyType  # 暂时注释掉
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

# 临时定义缺失的类
@dataclass
class Plan:
    pool_id: str
    strategy_type: str
    allocation_amount: float
    priority: int
    expected_apy: float
    risk_level: str
    reasoning: str

@dataclass
class AgentResult:
    agent_name: str
    data: Any
    timestamp: Any
    status: str
    metadata: Optional[Dict[str, Any]] = None

logger = structlog.get_logger(__name__)

# ========== Agno 結構化輸出模型 ==========

if AGNO_AVAILABLE:
    class StrategyAnalysis(BaseModel):
        """策略分析 - Agno 結構化輸出"""
        strategy_type: str = Field(..., description="策略類型", pattern="^(delta_neutral|ladder_ss|passive_high_tvl)$")
        suitability_score: float = Field(..., ge=0.0, le=100.0, description="適合度評分 (0-100)")
        expected_return: float = Field(..., description="預期年化收益率")
        risk_level: str = Field(..., description="風險級別", pattern="^(low|medium|high)$")
        
        # 策略特定分析
        market_conditions_fit: float = Field(..., ge=0.0, le=1.0, description="市場條件適配度")
        capital_efficiency: float = Field(..., ge=0.0, le=1.0, description="資本效率")
        execution_complexity: str = Field(..., description="執行複雜度", pattern="^(simple|moderate|complex)$")
        
        # 具體建議
        recommended_allocation: float = Field(..., ge=0.0, le=1.0, description="建議分配比例")
        min_capital_requirement: float = Field(..., ge=0.0, description="最低資本要求 (USD)")
        max_position_size: float = Field(..., ge=0.0, description="最大持倉規模 (USD)")
        
        pros: List[str] = Field(default_factory=list, description="策略優勢")
        cons: List[str] = Field(default_factory=list, description="策略劣勢")
        key_risks: List[str] = Field(default_factory=list, description="關鍵風險")
        
        reasoning: str = Field(..., description="分析推理過程")

    class PoolAllocationDecision(BaseModel):
        """池子分配決策 - Agno 結構化輸出"""
        pool_id: str = Field(..., description="池子ID")
        strategy_type: str = Field(..., description="推薦策略", pattern="^(delta_neutral|ladder_ss|passive_high_tvl|none)$")
        allocation_amount: float = Field(..., ge=0.0, description="分配金額 (USD)")
        confidence_level: float = Field(..., ge=0.0, le=1.0, description="決策信心度")
        
        # 執行參數
        entry_timing: str = Field(..., description="入場時機", pattern="^(immediate|wait_dip|wait_breakout|gradual)$")
        position_sizing: str = Field(..., description="倉位管理", pattern="^(conservative|moderate|aggressive)$")
        hedge_ratio: Optional[float] = Field(None, ge=0.0, le=1.0, description="對沖比例 (如適用)")
        
        # 風險管理
        stop_loss_level: Optional[float] = Field(None, ge=0.0, le=1.0, description="止損水平")
        take_profit_level: Optional[float] = Field(None, ge=1.0, description="止盈水平")
        max_drawdown_tolerance: float = Field(..., ge=0.0, le=1.0, description="最大回撤容忍度")
        
        # 預期表現
        expected_apy: float = Field(..., description="預期年化收益率")
        risk_adjusted_return: float = Field(..., description="風險調整後收益")
        expected_duration: int = Field(..., ge=1, description="預期持有天數")
        
        reasoning: str = Field(..., description="分配決策推理")
        conditions_to_exit: List[str] = Field(default_factory=list, description="退出條件")

    class MarketEnvironmentAssessment(BaseModel):
        """市場環境評估 - Agno 結構化輸出"""
        overall_sentiment: str = Field(..., description="整體市場情緒", pattern="^(very_bearish|bearish|neutral|bullish|very_bullish)$")
        volatility_regime: str = Field(..., description="波動率狀態", pattern="^(low|normal|high|extreme)$")
        liquidity_conditions: str = Field(..., description="流動性條件", pattern="^(poor|fair|good|excellent)$")
        
        # 趨勢分析
        short_term_trend: str = Field(..., description="短期趨勢 (1-7天)", pattern="^(down|sideways|up)$")
        medium_term_trend: str = Field(..., description="中期趨勢 (1-4週)", pattern="^(down|sideways|up)$")
        long_term_trend: str = Field(..., description="長期趨勢 (1-3個月)", pattern="^(down|sideways|up)$")
        
        # 關鍵因子
        defi_tvl_trend: str = Field(..., description="DeFi TVL 趨勢", pattern="^(declining|stable|growing)$")
        yield_environment: str = Field(..., description="收益環境", pattern="^(compressed|normal|elevated)$")
        risk_appetite: str = Field(..., description="風險偏好", pattern="^(risk_off|neutral|risk_on)$")
        
        confidence_score: float = Field(..., ge=0.0, le=1.0, description="評估信心度")
        key_catalysts: List[str] = Field(default_factory=list, description="關鍵催化劑")
        major_risks: List[str] = Field(default_factory=list, description="主要風險")
        
        reasoning: str = Field(..., description="市場評估推理")

    class OptimalPortfolio(BaseModel):
        """最優組合 - Agno 結構化輸出"""
        total_allocation: float = Field(..., ge=0.0, description="總分配金額 (USD)")
        expected_portfolio_apy: float = Field(..., description="組合預期年化收益率")
        portfolio_risk_score: float = Field(..., ge=0.0, le=100.0, description="組合風險評分")
        
        # 策略分配
        delta_neutral_allocation: float = Field(..., ge=0.0, le=1.0, description="Delta中性策略分配比例")
        ladder_ss_allocation: float = Field(..., ge=0.0, le=1.0, description="階梯單邊策略分配比例")
        passive_high_tvl_allocation: float = Field(..., ge=0.0, le=1.0, description="被動高TVL策略分配比例")
        cash_reserve: float = Field(..., ge=0.0, le=1.0, description="現金儲備比例")
        
        # 風險指標
        portfolio_sharpe_ratio: float = Field(..., description="組合夏普比率")
        max_expected_drawdown: float = Field(..., ge=0.0, le=1.0, description="最大預期回撤")
        diversification_score: float = Field(..., ge=0.0, le=1.0, description="多樣化評分")
        
        pool_allocations: List[PoolAllocationDecision] = Field(default_factory=list, description="具體池子分配")
        rebalancing_frequency: str = Field(..., description="再平衡頻率", pattern="^(daily|weekly|bi_weekly|monthly)$")
        
        reasoning: str = Field(..., description="組合構建推理")
        monitoring_metrics: List[str] = Field(default_factory=list, description="監控指標")

else:
    # 降級數據類
    @dataclass
    class StrategyAnalysis:
        strategy_type: str
        suitability_score: float
        expected_return: float
        risk_level: str
        recommended_allocation: float
        reasoning: str

# ========== DeFi 知識庫 ==========

class DeFiKnowledgeBase:
    """DeFi 專業知識庫"""
    
    def get_static_knowledge(self) -> str:
        """獲取靜態 DeFi 知識"""
        return """
        DeFi Liquidity Provision Strategies:
        
        1. Delta-Neutral LP Strategy:
        - Provide liquidity while hedging directional risk
        - Use perpetual futures to hedge token exposure
        - Target: 15-30% APY with low volatility
        - Best in: High volatility markets
        - Capital requirement: $10,000+ for efficiency
        
        2. Ladder Single-Sided Strategy:
        - Place multiple positions at different price levels
        - Directional bet on token price movement
        - Target: 30-60% APY with higher risk
        - Best in: Trending markets
        - Capital requirement: $5,000+ per ladder
        
        3. Passive High-TVL Strategy:
        - Long-term positions in stable, high-TVL pools
        - Focus on fee generation and compound growth
        - Target: 8-15% APY with low risk
        - Best in: Stable markets
        - Capital requirement: $1,000+ minimum
        
        Risk Factors:
        - Impermanent Loss: Price divergence reduces returns
        - Smart Contract Risk: Protocol vulnerabilities
        - Liquidity Risk: Difficulty exiting positions
        - Market Risk: Overall market volatility
        
        Optimization Factors:
        - Pool fees and volume
        - Token volatility and correlation
        - Available hedging instruments
        - Capital efficiency requirements
        """

# ========== Agno Enhanced Planner Agent ==========

class PlannerAgnoAgent(BaseAgent):
    """基於 Agno Framework 的增強版策略規劃 Agent"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        # 檢查 Agno 可用性
        self.agno_available = AGNO_AVAILABLE

        # 添加agent_config属性以保持兼容性
        self.agent_config = self.config
        
        if not self.agno_available:
            logger.warning("agno_framework_not_available", 
                         message="Agno framework not installed, falling back to traditional planning")
        
        # Agno 相關配置
        agent_specific_config = self.agent_config or {}
        agno_config = agent_specific_config.get('agno', {})
        
        # LLM 模型選擇
        self.primary_model = agno_config.get('primary_model', 'gpt-4o')
        self.secondary_model = agno_config.get('secondary_model', 'claude-3-7-sonnet-latest')
        self.use_reasoning = agno_config.get('use_reasoning', True)
        self.enable_memory = agno_config.get('enable_memory', True)
        self.enable_knowledge_base = agno_config.get('enable_knowledge_base', True)
        self.enable_market_data = agno_config.get('enable_market_data', True)
        self.debug_mode = agno_config.get('debug_mode', False)
        
        # Agno Agent 實例
        self.agno_market_analyst: Optional[Agent] = None
        self.agno_strategy_advisor: Optional[Agent] = None
        self.agno_portfolio_optimizer: Optional[Agent] = None
        
        # Memory 存儲和知識庫
        self.memory_storage = None
        self.knowledge_base = DeFiKnowledgeBase()
        
        # 策略歷史和學習
        self.strategy_performance_history: Dict[str, List[Dict]] = {}
        self.last_market_assessment: Optional[datetime] = None
        
    async def initialize(self) -> None:
        """初始化 Agno Agents 和知識庫"""
        await super().initialize()
        await self.planner.initialize()
        
        if not self.agno_available:
            logger.info("planner_fallback_initialized", message="Using traditional planning only")
            self.is_initialized = True
            return
        
        try:
            # 初始化 Memory 存儲
            if self.enable_memory:
                self.memory_storage = SqliteStorage(
                    table_name="planner_agno_memory",
                    db_file="data/agno_memory/planner_sessions.db",
                    auto_upgrade_schema=True
                )
            
            # 構建知識庫（使用靜態知識）
            defi_knowledge = self.knowledge_base.get_static_knowledge()
            
            # 初始化市場分析 Agent
            self.agno_market_analyst = Agent(
                name="DeFiMarketAnalyst",
                role="Analyze DeFi market conditions for strategic planning",
                agent_id="planner-market-analyst",
                model=OpenAIChat(id=self.primary_model),
                tools=[
                    ReasoningTools(add_instructions=True)
                ] + ([DuckDuckGoTools()] if self.enable_market_data and DUCKDUCKGO_AVAILABLE else []),
                reasoning=False,  # 暂时禁用推理避免复杂性
                response_model=MarketEnvironmentAssessment,  # 重新启用结构化输出
                storage=self.memory_storage,
                session_id="market_analysis_session",
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a DeFi market analyst specializing in liquidity provision strategies.",
                    "Analyze market conditions to guide optimal LP strategy selection.",
                    "Consider volatility regimes, liquidity conditions, and market sentiment.",
                    "Provide insights for timing strategy deployment and risk management.",
                    f"DeFi Knowledge Base:\n{defi_knowledge}",
                    "Focus on actionable market intelligence for LP optimization."
                ]
            )
            
            # 初始化策略顧問 Agent
            self.agno_strategy_advisor = Agent(
                name="DeFiStrategyAdvisor",
                role="Provide expert advice on DeFi LP strategies",
                agent_id="planner-strategy-advisor",
                model=Claude(id=self.secondary_model),
                tools=[ReasoningTools(add_instructions=True)],
                reasoning=False,  # 暂时禁用推理避免复杂性
                response_model=StrategyAnalysis,  # 重新启用结构化输出
                storage=self.memory_storage,
                session_id="strategy_advice_session",
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a DeFi strategy expert with deep knowledge of liquidity provision.",
                    "Analyze different LP strategies for their suitability in current market conditions.",
                    "Consider risk-return profiles, capital requirements, and execution complexity.",
                    "Provide detailed strategy analysis with clear pros, cons, and risk assessments.",
                    f"DeFi Knowledge Base:\n{defi_knowledge}",
                    "Focus on practical strategy implementation and risk management."
                ]
            )
            
            # 初始化組合優化 Agent
            self.agno_portfolio_optimizer = Agent(
                name="DeFiPortfolioOptimizer",
                role="Optimize DeFi portfolio allocation across strategies and pools",
                agent_id="planner-portfolio-optimizer",
                model=OpenAIChat(id=self.primary_model),
                tools=[ReasoningTools(add_instructions=True)],
                reasoning=False,  # 暂时禁用推理避免复杂性
                response_model=OptimalPortfolio,  # 重新启用结构化输出
                storage=self.memory_storage,
                session_id="portfolio_optimization_session",
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a quantitative portfolio manager specializing in DeFi strategies.",
                    "Optimize portfolio allocation to maximize risk-adjusted returns.",
                    "Consider correlations, diversification, and capital efficiency.",
                    "Provide specific allocation recommendations with risk management guidelines.",
                    f"DeFi Knowledge Base:\n{defi_knowledge}",
                    "Focus on practical portfolio construction and ongoing management."
                ]
            )
            
            logger.info("planner_agno_initialized",
                       primary_model=self.primary_model,
                       secondary_model=self.secondary_model,
                       reasoning_enabled=self.use_reasoning,
                       memory_enabled=self.enable_memory,
                       knowledge_base_enabled=self.enable_knowledge_base,
                       market_data_enabled=self.enable_market_data,
                       debug_mode=self.debug_mode)
            
            self.is_initialized = True
            
        except Exception as e:
            logger.error("planner_agno_initialization_failed", error=str(e))
            # 降級到傳統模式
            self.agno_available = False
            logger.warning("falling_back_to_traditional_planning", error=str(e))
            self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行 Agno 增強版策略規劃"""
        if not self.is_initialized:
            await self.initialize()
        
        # 如果 Agno 不可用，使用傳統規劃
        if not self.agno_available:
            traditional_result = await self.planner.execute()
            traditional_result.metadata = traditional_result.metadata or {}
            traditional_result.metadata["agno_enhanced"] = False
            traditional_result.metadata["fallback_mode"] = True
            return traditional_result
        
        try:
            logger.info("planner_agno_execution_started")
            
            # 1. 獲取評分後的池子數據
            scored_pools = self.planner._get_scored_pools()
            if not scored_pools:
                logger.info("no_scored_pools_for_planning")
                return AgentResult(
                    agent_name=self.name,
                    data=[],
                    timestamp=get_utc_timestamp(),
                    status="success",
                    metadata={"message": "沒有評分後的池子可供規劃", "agno_enhanced": True}
                )
            
            # 2. AI 市場環境評估
            market_assessment = await self._analyze_market_environment_with_ai()
            
            # 3. AI 策略分析
            strategy_analyses = {}
            for strategy_type in ["delta_neutral", "ladder_ss", "passive_high_tvl"]:
                analysis = await self._analyze_strategy_with_ai(strategy_type, market_assessment, scored_pools)
                if analysis:
                    strategy_analyses[strategy_type] = analysis
            
            # 4. AI 組合優化
            optimal_portfolio = await self._optimize_portfolio_with_ai(
                strategy_analyses, market_assessment, scored_pools
            )
            
            # 5. 生成具體執行計劃
            execution_plans = []
            if optimal_portfolio and optimal_portfolio.pool_allocations:
                for allocation in optimal_portfolio.pool_allocations:
                    if allocation.allocation_amount > 0:
                        plan = Plan(
                            pool_id=allocation.pool_id,
                            strategy_type=allocation.strategy_type,
                            allocation_amount=allocation.allocation_amount,
                            priority=self._calculate_priority(allocation),
                            expected_apy=allocation.expected_apy,
                            risk_level=self._determine_risk_level(allocation),
                            reasoning=allocation.reasoning
                        )
                        execution_plans.append(plan)
            
            # 6. 傳統規劃作為補充（混合模式）
            if not execution_plans:
                traditional_result = await self.planner.execute()
                if traditional_result.status == "success" and traditional_result.data:
                    execution_plans = traditional_result.data
            
            # 7. 更新策略學習歷史
            self._update_strategy_learning(strategy_analyses, optimal_portfolio)
            
            logger.info("planner_agno_execution_completed", 
                       plans_generated=len(execution_plans),
                       total_allocation=optimal_portfolio.total_allocation if optimal_portfolio else 0,
                       market_sentiment=market_assessment.overall_sentiment if market_assessment else "unknown")
            
            return AgentResult(
                agent_name=self.name,
                data=execution_plans,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "agno_enhanced": True,
                    "plans_generated": len(execution_plans),
                    "market_assessment": market_assessment.model_dump() if market_assessment and hasattr(market_assessment, 'model_dump') else None,
                    "strategy_analyses": {k: v.model_dump() if hasattr(v, 'model_dump') else str(v) 
                                        for k, v in strategy_analyses.items()},
                    "optimal_portfolio": optimal_portfolio.model_dump() if optimal_portfolio and hasattr(optimal_portfolio, 'model_dump') else None,
                    "ai_insights": {
                        "market_sentiment": market_assessment.overall_sentiment if market_assessment else "unknown",
                        "volatility_regime": market_assessment.volatility_regime if market_assessment else "unknown",
                        "best_strategy": self._identify_best_strategy(strategy_analyses),
                        "total_expected_apy": optimal_portfolio.expected_portfolio_apy if optimal_portfolio else 0.0,
                        "portfolio_risk_score": optimal_portfolio.portfolio_risk_score if optimal_portfolio else 0.0,
                        "diversification_score": optimal_portfolio.diversification_score if optimal_portfolio else 0.0
                    }
                }
            )
            
        except Exception as e:
            logger.error("planner_agno_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e), "agno_enhanced": True}
            )

    async def _analyze_market_environment_with_ai(self):
        """使用 AI 進行市場環境分析"""
        if not self.agno_market_analyst:
            return None
            
        # 檢查是否需要更新市場評估（1小時更新一次）
        if (self.last_market_assessment and 
            datetime.now() - self.last_market_assessment < timedelta(hours=1)):
            return None
            
        try:
            market_prompt = f"""
            Analyze current DeFi market environment for strategic planning:
            
            Current timestamp: {get_utc_timestamp().isoformat()}
            
            Please assess:
            1. Overall market sentiment and trend direction
            2. Volatility regime and stability factors
            3. Liquidity conditions across major DeFi protocols
            4. Yield environment and opportunity landscape
            
            Consider key factors:
            - Recent DeFi TVL trends and protocol adoption
            - Major token price movements and correlations
            - Regulatory developments affecting DeFi
            - Macroeconomic factors impacting risk appetite
            
            Focus on implications for LP strategies:
            - Delta-neutral suitability in current volatility
            - Ladder strategies and directional opportunities
            - Passive strategies and stable yield farming
            
            Provide actionable insights for portfolio allocation decisions.
            """
            
            response = await self.agno_market_analyst.arun(market_prompt)
            
            if response and hasattr(response, 'structured_content') and response.structured_content:
                assessment = response.structured_content
                self.last_market_assessment = datetime.now()
                
                logger.info("market_environment_analysis_completed", 
                           sentiment=assessment.overall_sentiment,
                           volatility_regime=assessment.volatility_regime,
                           confidence=assessment.confidence_score)
                return assessment
            else:
                logger.warning("market_environment_analysis_no_structured_response")
                return None
                
        except Exception as e:
            logger.error("market_environment_analysis_with_ai_failed", error=str(e))
            return None

    async def _analyze_strategy_with_ai(self, strategy_type: str, market_assessment, scored_pools):
        """使用 AI 進行策略分析"""
        if not self.agno_strategy_advisor:
            return None
            
        try:
            # 構建策略分析提示
            top_pools_info = []
            for pool in scored_pools[:5]:  # 分析前5個池子
                top_pools_info.append(f"- {pool.id}: Score {pool.score:.1f}, Hedgeable: {pool.hedgeable}")
            
            strategy_prompt = f"""
            Analyze the suitability of {strategy_type} strategy in current market conditions:
            
            Strategy Type: {strategy_type}
            
            Market Context:
            {market_assessment.model_dump_json(indent=2) if market_assessment and hasattr(market_assessment, 'model_dump_json') else "No market assessment available"}
            
            Top Scored Pools:
            {chr(10).join(top_pools_info)}
            
            Please analyze:
            1. Strategy suitability in current market regime
            2. Expected return potential and risk profile
            3. Capital requirements and execution complexity
            4. Optimal allocation percentage for this strategy
            5. Key advantages and disadvantages
            6. Major risks and mitigation approaches
            
            Consider:
            - Current volatility levels and trend direction
            - Available hedging instruments and costs
            - Pool quality and liquidity depth
            - Competition and yield compression factors
            
            Provide detailed analysis with practical implementation guidance.
            """
            
            response = await self.agno_strategy_advisor.arun(strategy_prompt)
            
            if response and hasattr(response, 'structured_content') and response.structured_content:
                analysis = response.structured_content
                logger.info("strategy_analysis_completed", 
                           strategy=strategy_type,
                           suitability_score=analysis.suitability_score,
                           expected_return=analysis.expected_return)
                return analysis
            else:
                logger.warning("strategy_analysis_no_structured_response", strategy=strategy_type)
                return None
                
        except Exception as e:
            logger.error("strategy_analysis_with_ai_failed", strategy=strategy_type, error=str(e))
            return None

    async def _optimize_portfolio_with_ai(self, strategy_analyses, market_assessment, scored_pools):
        """使用 AI 進行組合優化"""
        if not self.agno_portfolio_optimizer:
            return None
            
        try:
            # 構建組合優化提示
            total_capital = 100000  # 假設資本，實際應從配置獲取
            
            optimization_prompt = f"""
            Optimize portfolio allocation across DeFi LP strategies:
            
            Available Capital: ${total_capital:,.0f}
            
            Strategy Analyses:
            {json.dumps({k: v.model_dump() if hasattr(v, 'model_dump') else str(v) for k, v in strategy_analyses.items()}, indent=2)}
            
            Market Assessment:
            {market_assessment.model_dump_json(indent=2) if market_assessment and hasattr(market_assessment, 'model_dump_json') else "No market assessment available"}
            
            Top Pools Available:
            {json.dumps([{"id": p.id, "score": p.score, "hedgeable": p.hedgeable} for p in scored_pools[:10]], indent=2)}
            
            Please optimize:
            1. Allocation across strategies (delta_neutral, ladder_ss, passive_high_tvl)
            2. Specific pool selections and allocation amounts
            3. Risk management parameters and position sizing
            4. Expected portfolio performance metrics
            5. Rebalancing frequency and monitoring requirements
            
            Objectives:
            - Maximize risk-adjusted returns (Sharpe ratio)
            - Maintain diversification across strategies
            - Consider capital efficiency and execution costs
            - Align with current market conditions
            
            Provide specific dollar allocations and implementation guidance.
            """
            
            response = await self.agno_portfolio_optimizer.arun(optimization_prompt)
            
            if response and hasattr(response, 'structured_content') and response.structured_content:
                portfolio = response.structured_content
                logger.info("portfolio_optimization_completed", 
                           total_allocation=portfolio.total_allocation,
                           expected_apy=portfolio.expected_portfolio_apy,
                           risk_score=portfolio.portfolio_risk_score)
                return portfolio
            else:
                logger.warning("portfolio_optimization_no_structured_response")
                return None
                
        except Exception as e:
            logger.error("portfolio_optimization_with_ai_failed", error=str(e))
            return None

    def _calculate_priority(self, allocation) -> int:
        """計算執行優先級"""
        # 基於信心度和分配金額計算優先級
        confidence_weight = allocation.confidence_level * 50
        amount_weight = min(allocation.allocation_amount / 10000, 50)  # 標準化到50
        return int(confidence_weight + amount_weight)

    def _determine_risk_level(self, allocation) -> str:
        """確定風險級別"""
        if allocation.strategy_type == "passive_high_tvl":
            return "low"
        elif allocation.strategy_type == "delta_neutral":
            return "medium"
        else:  # ladder_ss
            return "high"

    def _identify_best_strategy(self, strategy_analyses) -> str:
        """識別最佳策略"""
        if not strategy_analyses:
            return "unknown"
        
        best_strategy = max(strategy_analyses.keys(), 
                          key=lambda k: strategy_analyses[k].suitability_score 
                          if hasattr(strategy_analyses[k], 'suitability_score') else 0)
        return best_strategy

    def _update_strategy_learning(self, strategy_analyses, optimal_portfolio):
        """更新策略學習歷史"""
        timestamp = get_utc_timestamp()
        
        # 記錄策略表現預測，用於後續學習
        learning_record = {
            "timestamp": timestamp,
            "strategy_analyses": {k: v.model_dump() if hasattr(v, 'model_dump') else str(v) 
                                for k, v in strategy_analyses.items()},
            "portfolio_allocation": optimal_portfolio.model_dump() if optimal_portfolio and hasattr(optimal_portfolio, 'model_dump') else None,
            "market_conditions": {
                "sentiment": getattr(optimal_portfolio, 'market_sentiment', 'unknown') if optimal_portfolio else 'unknown'
            }
        }
        
        # 存儲到歷史記錄（保留最近30天）
        if "strategy_decisions" not in self.strategy_performance_history:
            self.strategy_performance_history["strategy_decisions"] = []
        
        self.strategy_performance_history["strategy_decisions"].append(learning_record)
        
        # 清理舊記錄
        cutoff_time = timestamp - timedelta(days=30)
        self.strategy_performance_history["strategy_decisions"] = [
            record for record in self.strategy_performance_history["strategy_decisions"] 
            if record["timestamp"] > cutoff_time
        ]

    async def get_planning_insights(self) -> Dict[str, Any]:
        """獲取規劃洞察"""
        try:
            insights = {
                "agno_framework_available": self.agno_available,
                "market_analyst_available": self.agno_market_analyst is not None,
                "strategy_advisor_available": self.agno_strategy_advisor is not None,
                "portfolio_optimizer_available": self.agno_portfolio_optimizer is not None,
                "memory_enabled": self.enable_memory,
                "reasoning_enabled": self.use_reasoning,
                "knowledge_base_enabled": self.enable_knowledge_base,
                "market_data_enabled": self.enable_market_data,
                "last_market_assessment": self.last_market_assessment.isoformat() if self.last_market_assessment else None,
                "strategy_history_entries": len(self.strategy_performance_history.get("strategy_decisions", []))
            }
            
            return insights
            
        except Exception as e:
            logger.error("failed_to_get_planning_insights", error=str(e))
            return {"error": str(e)}

    async def cleanup(self) -> None:
        """清理 Agno 資源"""
        try:
            # 清理基礎 planner
            await self.planner.cleanup()
            
            # 清理策略歷史（可選）
            self.strategy_performance_history.clear()
            
            logger.info("planner_agno_cleanup_completed")
            
        except Exception as e:
            logger.error("planner_agno_cleanup_failed", error=str(e))

    def __str__(self) -> str:
        return f"PlannerAgnoAgent(agno_enhanced={self.agno_available}, reasoning={self.use_reasoning})"
