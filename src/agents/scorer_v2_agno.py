"""
Scorer V2 Agno Agent - Dy-Flow v3 + Agno Framework
基於 Agno Framework 的增強版池子評分系統
集成 AI 推理、結構化輸出和知識庫支持
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import json
import math

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    from agno.models.anthropic import Claude
    from agno.tools.reasoning import ReasoningTools
    from agno.storage.sqlite import SqliteStorage
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    # 定義替代類
    class BaseModel:
        pass
    class Agent:
        pass

# Dy-Flow v3 imports
from .base_agent import BaseAgent
# from .scorer_v2 import ScorerV2Agent, PoolDetailedInfo, DynamicWeights  # 暂时注释掉
# from ..utils.models_v3 import PoolRaw, PoolScore, AgentResult  # 暂时注释掉
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

# 临时定义缺失的类
@dataclass
class PoolRaw:
    id: str
    name: str
    tvl: float
    volume_24h: float
    fees_24h: float

@dataclass
class PoolDetailedInfo:
    id: str
    name: str
    tvl: float
    volume_24h: float
    fees_24h: float
    volatility: float = 0.0
    liquidity_depth: float = 0.0

@dataclass
class PoolScore:
    id: str
    score: float
    hedgeable: bool

@dataclass
class AgentResult:
    agent_name: str
    data: Any
    timestamp: Any
    status: str
    metadata: Optional[Dict[str, Any]] = None

logger = structlog.get_logger(__name__)

# ========== Agno 結構化輸出模型 ==========

if AGNO_AVAILABLE:
    class PoolFactorAnalysis(BaseModel):
        """池子因子分析 - Agno 結構化輸出"""
        pool_id: str = Field(..., description="池子ID")
        fee_score: float = Field(..., ge=0.0, le=100.0, description="費率評分 (0-100)")
        volume_score: float = Field(..., ge=0.0, le=100.0, description="交易量評分 (0-100)")
        tvl_score: float = Field(..., ge=0.0, le=100.0, description="TVL評分 (0-100)")
        token_quality_score: float = Field(..., ge=0.0, le=100.0, description="代幣品質評分 (0-100)")
        liquidity_depth_score: float = Field(..., ge=0.0, le=100.0, description="流動性深度評分 (0-100)")
        volatility_score: float = Field(..., ge=0.0, le=100.0, description="波動率評分 (0-100)")
        
        overall_score: float = Field(..., ge=0.0, le=100.0, description="綜合評分 (0-100)")
        confidence_level: float = Field(..., ge=0.0, le=1.0, description="評分信心度 (0.0-1.0)")
        
        risk_factors: List[str] = Field(default_factory=list, description="識別的風險因子")
        opportunities: List[str] = Field(default_factory=list, description="識別的機會點")
        
        reasoning: str = Field(..., description="評分推理過程")
        hedgeable_recommendation: bool = Field(..., description="是否推薦進行對沖")

    class MarketConditionAssessment(BaseModel):
        """市場狀況評估 - Agno 結構化輸出"""
        volatility_level: str = Field(..., description="波動率水平", pattern="^(low|medium|high)$")
        volume_trend: str = Field(..., description="交易量趨勢", pattern="^(increasing|stable|decreasing)$")
        market_sentiment: str = Field(..., description="市場情緒", pattern="^(bullish|neutral|bearish)$")
        risk_appetite: str = Field(..., description="風險偏好", pattern="^(conservative|moderate|aggressive)$")
        
        confidence_score: float = Field(..., ge=0.0, le=1.0, description="評估信心度")
        key_observations: List[str] = Field(default_factory=list, description="關鍵市場觀察")
        weight_recommendations: Dict[str, float] = Field(default_factory=dict, description="權重調整建議")

    class DynamicWeightAdjustment(BaseModel):
        """動態權重調整 - Agno 結構化輸出"""
        fee_tvl: float = Field(..., ge=0.0, le=1.0, description="費率權重")
        volume_score: float = Field(..., ge=0.0, le=1.0, description="交易量權重")
        tvl_score: float = Field(..., ge=0.0, le=1.0, description="TVL權重")
        token_quality: float = Field(..., ge=0.0, le=1.0, description="代幣品質權重")
        liquidity_depth: float = Field(..., ge=0.0, le=1.0, description="流動性深度權重")
        volatility_score: float = Field(..., ge=0.0, le=1.0, description="波動率權重")
        
        adjustment_reasoning: str = Field(..., description="權重調整推理")
        market_context: str = Field(..., description="市場背景分析")
else:
    # 降級數據類
    @dataclass
    class PoolFactorAnalysis:
        pool_id: str
        fee_score: float
        volume_score: float
        tvl_score: float
        token_quality_score: float
        liquidity_depth_score: float
        volatility_score: float
        overall_score: float
        confidence_level: float
        risk_factors: List[str]
        opportunities: List[str]
        reasoning: str
        hedgeable_recommendation: bool

# ========== Agno Enhanced Scorer Agent ==========

class ScorerV2AgnoAgent(BaseAgent):
    """基於 Agno Framework 的增強版 Scorer Agent"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        # 檢查 Agno 可用性
        self.agno_available = AGNO_AVAILABLE
        
        if not self.agno_available:
            logger.warning("agno_framework_not_available", 
                         message="Agno framework not installed, falling back to traditional scoring")
        
        # Agno 相關配置
        agent_specific_config = self.agent_config or {}
        agno_config = agent_specific_config.get('agno', {})
        
        # LLM 模型選擇
        self.primary_model = agno_config.get('primary_model', 'gpt-4o')
        self.secondary_model = agno_config.get('secondary_model', 'claude-3-7-sonnet-latest')
        self.use_reasoning = agno_config.get('use_reasoning', True)
        self.enable_memory = agno_config.get('enable_memory', True)
        self.debug_mode = agno_config.get('debug_mode', False)
        
        # Agno Agent 實例
        self.agno_market_analyzer: Optional[Agent] = None
        self.agno_weight_optimizer: Optional[Agent] = None
        self.agno_pool_analyzer: Optional[Agent] = None
        
        # Memory 存儲
        self.memory_storage = None
        
    async def initialize(self) -> None:
        """初始化 Agno Agents 和基礎組件"""
        await super().initialize()
        await self.scorer_v2.initialize()
        
        if not self.agno_available:
            logger.info("scorer_v2_fallback_initialized", message="Using traditional scoring only")
            self.is_initialized = True
            return
        
        try:
            # 初始化 Memory 存儲
            if self.enable_memory:
                self.memory_storage = SqliteStorage(
                    table_name="scorer_v2_agno_memory",
                    db_file="data/agno_memory/scorer_v2_sessions.db",
                    auto_upgrade_schema=True
                )
            
            # 初始化市場分析 Agent (启用正确的结构化输出)
            self.agno_market_analyzer = Agent(
                name="MarketConditionAnalyzer",
                role="Analyze DeFi market conditions and provide strategic insights",
                agent_id="scorer-v2-market-analyzer",
                model=OpenAIChat(id=self.primary_model),
                tools=[ReasoningTools(add_instructions=True)] if self.use_reasoning else [],
                reasoning=False,  # 暂时禁用推理避免复杂性
                response_model=MarketConditionAssessment,  # 重新启用结构化输出
                storage=self.memory_storage,
                session_id="market_analysis_session",
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a DeFi market analyst specializing in liquidity pool evaluation.",
                    "Analyze market conditions including volatility, volume trends, and sentiment.",
                    "You MUST return pure JSON with the exact structure required.",
                    "Focus on factors that affect LP strategies: impermanent loss risk, fee generation potential, and hedging opportunities.",
                    "Be precise with your confidence scores and provide actionable insights.",
                    "Return only valid JSON format - no additional text or explanation."
                ]
            )
            
            # 初始化權重優化 Agent (启用正确的结构化输出)
            self.agno_weight_optimizer = Agent(
                name="DynamicWeightOptimizer",
                role="Optimize scoring weights based on market conditions",
                agent_id="scorer-v2-weight-optimizer",
                model=OpenAIChat(id=self.primary_model),  # 使用 OpenAI 而不是 Claude
                tools=[ReasoningTools(add_instructions=True)] if self.use_reasoning else [],
                reasoning=False,  # 暂时禁用推理避免复杂性
                response_model=DynamicWeightAdjustment,  # 重新启用结构化输出
                storage=self.memory_storage,
                session_id="weight_optimization_session",
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are a quantitative analyst specializing in dynamic factor weight optimization.",
                    "Given market conditions, adjust scoring weights to maximize risk-adjusted returns.",
                    "You MUST return pure JSON with the exact structure required.",
                    "Consider the following factors: fee_tvl, volume_score, tvl_score, token_quality, liquidity_depth, volatility_score.",
                    "Base weights should sum to 1.0. Provide clear reasoning for each adjustment.",
                    "Focus on practical DeFi strategies: Delta-Neutral LP, Ladder Single-Sided, and Passive High-TVL.",
                    "Account for current market volatility, volume trends, and risk appetite.",
                    "Return only valid JSON format - no additional text or explanation."
                ]
            )
            
            # 初始化池子分析 Agent (启用正确的结构化输出)
            self.agno_pool_analyzer = Agent(
                name="AdvancedPoolAnalyzer",
                role="Perform deep analysis of liquidity pools with AI reasoning",
                agent_id="scorer-v2-pool-analyzer",
                model=OpenAIChat(id=self.primary_model),
                tools=[ReasoningTools(add_instructions=True)] if self.use_reasoning else [],
                reasoning=False,  # 暂时禁用推理避免复杂性
                response_model=PoolFactorAnalysis,  # 重新启用结构化输出
                storage=self.memory_storage,
                add_datetime_to_instructions=True,
                show_tool_calls=self.debug_mode,
                instructions=[
                    "You are an expert DeFi pool analyst with deep knowledge of liquidity provision strategies.",
                    "Analyze liquidity pools across multiple dimensions: fees, volume, TVL, token quality, liquidity depth, and volatility.",
                    "You MUST return pure JSON with the exact structure required.",
                    "Identify specific risk factors and opportunity areas for each pool.",
                    "Consider hedging feasibility based on token pairs and liquidity characteristics.",
                    "Your analysis should support automated LP management and risk control decisions.",
                    "Return only valid JSON format - no additional text or explanation."
                ]
            )
            
            logger.info("scorer_v2_agno_initialized",
                       primary_model=self.primary_model,
                       secondary_model=self.secondary_model,
                       reasoning_enabled=self.use_reasoning,
                       memory_enabled=self.enable_memory,
                       debug_mode=self.debug_mode)
            
            self.is_initialized = True
            
        except Exception as e:
            logger.error("scorer_v2_agno_initialization_failed", error=str(e))
            # 降級到傳統模式
            self.agno_available = False
            logger.warning("falling_back_to_traditional_scoring", error=str(e))
            self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行 Agno 增強版池子評分"""
        if not self.is_initialized:
            await self.initialize()
        
        # 如果 Agno 不可用，使用傳統評分
        if not self.agno_available:
            traditional_result = await self.scorer_v2.execute()
            traditional_result.metadata = traditional_result.metadata or {}
            traditional_result.metadata["agno_enhanced"] = False
            traditional_result.metadata["fallback_mode"] = True
            return traditional_result
        
        try:
            logger.info("scorer_v2_agno_execution_started")
            
            # 1. 獲取基礎數據
            pool_raws = self.scorer_v2._get_pool_raws()
            if not pool_raws:
                logger.info("no_pools_to_score_agno")
                return AgentResult(
                    agent_name=self.name,
                    data=[],
                    timestamp=get_utc_timestamp(),
                    status="success",
                    metadata={"message": "沒有需要評分的池子", "agno_enhanced": True}
                )
            
            # 2. AI 市場分析
            market_assessment = await self._analyze_market_with_ai()
            
            # 3. AI 動態權重優化
            optimized_weights = await self._optimize_weights_with_ai(market_assessment)
            
            # 4. 批量池子分析
            pool_scores = []
            agno_analyses = []
            
            for pool_raw in pool_raws[:10]:  # 限制前10個池子進行深度 AI 分析
                try:
                    pool_detail = await self.scorer_v2._get_pool_detailed_info(pool_raw)
                    if not pool_detail:
                        continue
                    
                    # AI 增強分析
                    agno_analysis = await self._analyze_pool_with_ai(pool_raw, pool_detail, market_assessment)
                    if agno_analysis:
                        # 基於 AI 分析創建 PoolScore
                        pool_score = PoolScore(
                            id=pool_raw.id,
                            score=agno_analysis.overall_score,
                            hedgeable=agno_analysis.hedgeable_recommendation
                        )
                        pool_scores.append(pool_score)
                        agno_analyses.append(agno_analysis)
                    
                except Exception as e:
                    logger.error("agno_pool_analysis_failed", 
                               pool_id=pool_raw.id, 
                               error=str(e))
            
            # 5. 對剩餘池子使用傳統評分（混合模式）
            if len(pool_raws) > 10:
                traditional_result = await self.scorer_v2.execute()
                if traditional_result.status == "success" and traditional_result.data:
                    pool_scores.extend(traditional_result.data)
            
            # 6. 排序和最終處理
            pool_scores = sorted(pool_scores, key=lambda x: x.score, reverse=True)
            
            logger.info("scorer_v2_agno_execution_completed", 
                       pools_processed=len(pool_raws),
                       pools_scored=len(pool_scores),
                       ai_analyzed_pools=len(agno_analyses))
            
            return AgentResult(
                agent_name=self.name,
                data=pool_scores,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "agno_enhanced": True,
                    "pools_processed": len(pool_raws),
                    "pools_scored": len(pool_scores),
                    "ai_analyzed_pools": len(agno_analyses),
                    "avg_score": sum(p.score for p in pool_scores) / len(pool_scores) if pool_scores else 0,
                    "avg_confidence": sum(a.confidence_level for a in agno_analyses) / len(agno_analyses) if agno_analyses else 0,
                    "market_assessment": market_assessment.model_dump() if market_assessment and hasattr(market_assessment, 'model_dump') else None,
                    "weight_optimization": optimized_weights.model_dump() if optimized_weights and hasattr(optimized_weights, 'model_dump') else None,
                    "top_pools_analysis": [a.model_dump() if hasattr(a, 'model_dump') else str(a) for a in agno_analyses[:5]],
                    "ai_insights": {
                        "total_risk_factors": sum(len(a.risk_factors) for a in agno_analyses),
                        "total_opportunities": sum(len(a.opportunities) for a in agno_analyses),
                        "hedgeable_pools": sum(1 for a in agno_analyses if a.hedgeable_recommendation)
                    }
                }
            )
            
        except Exception as e:
            logger.error("scorer_v2_agno_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e), "agno_enhanced": True}
            )

    async def _analyze_market_with_ai(self):
        """使用 AI 進行市場狀況分析"""
        if not self.agno_market_analyzer:
            return None
            
        try:
            # 構建市場分析提示
            market_prompt = f"""
            Analyze current DeFi market conditions based on the following context:
            
            Current timestamp: {get_utc_timestamp().isoformat()}
            
            Please assess:
            1. Market volatility level (low/medium/high)
            2. Volume trends in major DeFi protocols  
            3. Overall market sentiment (bullish/neutral/bearish)
            4. Current risk appetite in DeFi space (conservative/moderate/aggressive)
            
            Consider recent market events, TVL flows, and general crypto market conditions.
            Provide weight adjustment recommendations for pool scoring factors.
            
            Focus on how these conditions should influence LP strategy selection:
            - Delta-Neutral LP (hedged strategies)
            - Ladder Single-Sided (directional bets)  
            - Passive High-TVL (stable yield farming)
            """
            
            response = await self.agno_market_analyzer.arun(market_prompt)
            
            # 處理不同的響應格式
            assessment = None
            if response:
                if hasattr(response, 'structured_content') and response.structured_content:
                    assessment = response.structured_content
                elif hasattr(response, 'content') and response.content:
                    # 嘗試解析為結構化內容
                    try:
                        # 如果是 Pydantic 模型實例
                        if hasattr(response.content, 'volatility_level'):
                            assessment = response.content
                        else:
                            # 嘗試從文本解析
                            assessment = None
                    except Exception:
                        assessment = None
                elif isinstance(response, MarketConditionAssessment):
                    assessment = response
            
            if assessment:
                logger.info("market_analysis_completed", 
                           volatility=getattr(assessment, 'volatility_level', 'unknown'),
                           sentiment=getattr(assessment, 'market_sentiment', 'unknown'),
                           confidence=getattr(assessment, 'confidence_score', 0.0))
                return assessment
            else:
                logger.warning("market_analysis_no_structured_response", response_type=type(response).__name__)
                return None
                
        except Exception as e:
            logger.error("market_analysis_with_ai_failed", error=str(e))
            return None

    async def _optimize_weights_with_ai(self, market_assessment):
        """使用 AI 優化評分權重"""
        if not self.agno_weight_optimizer:
            return None
            
        try:
            # 構建權重優化提示
            base_weights = self.scorer_v2.base_weights
            
            weight_prompt = f"""
            Optimize scoring weights for liquidity pool evaluation based on market conditions.
            
            Current base weights:
            {json.dumps(base_weights, indent=2)}
            
            Market assessment:
            {market_assessment.model_dump_json(indent=2) if market_assessment and hasattr(market_assessment, 'model_dump_json') else "No market assessment available"}
            
            Please provide optimized weights that:
            1. Sum to 1.0 exactly
            2. Account for current market volatility and sentiment
            3. Balance risk and return for LP strategies
            4. Consider the effectiveness of each factor in current conditions
            
            Weight factors:
            - fee_tvl: Annual fee generation potential
            - volume_score: Trading activity and liquidity
            - tvl_score: Pool size and stability
            - token_quality: Token reliability and hedgeability  
            - liquidity_depth: Available liquidity for large trades
            - volatility_score: Price stability (inverse of volatility)
            
            Provide clear reasoning for each weight adjustment.
            """
            
            response = await self.agno_weight_optimizer.arun(weight_prompt)
            
            # 處理不同的響應格式
            weight_adjustment = None
            if response:
                if hasattr(response, 'structured_content') and response.structured_content:
                    weight_adjustment = response.structured_content
                elif hasattr(response, 'content') and response.content:
                    try:
                        if hasattr(response.content, 'fee_tvl'):
                            weight_adjustment = response.content
                    except Exception:
                        weight_adjustment = None
                elif isinstance(response, DynamicWeightAdjustment):
                    weight_adjustment = response
            
            if weight_adjustment:
                
                # 驗證權重總和
                total_weight = (weight_adjustment.fee_tvl + weight_adjustment.volume_score + 
                              weight_adjustment.tvl_score + weight_adjustment.token_quality + 
                              weight_adjustment.liquidity_depth + weight_adjustment.volatility_score)
                
                if abs(total_weight - 1.0) > 0.01:
                    logger.warning("weight_sum_not_normalized", total=total_weight)
                    # 正規化權重
                    factor = 1.0 / total_weight
                    weight_adjustment.fee_tvl *= factor
                    weight_adjustment.volume_score *= factor
                    weight_adjustment.tvl_score *= factor
                    weight_adjustment.token_quality *= factor
                    weight_adjustment.liquidity_depth *= factor
                    weight_adjustment.volatility_score *= factor
                
                logger.info("weight_optimization_completed")
                return weight_adjustment
            else:
                logger.warning("weight_optimization_no_structured_response")
                return None
                
        except Exception as e:
            logger.error("weight_optimization_with_ai_failed", error=str(e))
            return None

    async def _analyze_pool_with_ai(self, pool_raw: PoolRaw, pool_detail: PoolDetailedInfo, market_assessment):
        """使用 AI 進行深度池子分析"""
        if not self.agno_pool_analyzer:
            return None
            
        try:
            # 構建池子分析提示
            pool_prompt = f"""
            Perform comprehensive analysis of this liquidity pool:
            
            Pool Data:
            - ID: {pool_raw.id}
            - Chain: {pool_raw.chain}
            - TVL: ${pool_raw.tvl_usd:,.2f}
            - 24h Fees: ${pool_raw.fee24h:,.2f}
            - Fee/TVL Ratio: {pool_raw.fee_tvl:.2f}%
            
            Pool Details:
            - Token Pair: {pool_detail.token0_symbol}/{pool_detail.token1_symbol}
            - Fee Rate: {pool_detail.fee_rate:.4f}%
            - Liquidity Depth: ${pool_detail.liquidity_depth:,.2f}
            - Price Volatility: {pool_detail.price_volatility:.4f}
            - 24h Volume: ${pool_detail.volume_24h:,.2f}
            
            Market Context:
            {market_assessment.model_dump_json(indent=2) if market_assessment and hasattr(market_assessment, 'model_dump_json') else "No market context available"}
            
            Please provide:
            1. Individual factor scores (0-100) for each dimension
            2. Overall composite score with confidence level
            3. Risk factors and opportunities identification
            4. Hedging feasibility assessment
            5. Detailed reasoning for the scoring
            
            Consider:
            - Token quality and stability
            - Liquidity efficiency and depth
            - Fee generation sustainability  
            - Impermanent loss risk
            - Market making profitability
            - Hedge execution feasibility
            """
            
            response = await self.agno_pool_analyzer.arun(pool_prompt)
            
            # 處理不同的響應格式
            analysis = None
            if response:
                if hasattr(response, 'structured_content') and response.structured_content:
                    analysis = response.structured_content
                elif hasattr(response, 'content') and response.content:
                    try:
                        if hasattr(response.content, 'overall_score'):
                            analysis = response.content
                    except Exception:
                        analysis = None
                elif isinstance(response, PoolFactorAnalysis):
                    analysis = response
            
            if analysis:
                logger.debug("pool_ai_analysis_completed", 
                           pool_id=pool_raw.id,
                           score=getattr(analysis, 'overall_score', 0),
                           confidence=getattr(analysis, 'confidence_level', 0.0))
                return analysis
            else:
                logger.warning("pool_analysis_no_structured_response", 
                             pool_id=pool_raw.id,
                             response_type=type(response).__name__)
                return None
                
        except Exception as e:
            logger.error("pool_analysis_with_ai_failed", 
                        pool_id=pool_raw.id, 
                        error=str(e))
            return None

    async def get_ai_insights(self) -> Dict[str, Any]:
        """獲取 AI 分析洞察摘要"""
        try:
            insights = {
                "agno_framework_available": self.agno_available,
                "market_analysis_available": self.agno_market_analyzer is not None,
                "weight_optimization_available": self.agno_weight_optimizer is not None,
                "pool_analysis_available": self.agno_pool_analyzer is not None,
                "memory_enabled": self.enable_memory,
                "reasoning_enabled": self.use_reasoning,
                "models_used": {
                    "primary": self.primary_model,
                    "secondary": self.secondary_model
                } if self.agno_available else None
            }
            
            # 如果有 memory storage，獲取會話統計
            if self.memory_storage:
                insights["memory_stats"] = {
                    "sessions_active": 3,  # 佔位符
                    "total_interactions": 0  # 佔位符
                }
            
            return insights
            
        except Exception as e:
            logger.error("failed_to_get_ai_insights", error=str(e))
            return {"error": str(e)}

    async def cleanup(self) -> None:
        """清理 Agno 資源"""
        try:
            # 清理基礎 scorer
            await self.scorer_v2.cleanup()
            
            # Agno agents 會自動管理清理，但可以在此添加特定邏輯
            if self.memory_storage:
                # TODO: 如果需要，可以在此處理 memory storage 的特定清理
                pass
            
            logger.info("scorer_v2_agno_cleanup_completed")
            
        except Exception as e:
            logger.error("scorer_v2_agno_cleanup_failed", error=str(e))

    def __str__(self) -> str:
        return f"ScorerV2AgnoAgent(agno_enhanced={self.agno_available}, reasoning={self.use_reasoning})"
