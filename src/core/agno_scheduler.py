"""
Enhanced AGNO Scheduler - DyFlow v3 + Agno Framework
增強版 AGNO 調度器，支持依賴管理、故障轉移和事件驅動執行
"""

import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple, Callable
from datetime import datetime, timedelta
from enum import Enum
import structlog
from dataclasses import dataclass, field
import json

logger = structlog.get_logger(__name__)

# ========== Scheduler Models ==========

class AgentStatus(Enum):
    """Agent 狀態枚舉"""
    IDLE = "idle"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    RETRYING = "retrying"
    DISABLED = "disabled"

class ExecutionMode(Enum):
    """執行模式枚舉"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    DEPENDENCY_BASED = "dependency_based"
    EVENT_DRIVEN = "event_driven"

@dataclass
class AgentConfig:
    """Agent 配置"""
    instance: Any
    dependencies: List[str] = field(default_factory=list)
    max_retries: int = 3
    retry_delay_seconds: int = 5
    timeout_seconds: int = 300
    critical: bool = False  # 關鍵 Agent，失敗時停止整個工作流
    enabled: bool = True
    execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL

@dataclass
class ExecutionResult:
    """執行結果"""
    agent_name: str
    status: AgentStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    result: Any = None
    error: Optional[str] = None
    retry_count: int = 0

@dataclass
class WorkflowState:
    """工作流狀態"""
    workflow_id: str
    start_time: datetime
    status: str = "running"
    completed_agents: Set[str] = field(default_factory=set)
    failed_agents: Set[str] = field(default_factory=set)
    execution_results: Dict[str, ExecutionResult] = field(default_factory=dict)

# ========== Enhanced AGNO Scheduler ==========

class AGNOScheduler:
    """增強版 AGNO 調度器"""

    def __init__(self, config: Any, database: Any):
        self.config = config
        self.database = database

        # Agent 註冊表
        self.agents: Dict[str, AgentConfig] = {}

        # 執行狀態
        self.current_workflow: Optional[WorkflowState] = None
        self.agent_states: Dict[str, AgentStatus] = {}

        # 故障轉移配置
        self.failover_config = {
            'max_workflow_retries': 3,
            'agent_failure_threshold': 0.3,  # 30% Agent 失敗時停止工作流
            'enable_circuit_breaker': True,
            'circuit_breaker_threshold': 5,  # 5次連續失敗後斷路
            'circuit_breaker_timeout': 300   # 5分鐘後重試
        }

        # 斷路器狀態
        self.circuit_breakers: Dict[str, Dict[str, Any]] = {}

        # 事件處理器
        self.event_handlers: Dict[str, List[Callable]] = {}

        # 執行歷史
        self.execution_history: List[WorkflowState] = []

        logger.info("agno_scheduler_initialized",
                   failover_enabled=self.failover_config['enable_circuit_breaker'])

    async def initialize(self) -> bool:
        """初始化調度器"""
        try:
            # 初始化所有註冊的 Agent
            for agent_name, agent_config in self.agents.items():
                try:
                    if hasattr(agent_config.instance, 'initialize'):
                        await agent_config.instance.initialize()
                    self.agent_states[agent_name] = AgentStatus.IDLE
                    logger.info("agent_initialized", agent=agent_name)
                except Exception as e:
                    logger.error("agent_initialization_failed",
                               agent=agent_name, error=str(e))
                    self.agent_states[agent_name] = AgentStatus.FAILED

                    if agent_config.critical:
                        logger.error("critical_agent_initialization_failed",
                                   agent=agent_name)
                        return False

            logger.info("agno_scheduler_initialization_completed",
                       total_agents=len(self.agents),
                       failed_agents=len([s for s in self.agent_states.values()
                                        if s == AgentStatus.FAILED]))
            return True

        except Exception as e:
            logger.error("agno_scheduler_initialization_failed", error=str(e))
            return False

    def register_agent(self, name: str, agent: Any,
                      dependencies: Optional[List[str]] = None,
                      max_retries: int = 3,
                      timeout_seconds: int = 300,
                      critical: bool = False,
                      execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL) -> None:
        """註冊 Agent"""
        try:
            agent_config = AgentConfig(
                instance=agent,
                dependencies=dependencies or [],
                max_retries=max_retries,
                timeout_seconds=timeout_seconds,
                critical=critical,
                execution_mode=execution_mode
            )

            self.agents[name] = agent_config
            self.agent_states[name] = AgentStatus.IDLE

            # 初始化斷路器
            self.circuit_breakers[name] = {
                'failure_count': 0,
                'last_failure_time': None,
                'state': 'closed'  # closed, open, half_open
            }

            logger.info("agent_registered",
                       agent=name,
                       dependencies=dependencies,
                       critical=critical)

        except Exception as e:
            logger.error("agent_registration_failed", agent=name, error=str(e))

    def add_event_handler(self, event_type: str, handler: Callable) -> None:
        """添加事件處理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        logger.info("event_handler_added", event_type=event_type)

    async def emit_event(self, event_type: str, data: Any) -> None:
        """發送事件"""
        try:
            handlers = self.event_handlers.get(event_type, [])
            for handler in handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(data)
                    else:
                        handler(data)
                except Exception as e:
                    logger.error("event_handler_failed",
                               event_type=event_type,
                               error=str(e))
        except Exception as e:
            logger.error("event_emission_failed",
                        event_type=event_type,
                        error=str(e))

    def _resolve_execution_order(self, agent_names: List[str]) -> List[List[str]]:
        """解析執行順序（基於依賴關係）"""
        try:
            # 構建依賴圖
            dependency_graph = {}
            in_degree = {}

            for name in agent_names:
                if name not in self.agents:
                    logger.warning("agent_not_registered", agent=name)
                    continue

                dependencies = self.agents[name].dependencies
                dependency_graph[name] = dependencies
                in_degree[name] = len(dependencies)

            # 拓撲排序
            execution_layers = []
            remaining_agents = set(agent_names)

            while remaining_agents:
                # 找到沒有依賴的 Agent
                ready_agents = []
                for agent in remaining_agents:
                    if agent in in_degree and in_degree[agent] == 0:
                        ready_agents.append(agent)

                if not ready_agents:
                    # 檢查是否有循環依賴
                    logger.error("circular_dependency_detected",
                               remaining_agents=list(remaining_agents))
                    # 強制執行剩餘 Agent
                    ready_agents = list(remaining_agents)

                execution_layers.append(ready_agents)

                # 更新依賴計數
                for agent in ready_agents:
                    remaining_agents.remove(agent)
                    for other_agent in remaining_agents:
                        if agent in dependency_graph.get(other_agent, []):
                            in_degree[other_agent] -= 1

            logger.info("execution_order_resolved",
                       layers=len(execution_layers),
                       total_agents=len(agent_names))

            return execution_layers

        except Exception as e:
            logger.error("execution_order_resolution_failed", error=str(e))
            # 降級到順序執行
            return [[agent] for agent in agent_names]

    async def execute_workflow(self, agent_names: List[str],
                             workflow_id: Optional[str] = None) -> WorkflowState:
        """執行工作流"""
        try:
            workflow_id = workflow_id or f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 創建工作流狀態
            self.current_workflow = WorkflowState(
                workflow_id=workflow_id,
                start_time=datetime.now()
            )

            logger.info("workflow_execution_started",
                       workflow_id=workflow_id,
                       agents=agent_names)

            # 發送工作流開始事件
            await self.emit_event("workflow_started", {
                "workflow_id": workflow_id,
                "agents": agent_names
            })

            # 解析執行順序
            execution_layers = self._resolve_execution_order(agent_names)

            # 逐層執行
            for layer_index, layer_agents in enumerate(execution_layers):
                logger.info("executing_layer",
                           layer=layer_index + 1,
                           agents=layer_agents)

                # 並行執行同一層的 Agent
                layer_results = await self._execute_agent_batch(layer_agents)

                # 檢查關鍵 Agent 失敗
                critical_failures = []
                for agent_name, result in layer_results.items():
                    if (result.status == AgentStatus.FAILED and
                        self.agents[agent_name].critical):
                        critical_failures.append(agent_name)

                if critical_failures:
                    logger.error("critical_agents_failed",
                               agents=critical_failures)
                    self.current_workflow.status = "failed"
                    break

                # 檢查失敗率
                failed_count = len([r for r in layer_results.values()
                                  if r.status == AgentStatus.FAILED])
                failure_rate = failed_count / len(layer_agents) if layer_agents else 0

                if failure_rate > self.failover_config['agent_failure_threshold']:
                    logger.error("high_failure_rate_detected",
                               failure_rate=failure_rate,
                               threshold=self.failover_config['agent_failure_threshold'])
                    self.current_workflow.status = "failed"
                    break

            # 完成工作流
            if self.current_workflow.status != "failed":
                self.current_workflow.status = "completed"

            # 發送工作流完成事件
            await self.emit_event("workflow_completed", {
                "workflow_id": workflow_id,
                "status": self.current_workflow.status,
                "execution_results": self.current_workflow.execution_results
            })

            # 保存到歷史
            self.execution_history.append(self.current_workflow)

            logger.info("workflow_execution_completed",
                       workflow_id=workflow_id,
                       status=self.current_workflow.status,
                       total_agents=len(agent_names),
                       completed_agents=len(self.current_workflow.completed_agents),
                       failed_agents=len(self.current_workflow.failed_agents))

            return self.current_workflow

        except Exception as e:
            logger.error("workflow_execution_failed",
                        workflow_id=workflow_id,
                        error=str(e))

            if self.current_workflow:
                self.current_workflow.status = "error"
                return self.current_workflow

            # 創建錯誤狀態
            return WorkflowState(
                workflow_id=workflow_id or "unknown",
                start_time=datetime.now(),
                status="error"
            )

    async def _execute_agent_batch(self, agent_names: List[str]) -> Dict[str, ExecutionResult]:
        """並行執行一批 Agent"""
        try:
            # 創建執行任務
            tasks = []
            for agent_name in agent_names:
                if agent_name in self.agents and self.agents[agent_name].enabled:
                    task = asyncio.create_task(
                        self._execute_single_agent(agent_name),
                        name=f"agent_{agent_name}"
                    )
                    tasks.append((agent_name, task))

            # 等待所有任務完成
            results = {}
            for agent_name, task in tasks:
                try:
                    result = await task
                    results[agent_name] = result
                except Exception as e:
                    logger.error("agent_task_failed", agent=agent_name, error=str(e))
                    results[agent_name] = ExecutionResult(
                        agent_name=agent_name,
                        status=AgentStatus.FAILED,
                        start_time=datetime.now(),
                        end_time=datetime.now(),
                        error=str(e)
                    )

            return results

        except Exception as e:
            logger.error("agent_batch_execution_failed", error=str(e))
            return {}

    async def _execute_single_agent(self, agent_name: str) -> ExecutionResult:
        """執行單個 Agent"""
        agent_config = self.agents[agent_name]
        start_time = datetime.now()

        # 檢查斷路器
        if not self._check_circuit_breaker(agent_name):
            return ExecutionResult(
                agent_name=agent_name,
                status=AgentStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now(),
                error="Circuit breaker is open"
            )

        # 執行 Agent（帶重試）
        for retry_count in range(agent_config.max_retries + 1):
            try:
                self.agent_states[agent_name] = AgentStatus.RUNNING

                # 發送 Agent 開始事件
                await self.emit_event("agent_started", {
                    "agent_name": agent_name,
                    "retry_count": retry_count
                })

                # 執行 Agent
                result = await self._run_agent_with_timeout(
                    agent_config.instance,
                    agent_config.timeout_seconds
                )

                # 成功執行
                self.agent_states[agent_name] = AgentStatus.SUCCESS
                self._reset_circuit_breaker(agent_name)

                if self.current_workflow:
                    self.current_workflow.completed_agents.add(agent_name)

                execution_result = ExecutionResult(
                    agent_name=agent_name,
                    status=AgentStatus.SUCCESS,
                    start_time=start_time,
                    end_time=datetime.now(),
                    result=result,
                    retry_count=retry_count
                )

                # 發送 Agent 成功事件
                await self.emit_event("agent_completed", {
                    "agent_name": agent_name,
                    "status": "success",
                    "result": result
                })

                if self.current_workflow:
                    self.current_workflow.execution_results[agent_name] = execution_result

                return execution_result

            except Exception as e:
                logger.error("agent_execution_attempt_failed",
                           agent=agent_name,
                           retry=retry_count,
                           error=str(e))

                if retry_count < agent_config.max_retries:
                    self.agent_states[agent_name] = AgentStatus.RETRYING
                    await asyncio.sleep(agent_config.retry_delay_seconds)
                else:
                    # 最終失敗
                    self.agent_states[agent_name] = AgentStatus.FAILED
                    self._trigger_circuit_breaker(agent_name)

                    if self.current_workflow:
                        self.current_workflow.failed_agents.add(agent_name)

                    execution_result = ExecutionResult(
                        agent_name=agent_name,
                        status=AgentStatus.FAILED,
                        start_time=start_time,
                        end_time=datetime.now(),
                        error=str(e),
                        retry_count=retry_count
                    )

                    # 發送 Agent 失敗事件
                    await self.emit_event("agent_failed", {
                        "agent_name": agent_name,
                        "error": str(e),
                        "retry_count": retry_count
                    })

                    if self.current_workflow:
                        self.current_workflow.execution_results[agent_name] = execution_result

                    return execution_result

    async def _run_agent_with_timeout(self, agent: Any, timeout_seconds: int) -> Any:
        """帶超時執行 Agent"""
        try:
            # 檢查 Agent 是否有 execute 方法
            if hasattr(agent, 'execute'):
                execute_method = agent.execute
            elif hasattr(agent, 'run'):
                execute_method = agent.run
            else:
                raise Exception(f"Agent {agent} has no execute or run method")

            # 執行帶超時
            if asyncio.iscoroutinefunction(execute_method):
                result = await asyncio.wait_for(
                    execute_method(),
                    timeout=timeout_seconds
                )
            else:
                # 同步方法包裝為異步
                result = await asyncio.get_event_loop().run_in_executor(
                    None, execute_method
                )

            return result

        except asyncio.TimeoutError:
            raise Exception(f"Agent execution timed out after {timeout_seconds} seconds")
        except Exception as e:
            raise Exception(f"Agent execution failed: {str(e)}")

    def _check_circuit_breaker(self, agent_name: str) -> bool:
        """檢查斷路器狀態"""
        if not self.failover_config['enable_circuit_breaker']:
            return True

        breaker = self.circuit_breakers.get(agent_name, {})
        state = breaker.get('state', 'closed')

        if state == 'closed':
            return True
        elif state == 'open':
            # 檢查是否可以進入半開狀態
            last_failure = breaker.get('last_failure_time')
            if last_failure:
                timeout = self.failover_config['circuit_breaker_timeout']
                if (datetime.now() - last_failure).total_seconds() > timeout:
                    breaker['state'] = 'half_open'
                    logger.info("circuit_breaker_half_open", agent=agent_name)
                    return True
            return False
        elif state == 'half_open':
            return True

        return False

    def _trigger_circuit_breaker(self, agent_name: str) -> None:
        """觸發斷路器"""
        if not self.failover_config['enable_circuit_breaker']:
            return

        breaker = self.circuit_breakers[agent_name]
        breaker['failure_count'] += 1
        breaker['last_failure_time'] = datetime.now()

        threshold = self.failover_config['circuit_breaker_threshold']
        if breaker['failure_count'] >= threshold:
            breaker['state'] = 'open'
            logger.warning("circuit_breaker_opened",
                         agent=agent_name,
                         failure_count=breaker['failure_count'])

    def _reset_circuit_breaker(self, agent_name: str) -> None:
        """重置斷路器"""
        if not self.failover_config['enable_circuit_breaker']:
            return

        breaker = self.circuit_breakers[agent_name]
        breaker['failure_count'] = 0
        breaker['state'] = 'closed'
        breaker['last_failure_time'] = None

    # ========== Utility Methods ==========

    def get_agent_status(self, agent_name: str) -> Optional[AgentStatus]:
        """獲取 Agent 狀態"""
        return self.agent_states.get(agent_name)

    def get_workflow_status(self) -> Optional[WorkflowState]:
        """獲取當前工作流狀態"""
        return self.current_workflow

    def get_execution_history(self, limit: int = 10) -> List[WorkflowState]:
        """獲取執行歷史"""
        return self.execution_history[-limit:]

    def enable_agent(self, agent_name: str) -> bool:
        """啟用 Agent"""
        if agent_name in self.agents:
            self.agents[agent_name].enabled = True
            self.agent_states[agent_name] = AgentStatus.IDLE
            logger.info("agent_enabled", agent=agent_name)
            return True
        return False

    def disable_agent(self, agent_name: str) -> bool:
        """禁用 Agent"""
        if agent_name in self.agents:
            self.agents[agent_name].enabled = False
            self.agent_states[agent_name] = AgentStatus.DISABLED
            logger.info("agent_disabled", agent=agent_name)
            return True
        return False

    def get_agent_metrics(self) -> Dict[str, Any]:
        """獲取 Agent 指標"""
        try:
            total_agents = len(self.agents)
            enabled_agents = len([a for a in self.agents.values() if a.enabled])

            status_counts = {}
            for status in AgentStatus:
                status_counts[status.value] = len([
                    s for s in self.agent_states.values() if s == status
                ])

            circuit_breaker_stats = {
                'open_breakers': len([
                    b for b in self.circuit_breakers.values()
                    if b.get('state') == 'open'
                ]),
                'half_open_breakers': len([
                    b for b in self.circuit_breakers.values()
                    if b.get('state') == 'half_open'
                ])
            }

            workflow_stats = {
                'total_workflows': len(self.execution_history),
                'successful_workflows': len([
                    w for w in self.execution_history
                    if w.status == 'completed'
                ]),
                'failed_workflows': len([
                    w for w in self.execution_history
                    if w.status in ['failed', 'error']
                ])
            }

            return {
                'total_agents': total_agents,
                'enabled_agents': enabled_agents,
                'agent_status_counts': status_counts,
                'circuit_breaker_stats': circuit_breaker_stats,
                'workflow_stats': workflow_stats,
                'current_workflow_status': self.current_workflow.status if self.current_workflow else None
            }

        except Exception as e:
            logger.error("get_agent_metrics_failed", error=str(e))
            return {}

    async def cleanup(self) -> None:
        """清理資源"""
        try:
            # 清理所有 Agent
            for agent_name, agent_config in self.agents.items():
                try:
                    if hasattr(agent_config.instance, 'cleanup'):
                        await agent_config.instance.cleanup()
                    logger.info("agent_cleaned_up", agent=agent_name)
                except Exception as e:
                    logger.error("agent_cleanup_failed",
                               agent=agent_name,
                               error=str(e))

            # 清理狀態
            self.agent_states.clear()
            self.circuit_breakers.clear()
            self.current_workflow = None

            logger.info("agno_scheduler_cleanup_completed")

        except Exception as e:
            logger.error("agno_scheduler_cleanup_failed", error=str(e))
