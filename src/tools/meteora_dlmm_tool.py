"""
Meteora DLMM Tool - DyFlow v3 + Agno Framework
基於 Agno Framework 的 Meteora DLMM v2 操作工具
支持單邊 LP、自動收割、範圍調整等功能
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import base64

# Agno Framework imports
try:
    from agno.tools import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        pass

# Solana imports
try:
    from solana.rpc.async_api import AsyncClient
    from solana.rpc.commitment import Commitment
    from solana.rpc.types import TxOpts
    from solana.transaction import Transaction
    from solana.keypair import Keypair
    from solana.publickey import PublicKey
    from solders.pubkey import Pubkey
    from solders.instruction import Instruction
    import aiohttp
    SOLANA_AVAILABLE = True
except ImportError:
    SOLANA_AVAILABLE = False
    AsyncClient = None

from ..utils.exceptions import DyFlowException
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)

# ========== Data Models ==========

@dataclass
class DLMMPoolInfo:
    """DLMM 池子信息"""
    address: str
    name: str
    mint_x: str
    mint_y: str
    reserve_x: float
    reserve_y: float
    bin_step: int
    base_fee_percentage: float
    max_fee_percentage: float
    protocol_fee_percentage: float
    liquidity: float
    reward_infos: List[Dict[str, Any]]
    fees_24h: float
    fees_7d: float
    today_fees: float
    trade_volume_24h: float
    cumulative_trade_volume: str
    cumulative_fee_volume: str
    current_price: float
    apr: float
    apy: float
    farm_apr: float
    farm_apy: float
    hide: bool

@dataclass
class DLMMPosition:
    """DLMM 持仓信息"""
    lb_pair: str
    owner: str
    liquidity_shares: List[Dict[str, Any]]
    total_x_amount: float
    total_y_amount: float
    total_usd_value: float
    unclaimed_fees: Dict[str, float]
    unclaimed_rewards: List[Dict[str, Any]]

@dataclass
class DLMMSubscribeParams:
    """DLMM 订阅参数"""
    pool_address: str
    token_amount: float
    token_mint: str
    strategy_type: str  # "spot_oneside", "curve_oneside", "bid_ask"
    min_bin_id: Optional[int] = None
    max_bin_id: Optional[int] = None
    should_auto_fill: bool = True

# ========== Meteora DLMM Tool ==========

class MeteoraDLMMTool(Tool if AGNO_AVAILABLE else object):
    """Meteora DLMM v2 操作工具"""
    
    name = "meteora_dlmm"
    description = "Meteora DLMM v2 流動性池操作工具，支持單邊LP、收割、範圍調整"
    
    def __init__(self, config: Dict[str, Any]):
        if AGNO_AVAILABLE:
            super().__init__()
        
        self.config = config
        self.rpc_url = config.get('rpc_url', 'https://api.mainnet-beta.solana.com')
        self.api_base = config.get('api_base', 'https://dlmm-api.meteora.ag')
        self.program_id = config.get('program_id', 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')
        
        # RPC 客戶端
        self.rpc_client: Optional[AsyncClient] = None
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 錢包配置 (生產環境應使用安全存儲)
        self.wallet_keypair: Optional[Keypair] = None
        
        # 緩存
        self._pool_cache: Dict[str, DLMMPoolInfo] = {}
        self._cache_ttl = timedelta(minutes=5)
        self._last_cache_update: Dict[str, datetime] = {}
        
        logger.info("meteora_dlmm_tool_initialized", 
                   rpc_url=self.rpc_url,
                   api_base=self.api_base,
                   program_id=self.program_id)
    
    async def initialize(self) -> None:
        """初始化工具"""
        try:
            if not SOLANA_AVAILABLE:
                raise DyFlowException("Solana SDK not available")
            
            # 初始化 RPC 客戶端
            self.rpc_client = AsyncClient(
                self.rpc_url,
                commitment=Commitment("confirmed"),
                timeout=30.0
            )
            
            # 初始化 HTTP 會話
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'DyFlow-DLMM-Tool/3.0'
                }
            )
            
            # 測試連接
            health = await self.rpc_client.get_health()
            logger.info("meteora_dlmm_tool_connected", health=health)
            
        except Exception as e:
            logger.error("meteora_dlmm_tool_initialization_failed", error=str(e))
            raise DyFlowException(f"Meteora DLMM tool initialization failed: {e}")
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            if self.rpc_client:
                await self.rpc_client.close()
            if self.session:
                await self.session.close()
            logger.info("meteora_dlmm_tool_cleanup_completed")
        except Exception as e:
            logger.error("meteora_dlmm_tool_cleanup_failed", error=str(e))

    # ========== Pool Discovery & Info ==========
    
    async def get_all_pools(self, limit: int = 100) -> List[DLMMPoolInfo]:
        """獲取所有 DLMM 池子"""
        try:
            url = f"{self.api_base}/pair/all"
            params = {"limit": limit}
            
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    raise DyFlowException(f"API request failed: {response.status}")
                
                data = await response.json()
                pools = []
                
                for pool_data in data.get("data", []):
                    pool_info = self._parse_pool_data(pool_data)
                    if pool_info:
                        pools.append(pool_info)
                        # 更新緩存
                        self._pool_cache[pool_info.address] = pool_info
                        self._last_cache_update[pool_info.address] = datetime.now()
                
                logger.info("dlmm_pools_retrieved", count=len(pools))
                return pools
                
        except Exception as e:
            logger.error("get_all_pools_failed", error=str(e))
            raise DyFlowException(f"Failed to get DLMM pools: {e}")
    
    async def get_pool_info(self, pool_address: str, use_cache: bool = True) -> Optional[DLMMPoolInfo]:
        """獲取特定池子信息"""
        try:
            # 檢查緩存
            if use_cache and pool_address in self._pool_cache:
                last_update = self._last_cache_update.get(pool_address)
                if last_update and datetime.now() - last_update < self._cache_ttl:
                    return self._pool_cache[pool_address]
            
            url = f"{self.api_base}/pair/{pool_address}"
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.warning("pool_info_not_found", pool=pool_address, status=response.status)
                    return None
                
                data = await response.json()
                pool_info = self._parse_pool_data(data)
                
                if pool_info:
                    # 更新緩存
                    self._pool_cache[pool_address] = pool_info
                    self._last_cache_update[pool_address] = datetime.now()
                
                return pool_info
                
        except Exception as e:
            logger.error("get_pool_info_failed", pool=pool_address, error=str(e))
            return None
    
    def _parse_pool_data(self, data: Dict[str, Any]) -> Optional[DLMMPoolInfo]:
        """解析池子數據"""
        try:
            return DLMMPoolInfo(
                address=data.get("address", ""),
                name=data.get("name", ""),
                mint_x=data.get("mint_x", ""),
                mint_y=data.get("mint_y", ""),
                reserve_x=float(data.get("reserve_x", 0)),
                reserve_y=float(data.get("reserve_y", 0)),
                bin_step=int(data.get("bin_step", 0)),
                base_fee_percentage=float(data.get("base_fee_percentage", 0)),
                max_fee_percentage=float(data.get("max_fee_percentage", 0)),
                protocol_fee_percentage=float(data.get("protocol_fee_percentage", 0)),
                liquidity=float(data.get("liquidity", 0)),
                reward_infos=data.get("reward_infos", []),
                fees_24h=float(data.get("fees_24h", 0)),
                fees_7d=float(data.get("fees_7d", 0)),
                today_fees=float(data.get("today_fees", 0)),
                trade_volume_24h=float(data.get("trade_volume_24h", 0)),
                cumulative_trade_volume=data.get("cumulative_trade_volume", "0"),
                cumulative_fee_volume=data.get("cumulative_fee_volume", "0"),
                current_price=float(data.get("current_price", 0)),
                apr=float(data.get("apr", 0)),
                apy=float(data.get("apy", 0)),
                farm_apr=float(data.get("farm_apr", 0)),
                farm_apy=float(data.get("farm_apy", 0)),
                hide=bool(data.get("hide", False))
            )
        except Exception as e:
            logger.error("parse_pool_data_failed", error=str(e))
            return None

    # ========== Position Management ==========

    async def get_user_positions(self, wallet_address: str) -> List[DLMMPosition]:
        """獲取用戶持倉"""
        try:
            url = f"{self.api_base}/user/{wallet_address}"

            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.warning("user_positions_not_found", wallet=wallet_address)
                    return []

                data = await response.json()
                positions = []

                for pos_data in data.get("user_positions", []):
                    position = self._parse_position_data(pos_data)
                    if position:
                        positions.append(position)

                logger.info("user_positions_retrieved", wallet=wallet_address, count=len(positions))
                return positions

        except Exception as e:
            logger.error("get_user_positions_failed", wallet=wallet_address, error=str(e))
            return []

    def _parse_position_data(self, data: Dict[str, Any]) -> Optional[DLMMPosition]:
        """解析持倉數據"""
        try:
            return DLMMPosition(
                lb_pair=data.get("lb_pair", ""),
                owner=data.get("owner", ""),
                liquidity_shares=data.get("liquidity_shares", []),
                total_x_amount=float(data.get("total_x_amount", 0)),
                total_y_amount=float(data.get("total_y_amount", 0)),
                total_usd_value=float(data.get("total_usd_value", 0)),
                unclaimed_fees=data.get("unclaimed_fees", {}),
                unclaimed_rewards=data.get("unclaimed_rewards", [])
            )
        except Exception as e:
            logger.error("parse_position_data_failed", error=str(e))
            return None

    # ========== Single-Sided LP Operations ==========

    async def subscribe_single_sided(self, params: DLMMSubscribeParams) -> Dict[str, Any]:
        """單邊 LP 訂閱 (核心功能)"""
        try:
            if not self.wallet_keypair:
                raise DyFlowException("Wallet not configured")

            logger.info("dlmm_subscribe_started",
                       pool=params.pool_address,
                       amount=params.token_amount,
                       token=params.token_mint,
                       strategy=params.strategy_type)

            # 1. 獲取池子信息
            pool_info = await self.get_pool_info(params.pool_address)
            if not pool_info:
                raise DyFlowException(f"Pool not found: {params.pool_address}")

            # 2. 計算 bin 範圍
            bin_range = await self._calculate_optimal_bin_range(
                pool_info, params.strategy_type, params.min_bin_id, params.max_bin_id
            )

            # 3. 構建訂閱交易
            transaction = await self._build_subscribe_transaction(
                params, pool_info, bin_range
            )

            # 4. 發送交易
            result = await self._send_transaction(transaction)

            logger.info("dlmm_subscribe_completed",
                       pool=params.pool_address,
                       tx_signature=result.get("signature"),
                       bin_range=bin_range)

            return {
                "success": True,
                "signature": result.get("signature"),
                "pool_address": params.pool_address,
                "bin_range": bin_range,
                "amount": params.token_amount,
                "timestamp": get_utc_timestamp().isoformat()
            }

        except Exception as e:
            logger.error("dlmm_subscribe_failed",
                        pool=params.pool_address,
                        error=str(e))
            return {
                "success": False,
                "error": str(e),
                "pool_address": params.pool_address,
                "timestamp": get_utc_timestamp().isoformat()
            }

    async def _calculate_optimal_bin_range(self,
                                         pool_info: DLMMPoolInfo,
                                         strategy_type: str,
                                         min_bin_id: Optional[int] = None,
                                         max_bin_id: Optional[int] = None) -> Dict[str, int]:
        """計算最優 bin 範圍"""
        try:
            # 獲取當前活躍 bin
            current_bin = await self._get_current_active_bin(pool_info.address)

            if strategy_type == "spot_oneside":
                # 單邊現貨策略：在當前價格附近
                range_size = 10  # ±10 bins
                min_bin = min_bin_id if min_bin_id is not None else current_bin - range_size
                max_bin = max_bin_id if max_bin_id is not None else current_bin + range_size

            elif strategy_type == "curve_oneside":
                # 曲線單邊策略：更寬範圍
                range_size = 25  # ±25 bins
                min_bin = min_bin_id if min_bin_id is not None else current_bin - range_size
                max_bin = max_bin_id if max_bin_id is not None else current_bin + range_size

            elif strategy_type == "bid_ask":
                # 買賣價差策略：分別在兩側
                spread = 5
                min_bin = min_bin_id if min_bin_id is not None else current_bin - spread
                max_bin = max_bin_id if max_bin_id is not None else current_bin + spread

            else:
                # 默認策略
                min_bin = min_bin_id if min_bin_id is not None else current_bin - 15
                max_bin = max_bin_id if max_bin_id is not None else current_bin + 15

            return {
                "min_bin_id": min_bin,
                "max_bin_id": max_bin,
                "current_bin": current_bin
            }

        except Exception as e:
            logger.error("calculate_bin_range_failed", error=str(e))
            # 返回保守範圍
            return {
                "min_bin_id": min_bin_id or -50,
                "max_bin_id": max_bin_id or 50,
                "current_bin": 0
            }

    async def _get_current_active_bin(self, pool_address: str) -> int:
        """獲取當前活躍 bin ID"""
        try:
            # 通過 RPC 獲取池子賬戶數據
            pool_pubkey = PublicKey(pool_address)
            account_info = await self.rpc_client.get_account_info(pool_pubkey)

            if not account_info.value:
                raise DyFlowException("Pool account not found")

            # 解析賬戶數據獲取活躍 bin ID
            # 這裡需要根據 Meteora DLMM 的具體數據結構來解析
            # 簡化實現，實際需要更複雜的解析邏輯
            data = account_info.value.data

            # 假設活躍 bin ID 在特定偏移位置 (需要根據實際協議調整)
            if len(data) >= 100:
                # 這是簡化的解析，實際需要按照協議規範
                active_bin = int.from_bytes(data[80:84], byteorder='little', signed=True)
                return active_bin

            return 0  # 默認值

        except Exception as e:
            logger.error("get_current_active_bin_failed", pool=pool_address, error=str(e))
            return 0

    async def _build_subscribe_transaction(self,
                                         params: DLMMSubscribeParams,
                                         pool_info: DLMMPoolInfo,
                                         bin_range: Dict[str, int]) -> Transaction:
        """構建訂閱交易"""
        try:
            # 這裡需要根據 Meteora DLMM 協議構建具體的交易指令
            # 簡化實現，實際需要更複雜的指令構建

            transaction = Transaction()

            # 添加必要的指令
            # 1. 創建或獲取用戶位置賬戶
            # 2. 添加流動性指令
            # 3. 設置 bin 範圍

            logger.info("subscribe_transaction_built",
                       pool=params.pool_address,
                       bin_range=bin_range)

            return transaction

        except Exception as e:
            logger.error("build_subscribe_transaction_failed", error=str(e))
            raise DyFlowException(f"Failed to build subscribe transaction: {e}")

    async def _send_transaction(self, transaction: Transaction) -> Dict[str, Any]:
        """發送交易"""
        try:
            if not self.wallet_keypair:
                raise DyFlowException("Wallet not configured")

            # 簽名交易
            transaction.sign(self.wallet_keypair)

            # 發送交易
            opts = TxOpts(skip_preflight=False, preflight_commitment="confirmed")
            result = await self.rpc_client.send_transaction(transaction, opts)

            # 等待確認
            await self.rpc_client.confirm_transaction(result.value)

            return {
                "signature": str(result.value),
                "success": True
            }

        except Exception as e:
            logger.error("send_transaction_failed", error=str(e))
            raise DyFlowException(f"Failed to send transaction: {e}")

    # ========== Harvest & Exit Operations ==========

    async def harvest_fees(self, pool_address: str, wallet_address: str) -> Dict[str, Any]:
        """收割手續費"""
        try:
            logger.info("dlmm_harvest_started", pool=pool_address, wallet=wallet_address)

            # 1. 獲取用戶持倉
            positions = await self.get_user_positions(wallet_address)
            target_position = None

            for pos in positions:
                if pos.lb_pair == pool_address:
                    target_position = pos
                    break

            if not target_position:
                return {
                    "success": False,
                    "error": "No position found in this pool",
                    "pool_address": pool_address
                }

            # 2. 檢查是否有可收割的費用
            total_fees_usd = sum(target_position.unclaimed_fees.values())
            if total_fees_usd < 0.01:  # 最小收割閾值
                return {
                    "success": False,
                    "error": "Insufficient fees to harvest",
                    "unclaimed_fees": target_position.unclaimed_fees
                }

            # 3. 構建收割交易
            harvest_tx = await self._build_harvest_transaction(target_position)

            # 4. 發送交易
            result = await self._send_transaction(harvest_tx)

            logger.info("dlmm_harvest_completed",
                       pool=pool_address,
                       fees_harvested=target_position.unclaimed_fees,
                       tx_signature=result.get("signature"))

            return {
                "success": True,
                "signature": result.get("signature"),
                "pool_address": pool_address,
                "fees_harvested": target_position.unclaimed_fees,
                "timestamp": get_utc_timestamp().isoformat()
            }

        except Exception as e:
            logger.error("dlmm_harvest_failed", pool=pool_address, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "pool_address": pool_address,
                "timestamp": get_utc_timestamp().isoformat()
            }

    async def exit_position(self, pool_address: str, wallet_address: str,
                          percentage: float = 1.0) -> Dict[str, Any]:
        """退出持倉"""
        try:
            logger.info("dlmm_exit_started",
                       pool=pool_address,
                       wallet=wallet_address,
                       percentage=percentage)

            # 1. 獲取用戶持倉
            positions = await self.get_user_positions(wallet_address)
            target_position = None

            for pos in positions:
                if pos.lb_pair == pool_address:
                    target_position = pos
                    break

            if not target_position:
                return {
                    "success": False,
                    "error": "No position found in this pool",
                    "pool_address": pool_address
                }

            # 2. 計算退出數量
            exit_shares = []
            for share in target_position.liquidity_shares:
                exit_amount = int(float(share.get("amount", 0)) * percentage)
                if exit_amount > 0:
                    exit_shares.append({
                        "bin_id": share.get("bin_id"),
                        "amount": exit_amount
                    })

            if not exit_shares:
                return {
                    "success": False,
                    "error": "No liquidity to exit",
                    "pool_address": pool_address
                }

            # 3. 構建退出交易
            exit_tx = await self._build_exit_transaction(target_position, exit_shares)

            # 4. 發送交易
            result = await self._send_transaction(exit_tx)

            logger.info("dlmm_exit_completed",
                       pool=pool_address,
                       exit_percentage=percentage,
                       tx_signature=result.get("signature"))

            return {
                "success": True,
                "signature": result.get("signature"),
                "pool_address": pool_address,
                "exit_percentage": percentage,
                "exit_shares": exit_shares,
                "timestamp": get_utc_timestamp().isoformat()
            }

        except Exception as e:
            logger.error("dlmm_exit_failed", pool=pool_address, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "pool_address": pool_address,
                "timestamp": get_utc_timestamp().isoformat()
            }

    async def _build_harvest_transaction(self, position: DLMMPosition) -> Transaction:
        """構建收割交易"""
        try:
            transaction = Transaction()

            # 添加收割指令
            # 實際需要根據 Meteora DLMM 協議構建具體指令

            logger.info("harvest_transaction_built", position=position.lb_pair)
            return transaction

        except Exception as e:
            logger.error("build_harvest_transaction_failed", error=str(e))
            raise DyFlowException(f"Failed to build harvest transaction: {e}")

    async def _build_exit_transaction(self, position: DLMMPosition,
                                    exit_shares: List[Dict[str, Any]]) -> Transaction:
        """構建退出交易"""
        try:
            transaction = Transaction()

            # 添加退出流動性指令
            # 實際需要根據 Meteora DLMM 協議構建具體指令

            logger.info("exit_transaction_built",
                       position=position.lb_pair,
                       exit_shares_count=len(exit_shares))
            return transaction

        except Exception as e:
            logger.error("build_exit_transaction_failed", error=str(e))
            raise DyFlowException(f"Failed to build exit transaction: {e}")

    # ========== Utility Functions ==========

    async def get_pool_bins(self, pool_address: str, limit: int = 50) -> List[Dict[str, Any]]:
        """獲取池子 bin 信息"""
        try:
            url = f"{self.api_base}/pair/{pool_address}/bins"
            params = {"limit": limit}

            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    return []

                data = await response.json()
                return data.get("bins", [])

        except Exception as e:
            logger.error("get_pool_bins_failed", pool=pool_address, error=str(e))
            return []

    async def estimate_swap_output(self, pool_address: str,
                                 input_mint: str, input_amount: float) -> Dict[str, Any]:
        """估算交換輸出"""
        try:
            url = f"{self.api_base}/pair/{pool_address}/quote"
            params = {
                "input_mint": input_mint,
                "input_amount": str(int(input_amount))
            }

            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    return {"error": f"Quote request failed: {response.status}"}

                data = await response.json()
                return {
                    "output_amount": float(data.get("output_amount", 0)),
                    "price_impact": float(data.get("price_impact", 0)),
                    "fee": float(data.get("fee", 0))
                }

        except Exception as e:
            logger.error("estimate_swap_output_failed", error=str(e))
            return {"error": str(e)}

    def set_wallet(self, private_key: str) -> None:
        """設置錢包 (僅用於測試，生產環境需要安全存儲)"""
        try:
            self.wallet_keypair = Keypair.from_secret_key(base64.b64decode(private_key))
            logger.info("wallet_configured",
                       public_key=str(self.wallet_keypair.public_key))
        except Exception as e:
            logger.error("wallet_configuration_failed", error=str(e))
            raise DyFlowException(f"Failed to configure wallet: {e}")

    async def get_token_balance(self, wallet_address: str, token_mint: str) -> float:
        """獲取代幣餘額"""
        try:
            wallet_pubkey = PublicKey(wallet_address)
            token_accounts = await self.rpc_client.get_token_accounts_by_owner(
                wallet_pubkey,
                {"mint": PublicKey(token_mint)}
            )

            if not token_accounts.value:
                return 0.0

            # 獲取第一個代幣賬戶的餘額
            account_info = await self.rpc_client.get_token_account_balance(
                token_accounts.value[0].pubkey
            )

            return float(account_info.value.ui_amount or 0)

        except Exception as e:
            logger.error("get_token_balance_failed",
                        wallet=wallet_address,
                        token=token_mint,
                        error=str(e))
            return 0.0
