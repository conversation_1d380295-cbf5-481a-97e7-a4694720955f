"""
Jupiter Swap Tool - DyFlow v3 + Agno Framework
基於 Agno Framework 的 Jupiter 聚合器交換工具
支持 Solana 鏈上最優路由交換和 DCA 出貨
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import structlog
from dataclasses import dataclass
import base64

# Agno Framework imports
try:
    from agno.tools import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        pass

# Solana imports
try:
    from solana.rpc.async_api import AsyncClient
    from solana.rpc.commitment import Commitment
    from solana.rpc.types import TxOpts
    from solana.transaction import Transaction
    from solana.keypair import Keypair
    from solana.publickey import PublicKey
    import aiohttp
    SOLANA_AVAILABLE = True
except ImportError:
    SOLANA_AVAILABLE = False
    AsyncClient = None

from ..utils.exceptions import DyFlowException
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)

# ========== Data Models ==========

@dataclass
class SwapQuote:
    """交換報價"""
    input_mint: str
    output_mint: str
    input_amount: int
    output_amount: int
    price_impact_pct: float
    market_infos: List[Dict[str, Any]]
    route_plan: List[Dict[str, Any]]
    context_slot: int
    time_taken: float

@dataclass
class SwapResult:
    """交換結果"""
    success: bool
    signature: Optional[str] = None
    input_amount: float = 0.0
    output_amount: float = 0.0
    price_impact: float = 0.0
    fee_amount: float = 0.0
    error: Optional[str] = None
    timestamp: Optional[str] = None

@dataclass
class DCAParams:
    """DCA 參數"""
    input_mint: str
    output_mint: str
    total_amount: float
    num_orders: int
    interval_minutes: int
    slippage_bps: int = 50  # 0.5%
    max_price_impact_bps: int = 100  # 1%

# ========== Jupiter Swap Tool ==========

class JupiterSwapTool(Tool if AGNO_AVAILABLE else object):
    """Jupiter 聚合器交換工具"""
    
    name = "jupiter_swap"
    description = "Jupiter 聚合器交換工具，支持最優路由交換和 DCA 出貨"
    
    def __init__(self, config: Dict[str, Any]):
        if AGNO_AVAILABLE:
            super().__init__()
        
        self.config = config
        self.rpc_url = config.get('rpc_url', 'https://api.mainnet-beta.solana.com')
        self.jupiter_api = config.get('jupiter_api', 'https://quote-api.jup.ag/v6')
        
        # RPC 客戶端
        self.rpc_client: Optional[AsyncClient] = None
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 錢包配置
        self.wallet_keypair: Optional[Keypair] = None
        
        # 交換配置
        self.default_slippage_bps = config.get('default_slippage_bps', 50)  # 0.5%
        self.max_accounts = config.get('max_accounts', 64)
        
        logger.info("jupiter_swap_tool_initialized", 
                   rpc_url=self.rpc_url,
                   jupiter_api=self.jupiter_api)
    
    async def initialize(self) -> None:
        """初始化工具"""
        try:
            if not SOLANA_AVAILABLE:
                raise DyFlowException("Solana SDK not available")
            
            # 初始化 RPC 客戶端
            self.rpc_client = AsyncClient(
                self.rpc_url,
                commitment=Commitment("confirmed"),
                timeout=30.0
            )
            
            # 初始化 HTTP 會話
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'DyFlow-Jupiter-Tool/3.0'
                }
            )
            
            # 測試連接
            health = await self.rpc_client.get_health()
            logger.info("jupiter_swap_tool_connected", health=health)
            
        except Exception as e:
            logger.error("jupiter_swap_tool_initialization_failed", error=str(e))
            raise DyFlowException(f"Jupiter swap tool initialization failed: {e}")
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            if self.rpc_client:
                await self.rpc_client.close()
            if self.session:
                await self.session.close()
            logger.info("jupiter_swap_tool_cleanup_completed")
        except Exception as e:
            logger.error("jupiter_swap_tool_cleanup_failed", error=str(e))

    # ========== Quote & Swap Operations ==========
    
    async def get_quote(self, input_mint: str, output_mint: str, 
                       amount: int, slippage_bps: Optional[int] = None) -> Optional[SwapQuote]:
        """獲取交換報價"""
        try:
            slippage = slippage_bps or self.default_slippage_bps
            
            params = {
                'inputMint': input_mint,
                'outputMint': output_mint,
                'amount': str(amount),
                'slippageBps': str(slippage),
                'maxAccounts': str(self.max_accounts)
            }
            
            url = f"{self.jupiter_api}/quote"
            
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error("jupiter_quote_failed", 
                               status=response.status, 
                               error=error_text)
                    return None
                
                data = await response.json()
                
                quote = SwapQuote(
                    input_mint=data['inputMint'],
                    output_mint=data['outputMint'],
                    input_amount=int(data['inAmount']),
                    output_amount=int(data['outAmount']),
                    price_impact_pct=float(data.get('priceImpactPct', 0)),
                    market_infos=data.get('marketInfos', []),
                    route_plan=data.get('routePlan', []),
                    context_slot=int(data.get('contextSlot', 0)),
                    time_taken=float(data.get('timeTaken', 0))
                )
                
                logger.info("jupiter_quote_received", 
                           input_mint=input_mint,
                           output_mint=output_mint,
                           input_amount=amount,
                           output_amount=quote.output_amount,
                           price_impact=quote.price_impact_pct)
                
                return quote
                
        except Exception as e:
            logger.error("get_jupiter_quote_failed", 
                        input_mint=input_mint,
                        output_mint=output_mint,
                        amount=amount,
                        error=str(e))
            return None
    
    async def execute_swap(self, quote: SwapQuote, 
                          user_public_key: str,
                          priority_fee_lamports: int = 0) -> SwapResult:
        """執行交換"""
        try:
            if not self.wallet_keypair:
                raise DyFlowException("Wallet not configured")
            
            logger.info("jupiter_swap_started", 
                       input_mint=quote.input_mint,
                       output_mint=quote.output_mint,
                       input_amount=quote.input_amount)
            
            # 1. 獲取交換交易
            swap_transaction = await self._get_swap_transaction(
                quote, user_public_key, priority_fee_lamports
            )
            
            if not swap_transaction:
                return SwapResult(
                    success=False,
                    error="Failed to get swap transaction",
                    timestamp=get_utc_timestamp().isoformat()
                )
            
            # 2. 發送交易
            result = await self._send_transaction(swap_transaction)
            
            if result.get("success"):
                logger.info("jupiter_swap_completed", 
                           signature=result.get("signature"),
                           input_amount=quote.input_amount,
                           output_amount=quote.output_amount)
                
                return SwapResult(
                    success=True,
                    signature=result.get("signature"),
                    input_amount=float(quote.input_amount),
                    output_amount=float(quote.output_amount),
                    price_impact=quote.price_impact_pct,
                    timestamp=get_utc_timestamp().isoformat()
                )
            else:
                return SwapResult(
                    success=False,
                    error=result.get("error", "Unknown error"),
                    timestamp=get_utc_timestamp().isoformat()
                )
            
        except Exception as e:
            logger.error("jupiter_swap_failed", error=str(e))
            return SwapResult(
                success=False,
                error=str(e),
                timestamp=get_utc_timestamp().isoformat()
            )
    
    async def _get_swap_transaction(self, quote: SwapQuote, 
                                  user_public_key: str,
                                  priority_fee_lamports: int = 0) -> Optional[Transaction]:
        """獲取交換交易"""
        try:
            payload = {
                'quoteResponse': {
                    'inputMint': quote.input_mint,
                    'outputMint': quote.output_mint,
                    'inAmount': str(quote.input_amount),
                    'outAmount': str(quote.output_amount),
                    'priceImpactPct': quote.price_impact_pct,
                    'marketInfos': quote.market_infos,
                    'routePlan': quote.route_plan,
                    'contextSlot': quote.context_slot,
                    'timeTaken': quote.time_taken
                },
                'userPublicKey': user_public_key,
                'wrapAndUnwrapSol': True,
                'prioritizationFeeLamports': priority_fee_lamports
            }
            
            url = f"{self.jupiter_api}/swap"
            
            async with self.session.post(url, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error("get_swap_transaction_failed", 
                               status=response.status,
                               error=error_text)
                    return None
                
                data = await response.json()
                
                # 反序列化交易
                swap_transaction_buf = base64.b64decode(data['swapTransaction'])
                transaction = Transaction.deserialize(swap_transaction_buf)
                
                return transaction
                
        except Exception as e:
            logger.error("get_swap_transaction_error", error=str(e))
            return None
    
    async def _send_transaction(self, transaction: Transaction) -> Dict[str, Any]:
        """發送交易"""
        try:
            if not self.wallet_keypair:
                raise DyFlowException("Wallet not configured")
            
            # 簽名交易
            transaction.sign(self.wallet_keypair)
            
            # 發送交易
            opts = TxOpts(skip_preflight=False, preflight_commitment="confirmed")
            result = await self.rpc_client.send_transaction(transaction, opts)
            
            # 等待確認
            await self.rpc_client.confirm_transaction(result.value)
            
            return {
                "signature": str(result.value),
                "success": True
            }
            
        except Exception as e:
            logger.error("send_transaction_failed", error=str(e))
            return {
                "success": False,
                "error": str(e)
            }

    # ========== DCA Operations ==========
    
    async def execute_dca_order(self, params: DCAParams, order_index: int) -> SwapResult:
        """執行單次 DCA 訂單"""
        try:
            # 計算單次訂單金額
            order_amount = params.total_amount / params.num_orders
            
            logger.info("dca_order_started", 
                       order_index=order_index,
                       order_amount=order_amount,
                       input_mint=params.input_mint,
                       output_mint=params.output_mint)
            
            # 1. 獲取報價
            quote = await self.get_quote(
                params.input_mint,
                params.output_mint,
                int(order_amount),
                params.slippage_bps
            )
            
            if not quote:
                return SwapResult(
                    success=False,
                    error="Failed to get quote for DCA order",
                    timestamp=get_utc_timestamp().isoformat()
                )
            
            # 2. 檢查價格影響
            if quote.price_impact_pct > params.max_price_impact_bps / 10000:
                logger.warning("dca_order_high_price_impact", 
                             price_impact=quote.price_impact_pct,
                             max_allowed=params.max_price_impact_bps / 10000)
                
                return SwapResult(
                    success=False,
                    error=f"Price impact too high: {quote.price_impact_pct:.4f}%",
                    timestamp=get_utc_timestamp().isoformat()
                )
            
            # 3. 執行交換
            user_public_key = str(self.wallet_keypair.public_key) if self.wallet_keypair else ""
            result = await self.execute_swap(quote, user_public_key)
            
            if result.success:
                logger.info("dca_order_completed", 
                           order_index=order_index,
                           signature=result.signature,
                           output_amount=result.output_amount)
            
            return result
            
        except Exception as e:
            logger.error("dca_order_failed", 
                        order_index=order_index,
                        error=str(e))
            return SwapResult(
                success=False,
                error=str(e),
                timestamp=get_utc_timestamp().isoformat()
            )

    # ========== Utility Functions ==========
    
    def set_wallet(self, private_key: str) -> None:
        """設置錢包"""
        try:
            self.wallet_keypair = Keypair.from_secret_key(base64.b64decode(private_key))
            logger.info("wallet_configured", 
                       public_key=str(self.wallet_keypair.public_key))
        except Exception as e:
            logger.error("wallet_configuration_failed", error=str(e))
            raise DyFlowException(f"Failed to configure wallet: {e}")
    
    async def get_supported_tokens(self) -> List[Dict[str, Any]]:
        """獲取支持的代幣列表"""
        try:
            url = f"{self.jupiter_api}/tokens"
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    return []
                
                tokens = await response.json()
                logger.info("supported_tokens_retrieved", count=len(tokens))
                return tokens
                
        except Exception as e:
            logger.error("get_supported_tokens_failed", error=str(e))
            return []
