"""
1inch Swap Tool - DyFlow v3 + Agno Framework
基於 Agno Framework 的 1inch 聚合器交換工具
支持 BSC 鏈上最優路由交換和 DCA 出貨
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import structlog
from dataclasses import dataclass
from decimal import Decimal

# Agno Framework imports
try:
    from agno.tools import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Tool:
        pass

# Web3 imports
try:
    from web3 import Web3
    from web3.middleware import geth_poa_middleware
    import aiohttp
    WEB3_AVAILABLE = True
except ImportError:
    WEB3_AVAILABLE = False
    Web3 = None

from ..utils.exceptions import DyFlowException
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)

# ========== Data Models ==========

@dataclass
class SwapQuote:
    """交換報價"""
    from_token: str
    to_token: str
    from_amount: str
    to_amount: str
    protocols: List[List[Dict[str, Any]]]
    estimated_gas: int
    gas_price: str
    price_impact: float

@dataclass
class SwapResult:
    """交換結果"""
    success: bool
    tx_hash: Optional[str] = None
    from_amount: float = 0.0
    to_amount: float = 0.0
    gas_used: int = 0
    gas_price: float = 0.0
    error: Optional[str] = None
    timestamp: Optional[str] = None

@dataclass
class DCAParams:
    """DCA 參數"""
    from_token: str
    to_token: str
    total_amount: float
    num_orders: int
    interval_minutes: int
    slippage: float = 1.0  # 1%
    max_price_impact: float = 2.0  # 2%

# ========== 1inch Swap Tool ==========

class OneInchSwapTool(Tool if AGNO_AVAILABLE else object):
    """1inch 聚合器交換工具"""
    
    name = "oneinch_swap"
    description = "1inch 聚合器交換工具，支持 BSC 鏈最優路由交換和 DCA 出貨"
    
    def __init__(self, config: Dict[str, Any]):
        if AGNO_AVAILABLE:
            super().__init__()
        
        self.config = config
        self.rpc_url = config.get('rpc_url', 'https://bsc-dataseed1.binance.org/')
        self.oneinch_api = config.get('oneinch_api', 'https://api.1inch.dev')
        self.api_key = config.get('api_key', '')  # 1inch API key
        self.chain_id = config.get('chain_id', 56)  # BSC mainnet
        
        # Web3 客戶端
        self.w3: Optional[Web3] = None
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 錢包配置
        self.wallet_address: Optional[str] = None
        self.private_key: Optional[str] = None
        
        # 交換配置
        self.default_slippage = config.get('default_slippage', 1.0)  # 1%
        self.gas_limit = config.get('gas_limit', 300000)
        
        logger.info("oneinch_swap_tool_initialized", 
                   rpc_url=self.rpc_url,
                   oneinch_api=self.oneinch_api,
                   chain_id=self.chain_id)
    
    async def initialize(self) -> None:
        """初始化工具"""
        try:
            if not WEB3_AVAILABLE:
                raise DyFlowException("Web3 not available")
            
            # 初始化 Web3 客戶端
            self.w3 = Web3(Web3.HTTPProvider(self.rpc_url))
            self.w3.middleware_onion.inject(geth_poa_middleware, layer=0)
            
            # 初始化 HTTP 會話
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'DyFlow-1inch-Tool/3.0'
            }
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers=headers
            )
            
            # 測試連接
            is_connected = self.w3.is_connected()
            logger.info("oneinch_swap_tool_connected", connected=is_connected)
            
            if not is_connected:
                raise DyFlowException("Failed to connect to BSC RPC")
            
        except Exception as e:
            logger.error("oneinch_swap_tool_initialization_failed", error=str(e))
            raise DyFlowException(f"1inch swap tool initialization failed: {e}")
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            if self.session:
                await self.session.close()
            logger.info("oneinch_swap_tool_cleanup_completed")
        except Exception as e:
            logger.error("oneinch_swap_tool_cleanup_failed", error=str(e))

    # ========== Quote & Swap Operations ==========
    
    async def get_quote(self, from_token: str, to_token: str, 
                       amount: str, slippage: Optional[float] = None) -> Optional[SwapQuote]:
        """獲取交換報價"""
        try:
            slippage_pct = slippage or self.default_slippage
            
            params = {
                'src': from_token,
                'dst': to_token,
                'amount': amount,
                'includeProtocols': 'true',
                'includeGas': 'true'
            }
            
            url = f"{self.oneinch_api}/swap/v6.0/{self.chain_id}/quote"
            
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error("oneinch_quote_failed", 
                               status=response.status, 
                               error=error_text)
                    return None
                
                data = await response.json()
                
                quote = SwapQuote(
                    from_token=data['srcToken']['address'],
                    to_token=data['dstToken']['address'],
                    from_amount=data['srcAmount'],
                    to_amount=data['dstAmount'],
                    protocols=data.get('protocols', []),
                    estimated_gas=int(data.get('gas', 0)),
                    gas_price=data.get('gasPrice', '0'),
                    price_impact=float(data.get('priceImpact', 0))
                )
                
                logger.info("oneinch_quote_received", 
                           from_token=from_token,
                           to_token=to_token,
                           from_amount=amount,
                           to_amount=quote.to_amount,
                           price_impact=quote.price_impact)
                
                return quote
                
        except Exception as e:
            logger.error("get_oneinch_quote_failed", 
                        from_token=from_token,
                        to_token=to_token,
                        amount=amount,
                        error=str(e))
            return None
    
    async def execute_swap(self, from_token: str, to_token: str, 
                          amount: str, slippage: Optional[float] = None) -> SwapResult:
        """執行交換"""
        try:
            if not self.wallet_address or not self.private_key:
                raise DyFlowException("Wallet not configured")
            
            logger.info("oneinch_swap_started", 
                       from_token=from_token,
                       to_token=to_token,
                       amount=amount)
            
            slippage_pct = slippage or self.default_slippage
            
            # 1. 獲取交換交易數據
            swap_data = await self._get_swap_transaction(
                from_token, to_token, amount, slippage_pct
            )
            
            if not swap_data:
                return SwapResult(
                    success=False,
                    error="Failed to get swap transaction data",
                    timestamp=get_utc_timestamp().isoformat()
                )
            
            # 2. 構建並發送交易
            tx_hash = await self._send_transaction(swap_data)
            
            if tx_hash:
                # 3. 等待交易確認
                receipt = await self._wait_for_transaction(tx_hash)
                
                if receipt and receipt['status'] == 1:
                    logger.info("oneinch_swap_completed", 
                               tx_hash=tx_hash,
                               gas_used=receipt['gasUsed'])
                    
                    return SwapResult(
                        success=True,
                        tx_hash=tx_hash,
                        from_amount=float(amount),
                        gas_used=receipt['gasUsed'],
                        timestamp=get_utc_timestamp().isoformat()
                    )
                else:
                    return SwapResult(
                        success=False,
                        tx_hash=tx_hash,
                        error="Transaction failed",
                        timestamp=get_utc_timestamp().isoformat()
                    )
            else:
                return SwapResult(
                    success=False,
                    error="Failed to send transaction",
                    timestamp=get_utc_timestamp().isoformat()
                )
            
        except Exception as e:
            logger.error("oneinch_swap_failed", error=str(e))
            return SwapResult(
                success=False,
                error=str(e),
                timestamp=get_utc_timestamp().isoformat()
            )
    
    async def _get_swap_transaction(self, from_token: str, to_token: str, 
                                  amount: str, slippage: float) -> Optional[Dict[str, Any]]:
        """獲取交換交易數據"""
        try:
            params = {
                'src': from_token,
                'dst': to_token,
                'amount': amount,
                'from': self.wallet_address,
                'slippage': str(slippage),
                'disableEstimate': 'true'
            }
            
            url = f"{self.oneinch_api}/swap/v6.0/{self.chain_id}/swap"
            
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error("get_swap_transaction_failed", 
                               status=response.status,
                               error=error_text)
                    return None
                
                data = await response.json()
                return data.get('tx')
                
        except Exception as e:
            logger.error("get_swap_transaction_error", error=str(e))
            return None
    
    async def _send_transaction(self, tx_data: Dict[str, Any]) -> Optional[str]:
        """發送交易"""
        try:
            if not self.w3 or not self.private_key:
                raise DyFlowException("Web3 or private key not configured")
            
            # 獲取 nonce
            nonce = self.w3.eth.get_transaction_count(self.wallet_address)
            
            # 構建交易
            transaction = {
                'to': tx_data['to'],
                'value': int(tx_data.get('value', '0')),
                'gas': int(tx_data.get('gas', self.gas_limit)),
                'gasPrice': int(tx_data.get('gasPrice', self.w3.eth.gas_price)),
                'nonce': nonce,
                'data': tx_data.get('data', '0x')
            }
            
            # 簽名交易
            signed_txn = self.w3.eth.account.sign_transaction(transaction, self.private_key)
            
            # 發送交易
            tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)
            
            return tx_hash.hex()
            
        except Exception as e:
            logger.error("send_transaction_failed", error=str(e))
            return None
    
    async def _wait_for_transaction(self, tx_hash: str, timeout: int = 60) -> Optional[Dict[str, Any]]:
        """等待交易確認"""
        try:
            if not self.w3:
                return None
            
            receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=timeout)
            return dict(receipt)
            
        except Exception as e:
            logger.error("wait_for_transaction_failed", tx_hash=tx_hash, error=str(e))
            return None

    # ========== DCA Operations ==========
    
    async def execute_dca_order(self, params: DCAParams, order_index: int) -> SwapResult:
        """執行單次 DCA 訂單"""
        try:
            # 計算單次訂單金額
            order_amount = params.total_amount / params.num_orders
            amount_wei = self.w3.to_wei(order_amount, 'ether')  # 假設是 18 decimals
            
            logger.info("dca_order_started", 
                       order_index=order_index,
                       order_amount=order_amount,
                       from_token=params.from_token,
                       to_token=params.to_token)
            
            # 1. 獲取報價檢查價格影響
            quote = await self.get_quote(
                params.from_token,
                params.to_token,
                str(amount_wei)
            )
            
            if not quote:
                return SwapResult(
                    success=False,
                    error="Failed to get quote for DCA order",
                    timestamp=get_utc_timestamp().isoformat()
                )
            
            # 2. 檢查價格影響
            if quote.price_impact > params.max_price_impact:
                logger.warning("dca_order_high_price_impact", 
                             price_impact=quote.price_impact,
                             max_allowed=params.max_price_impact)
                
                return SwapResult(
                    success=False,
                    error=f"Price impact too high: {quote.price_impact:.4f}%",
                    timestamp=get_utc_timestamp().isoformat()
                )
            
            # 3. 執行交換
            result = await self.execute_swap(
                params.from_token,
                params.to_token,
                str(amount_wei),
                params.slippage
            )
            
            if result.success:
                logger.info("dca_order_completed", 
                           order_index=order_index,
                           tx_hash=result.tx_hash)
            
            return result
            
        except Exception as e:
            logger.error("dca_order_failed", 
                        order_index=order_index,
                        error=str(e))
            return SwapResult(
                success=False,
                error=str(e),
                timestamp=get_utc_timestamp().isoformat()
            )

    # ========== Utility Functions ==========
    
    def set_wallet(self, address: str, private_key: str) -> None:
        """設置錢包"""
        try:
            self.wallet_address = address
            self.private_key = private_key
            logger.info("wallet_configured", address=address)
        except Exception as e:
            logger.error("wallet_configuration_failed", error=str(e))
            raise DyFlowException(f"Failed to configure wallet: {e}")
    
    async def get_token_balance(self, token_address: str, wallet_address: str) -> float:
        """獲取代幣餘額"""
        try:
            if not self.w3:
                return 0.0
            
            if token_address.lower() == '******************************************':
                # BNB 餘額
                balance_wei = self.w3.eth.get_balance(wallet_address)
                return self.w3.from_wei(balance_wei, 'ether')
            else:
                # ERC20 代幣餘額
                # 這裡需要 ERC20 ABI，簡化實現
                return 0.0
                
        except Exception as e:
            logger.error("get_token_balance_failed", 
                        token=token_address, 
                        wallet=wallet_address, 
                        error=str(e))
            return 0.0
