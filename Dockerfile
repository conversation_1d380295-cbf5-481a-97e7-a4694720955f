# 多阶段构建 Docker 镜像 - Dy-Flow AI Agent 系统
FROM python:3.11-slim as base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd --create-home --shell /bin/bash dyflow

# 设置工作目录
WORKDIR /app

# 依赖安装阶段
FROM base as deps

# 复制依赖文件
COPY requirements.txt ./

# 安装 Python 依赖
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# 应用构建阶段
FROM deps as builder

# 复制源代码
COPY . .

# 设置权限
RUN chown -R dyflow:dyflow /app

# 创建必要的目录
RUN mkdir -p /app/data /app/logs /app/temp && \
    chown -R dyflow:dyflow /app/data /app/logs /app/temp

# 生产镜像
FROM python:3.11-slim as production

# 重新安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd --create-home --shell /bin/bash dyflow

# 设置工作目录
WORKDIR /app

# 从构建阶段复制依赖
COPY --from=deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# 从构建阶段复制应用代码
COPY --from=builder --chown=dyflow:dyflow /app .

# 切换到应用用户
USER dyflow

# 设置环境变量
ENV PYTHONPATH=/app
ENV DYFLOW_ENV=production

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8080/health', timeout=5)" || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["python", "dyflow_main.py"]
