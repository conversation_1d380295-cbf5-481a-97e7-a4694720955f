# Dy-Flow 2.0 Docker Compose Configuration
# Delta-Neutral LP + Perp-Hedge System

version: '3.8'

services:
  # Main Dy-Flow Application
  dyflow:
    build: .
    container_name: dyflow_2_0_agent
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Database
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      
      # Blockchain
      - BSC_RPC_URL=${BSC_RPC_URL:-https://bsc-dataseed1.binance.org/}
      - SOLANA_RPC_URL=${SOLANA_RPC_URL:-https://api.mainnet-beta.solana.com}
      - BSC_PK=${BSC_PK}
      - SOL_KEYFILE=${SOL_KEYFILE}
      
      # Exchange APIs
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_SECRET_KEY=${BINANCE_SECRET_KEY}
      - OKX_API_KEY=${OKX_API_KEY}
      - OKX_SECRET_KEY=${OKX_SECRET_KEY}
      - OKX_PASSPHRASE=${OKX_PASSPHRASE}
      - BYBIT_API_KEY=${BYBIT_API_KEY}
      - BYBIT_SECRET_KEY=${BYBIT_SECRET_KEY}
      
      # LLM
      - OLLAMA_BASE_URL=http://ollama:11434
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      
      # Application
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - CHECK_INTERVAL=${CHECK_INTERVAL:-300}
      - DEBUG_MODE=${DEBUG_MODE:-false}
      - DRY_RUN=${DRY_RUN:-false}
      
      # Strategy Parameters
      - ENTER_THRESHOLD=${ENTER_THRESHOLD:-0.60}
      - EXIT_THRESHOLD=${EXIT_THRESHOLD:-0.30}
      - MAX_POSITIONS=${MAX_POSITIONS:-5}
      - EMERGENCY_EXIT_THRESHOLD=${EMERGENCY_EXIT_THRESHOLD:-0.10}
      
      # Monitoring
      - ENABLE_NOTIFICATIONS=${ENABLE_NOTIFICATIONS:-false}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      
      # Cache & Performance
      - REDIS_URL=redis://redis:6379/0
      - ENABLE_CACHE=true
      
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - ./tools:/app/tools
      - ${SOL_KEYFILE}:/app/solana-keypair.json:ro
    depends_on:
      - redis
      - ollama
    networks:
      - dyflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Local Ollama for LLM inference
  ollama:
    image: ollama/ollama:latest
    container_name: dyflow_ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - dyflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: dyflow_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-dyflow123}
    volumes:
      - redis_data:/data
    networks:
      - dyflow-network
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "auth", "${REDIS_PASSWORD:-dyflow123}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL (optional local database for development)
  postgres:
    image: postgres:15-alpine
    container_name: dyflow_postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=dyflow
      - POSTGRES_USER=dyflow
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-dyflow123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docs/supabase_schema.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - dyflow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dyflow"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - local-db  # Only start with: docker-compose --profile local-db up

  # Prometheus for metrics collection (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: dyflow_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - dyflow-network
    profiles:
      - monitoring

  # Grafana for metrics visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: dyflow_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    networks:
      - dyflow-network
    profiles:
      - monitoring

  # Nginx for reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: dyflow_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - dyflow
    networks:
      - dyflow-network
    profiles:
      - proxy

networks:
  dyflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  ollama_data:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# Helper commands:
# Start basic services: docker-compose up -d
# Start with local database: docker-compose --profile local-db up -d
# Start with monitoring: docker-compose --profile monitoring up -d
# Start everything: docker-compose --profile local-db --profile monitoring --profile proxy up -d