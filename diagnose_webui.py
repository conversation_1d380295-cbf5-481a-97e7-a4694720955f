#!/usr/bin/env python3
"""
WebUI 诊断脚本 - 检查依赖和环境
"""

import sys
import subprocess

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查:")
    print(f"   版本: {sys.version}")
    print(f"   路径: {sys.executable}")
    print()

def check_dependencies():
    """检查依赖包"""
    print("📦 依赖包检查:")
    
    required_packages = [
        "streamlit",
        "pandas", 
        "plotly",
        "numpy"
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}: 已安装")
        except ImportError:
            print(f"   ❌ {package}: 未安装")
    print()

def check_streamlit():
    """检查Streamlit是否能正常工作"""
    print("🌐 Streamlit检查:")
    try:
        import streamlit as st
        print(f"   ✅ Streamlit版本: {st.__version__}")
        
        # 检查是否能创建基本组件
        import pandas as pd
        df = pd.DataFrame({"A": [1, 2], "B": [3, 4]})
        print("   ✅ Pandas DataFrame创建成功")
        
    except Exception as e:
        print(f"   ❌ Streamlit错误: {e}")
    print()

def test_data_generation():
    """测试数据生成"""
    print("📊 数据生成测试:")
    try:
        import random
        import pandas as pd
        
        # 测试池子数据生成
        pools = []
        for i in range(5):
            pools.append({
                "交易对": "BNB/USDC",
                "APR": f"{random.uniform(20, 80):.1f}%",
                "TVL": f"${random.uniform(1, 5):.1f}M"
            })
        
        df = pd.DataFrame(pools)
        print("   ✅ 池子数据生成成功")
        print(f"   📋 生成了 {len(df)} 条记录")
        print("   📄 示例数据:")
        print(df.head(3).to_string(index=False))
        
    except Exception as e:
        print(f"   ❌ 数据生成错误: {e}")
    print()

def check_port():
    """检查端口占用"""
    print("🔌 端口检查:")
    try:
        import socket
        
        ports = [8501, 8502]
        for port in ports:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', port))
            if result == 0:
                print(f"   🟡 端口 {port}: 已被占用")
            else:
                print(f"   ✅ 端口 {port}: 可用")
            sock.close()
            
    except Exception as e:
        print(f"   ❌ 端口检查错误: {e}")
    print()

def main():
    """主函数"""
    print("🔍 DyFlow WebUI 诊断工具")
    print("=" * 40)
    print()
    
    check_python_version()
    check_dependencies()
    check_streamlit()
    test_data_generation()
    check_port()
    
    print("💡 建议:")
    print("   1. 如果依赖包缺失，运行: pip3 install streamlit pandas plotly numpy --user")
    print("   2. 如果端口被占用，尝试使用不同端口")
    print("   3. 测试版本WebUI: http://localhost:8502")
    print("   4. 如果数据生成成功，WebUI应该能正常显示")
    print()
    print("🚀 启动测试WebUI:")
    print("   python3 -m streamlit run test_webui.py --server.port 8502")

if __name__ == "__main__":
    main()
