#!/usr/bin/env python3
"""
DyFlow实时监控Dashboard
显示系统连接状态、持仓情况、实时数据，并提供AI对话界面
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import json

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.prompt import Prompt
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import box
from rich.align import Align
from rich.columns import Columns

# 导入执行Agent
from src.agents.execution_agent import ExecutionAgent

console = Console()

class SystemStatus:
    """系统状态管理"""
    
    def __init__(self):
        self.connections = {
            "bsc_rpc": {"status": "connected", "latency": 45, "last_block": 41234567},
            "solana_rpc": {"status": "connected", "latency": 32, "last_slot": 287654321},
            "pancakeswap_subgraph": {"status": "connected", "latency": 120},
            "meteora_api": {"status": "connected", "latency": 89},
            "ollama_ai": {"status": "connected", "model": "qwen2.5:3b"}
        }
        
        self.wallets = {
            "bsc_wallet_1": {
                "address": "******************************************",
                "balance_bnb": 2.45,
                "balance_usdc": 1250.30,
                "balance_usdt": 890.75,
                "status": "active"
            },
            "sol_wallet_1": {
                "address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
                "balance_sol": 15.67,
                "balance_usdc": 2100.50,
                "status": "active"
            }
        }
        
        self.positions = {
            "bsc_bnb_usdc_001": {
                "chain": "BSC",
                "pair": "BNB/USDC",
                "pool_address": "******************************************",
                "liquidity_value": 8500.00,
                "token0_amount": 12.5,
                "token1_amount": 8500.0,
                "entry_time": "2024-01-15 10:30:00",
                "current_pnl": 320.50,
                "fees_earned": 45.20,
                "status": "active"
            },
            "bsc_bnb_usdt_001": {
                "chain": "BSC", 
                "pair": "BNB/USDT",
                "pool_address": "******************************************",
                "liquidity_value": 6200.00,
                "token0_amount": 9.2,
                "token1_amount": 6200.0,
                "entry_time": "2024-01-16 14:20:00",
                "current_pnl": -85.30,
                "fees_earned": 28.90,
                "status": "active"
            },
            "sol_sol_usdc_001": {
                "chain": "Solana",
                "pair": "SOL/USDC", 
                "pool_address": "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",
                "liquidity_value": 12000.00,
                "token0_amount": 85.5,
                "token1_amount": 12000.0,
                "entry_time": "2024-01-17 09:15:00",
                "current_pnl": 480.75,
                "fees_earned": 67.40,
                "status": "active"
            }
        }
        
        self.system_metrics = {
            "total_portfolio_value": 26700.00,
            "total_pnl": 715.95,
            "total_fees": 141.50,
            "active_positions": 3,
            "uptime": "2d 14h 32m",
            "last_rebalance": "2024-01-18 08:00:00"
        }

class DyFlowDashboard:
    """DyFlow实时监控Dashboard"""
    
    def __init__(self):
        self.console = console
        self.status = SystemStatus()
        self.ai_chat_history = []
        self.execution_agent = ExecutionAgent()
        
    def create_connection_status_panel(self):
        """创建连接状态面板"""
        table = Table(title="🔗 系统连接状态", box=box.ROUNDED, show_header=True)
        table.add_column("服务", style="cyan")
        table.add_column("状态", style="green", justify="center")
        table.add_column("延迟", style="yellow", justify="right")
        table.add_column("详情", style="blue")
        
        for service, info in self.status.connections.items():
            status_color = "green" if info["status"] == "connected" else "red"
            status_text = "🟢 已连接" if info["status"] == "connected" else "🔴 断开"
            
            latency = f"{info.get('latency', 0)}ms" if 'latency' in info else "-"
            
            details = ""
            if service == "bsc_rpc":
                details = f"区块: {info.get('last_block', 0)}"
            elif service == "solana_rpc":
                details = f"Slot: {info.get('last_slot', 0)}"
            elif service == "ollama_ai":
                details = f"模型: {info.get('model', 'N/A')}"
            
            table.add_row(
                service.replace("_", " ").title(),
                Text(status_text, style=status_color),
                latency,
                details
            )
        
        return table
    
    def create_wallet_status_panel(self):
        """创建钱包状态面板"""
        table = Table(title="💼 钱包状态", box=box.ROUNDED)
        table.add_column("钱包", style="cyan")
        table.add_column("地址", style="yellow", max_width=20)
        table.add_column("主币余额", style="green", justify="right")
        table.add_column("USDC余额", style="blue", justify="right")
        table.add_column("其他", style="magenta", justify="right")
        table.add_column("状态", style="green", justify="center")
        
        for wallet_id, wallet_info in self.status.wallets.items():
            address = wallet_info["address"]
            short_address = f"{address[:6]}...{address[-4:]}"
            
            if "bsc" in wallet_id:
                main_balance = f"{wallet_info['balance_bnb']:.2f} BNB"
                usdc_balance = f"{wallet_info['balance_usdc']:.2f} USDC"
                other_balance = f"{wallet_info['balance_usdt']:.2f} USDT"
            else:
                main_balance = f"{wallet_info['balance_sol']:.2f} SOL"
                usdc_balance = f"{wallet_info['balance_usdc']:.2f} USDC"
                other_balance = "-"
            
            status_text = "🟢 活跃" if wallet_info["status"] == "active" else "🔴 停用"
            
            table.add_row(
                wallet_id.replace("_", " ").title(),
                short_address,
                main_balance,
                usdc_balance,
                other_balance,
                status_text
            )
        
        return table
    
    def create_positions_panel(self):
        """创建持仓面板"""
        table = Table(title="📊 当前持仓", box=box.ROUNDED)
        table.add_column("链", style="cyan")
        table.add_column("交易对", style="yellow")
        table.add_column("池子地址", style="dim", max_width=15)
        table.add_column("流动性价值", style="green", justify="right")
        table.add_column("盈亏", style="blue", justify="right")
        table.add_column("手续费", style="magenta", justify="right")
        table.add_column("状态", style="green", justify="center")
        
        for pos_id, pos_info in self.status.positions.items():
            pnl = pos_info["current_pnl"]
            pnl_color = "green" if pnl >= 0 else "red"
            pnl_text = f"+${pnl:.2f}" if pnl >= 0 else f"-${abs(pnl):.2f}"
            
            pool_short = f"{pos_info['pool_address'][:6]}...{pos_info['pool_address'][-4:]}"
            
            table.add_row(
                pos_info["chain"],
                pos_info["pair"],
                pool_short,
                f"${pos_info['liquidity_value']:,.2f}",
                Text(pnl_text, style=pnl_color),
                f"${pos_info['fees_earned']:.2f}",
                "🟢 活跃"
            )
        
        return table
    
    def create_metrics_panel(self):
        """创建系统指标面板"""
        metrics = self.status.system_metrics
        
        metrics_table = Table(title="📈 系统指标", box=box.ROUNDED)
        metrics_table.add_column("指标", style="cyan")
        metrics_table.add_column("数值", style="green", justify="right")
        
        total_pnl = metrics["total_pnl"]
        pnl_color = "green" if total_pnl >= 0 else "red"
        pnl_text = f"+${total_pnl:.2f}" if total_pnl >= 0 else f"-${abs(total_pnl):.2f}"
        
        metrics_table.add_row("投资组合总值", f"${metrics['total_portfolio_value']:,.2f}")
        metrics_table.add_row("总盈亏", Text(pnl_text, style=pnl_color))
        metrics_table.add_row("累计手续费", f"${metrics['total_fees']:.2f}")
        metrics_table.add_row("活跃持仓", str(metrics['active_positions']))
        metrics_table.add_row("系统运行时间", metrics['uptime'])
        metrics_table.add_row("上次重平衡", metrics['last_rebalance'])
        
        return metrics_table
    
    def create_ai_chat_panel(self):
        """创建AI对话面板"""
        chat_content = []
        
        # 显示最近的对话历史
        for msg in self.ai_chat_history[-5:]:  # 只显示最近5条
            timestamp = msg.get('timestamp', '')
            role = msg.get('role', '')
            content = msg.get('content', '')
            
            if role == 'user':
                chat_content.append(f"[bold blue]👤 你 ({timestamp}):[/bold blue]")
                chat_content.append(f"[blue]{content}[/blue]")
            else:
                chat_content.append(f"[bold green]🤖 AI ({timestamp}):[/bold green]")
                chat_content.append(f"[green]{content}[/green]")
            chat_content.append("")
        
        if not chat_content:
            chat_content = ["[dim]暂无对话历史...[/dim]", "", "[yellow]💡 试试说: '出清所有持仓' 或 '查看BNB/USDC池子状态'[/yellow]"]
        
        chat_text = "\n".join(chat_content)
        
        return Panel(
            chat_text,
            title="🤖 AI助手对话",
            box=box.ROUNDED,
            style="blue"
        )
    
    def create_dashboard_layout(self):
        """创建Dashboard布局"""
        layout = Layout()
        
        # 主要布局分割
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=8)
        )
        
        # 主要区域分割
        layout["main"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        # 左侧分割
        layout["left"].split_column(
            Layout(name="connections", ratio=1),
            Layout(name="wallets", ratio=1),
            Layout(name="metrics", ratio=1)
        )
        
        # 右侧分割
        layout["right"].split_column(
            Layout(name="positions", ratio=2),
            Layout(name="ai_chat", ratio=1)
        )
        
        # 填充内容
        layout["header"].update(
            Panel(
                Align.center(Text("🚀 DyFlow 实时监控Dashboard", style="bold blue")),
                box=box.DOUBLE,
                style="blue",
                subtitle=f"更新时间: {datetime.now().strftime('%H:%M:%S')}"
            )
        )
        
        layout["connections"].update(self.create_connection_status_panel())
        layout["wallets"].update(self.create_wallet_status_panel())
        layout["metrics"].update(self.create_metrics_panel())
        layout["positions"].update(self.create_positions_panel())
        layout["ai_chat"].update(self.create_ai_chat_panel())
        
        layout["footer"].update(
            Panel(
                "[bold yellow]💬 AI对话模式[/bold yellow]\n"
                "输入指令与AI助手对话，例如:\n"
                "• '出清所有持仓' - 关闭所有LP并换回BNB/SOL\n"
                "• '查看BNB/USDC状态' - 获取特定池子信息\n"
                "• '增加SOL/USDC持仓' - 开新仓位\n"
                "• '设置止损15%' - 调整风险参数\n"
                "输入 'quit' 退出系统",
                title="💡 使用说明",
                style="yellow"
            )
        )
        
        return layout
    
    async def process_ai_command(self, user_input: str) -> str:
        """处理AI指令"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        # 添加用户消息到历史
        self.ai_chat_history.append({
            'role': 'user',
            'content': user_input,
            'timestamp': timestamp
        })
        
        # 模拟AI处理
        await asyncio.sleep(1)  # 模拟思考时间
        
        # 使用执行Agent处理指令
        execution_result = await self.execution_agent.execute_user_command(user_input)

        if execution_result["success"]:
            tasks = execution_result.get("tasks", [])
            response = f"✅ 指令执行成功！\n"
            response += f"📋 创建了{execution_result['tasks_created']}个任务\n\n"

            for task in tasks:
                status_emoji = "✅" if task["status"] == "completed" else "⏳" if task["status"] == "executing" else "❌"
                response += f"{status_emoji} {task['task_type']} - {task['status']}\n"

                if task.get("result"):
                    result = task["result"]
                    if "exit_amount" in result:
                        response += f"   💰 退出金额: ${result['exit_amount']:,.2f}\n"
                        response += f"   🪙 获得: {result['exit_token_amount']:.2f} {result['exit_token']}\n"
                    elif "position_value" in result:
                        response += f"   💰 开仓金额: ${result['position_value']:,.2f}\n"
                    response += f"   ⛽ Gas费用: {result.get('gas_used', 0):.6f}\n"

        else:
            response = f"❌ 执行失败: {execution_result['message']}"

        # 保留原有的简单回复作为备用
        if not execution_result["success"]:
            user_input_lower = user_input.lower()
            if '状态' in user_input_lower or '概况' in user_input_lower:
                response = f"📈 系统状态概况:\n" \
                          f"• 总资产: ${self.status.system_metrics['total_portfolio_value']:,.2f}\n" \
                          f"• 总盈亏: ${self.status.system_metrics['total_pnl']:,.2f}\n" \
                          f"• 活跃持仓: {self.status.system_metrics['active_positions']}个\n" \
                          f"• 系统运行: {self.status.system_metrics['uptime']}\n" \
                          f"• 所有连接正常 ✅"
            elif 'bnb/usdc' in user_input_lower:
                response = "📊 BNB/USDC池子状态:\n" \
                          "• 当前持仓: $8,500\n" \
                          "• 盈亏: +$320.50 (+3.77%)\n" \
                          "• 手续费: $45.20\n" \
                          "• 建议: 继续持有，表现良好"
            else:
                response = "🤖 我理解了你的请求。作为DyFlow AI助手，我可以帮你:\n" \
                          "• 管理LP持仓 (开仓/平仓)\n" \
                          "• 监控风险指标\n" \
                          "• 执行交易策略\n" \
                          "• 分析市场数据\n" \
                          "请告诉我具体需要什么帮助?"
        
        # 添加AI回复到历史
        self.ai_chat_history.append({
            'role': 'assistant',
            'content': response,
            'timestamp': timestamp
        })
        
        return response
    
    async def run_dashboard(self):
        """运行实时Dashboard"""
        with Live(
            self.create_dashboard_layout(),
            refresh_per_second=1,
            console=self.console
        ) as live:
            
            while True:
                try:
                    # 更新Dashboard
                    live.update(self.create_dashboard_layout())
                    
                    # 检查用户输入 (非阻塞)
                    await asyncio.sleep(0.1)
                    
                except KeyboardInterrupt:
                    break
    
    async def run_interactive_mode(self):
        """运行交互模式"""
        self.console.print("[bold green]🚀 DyFlow Dashboard 启动成功![/bold green]")
        self.console.print("[yellow]💡 输入指令与AI助手对话，输入 'dashboard' 查看实时面板[/yellow]")
        self.console.print()
        
        while True:
            try:
                # 显示当前状态摘要
                metrics = self.status.system_metrics
                self.console.print(f"[dim]总资产: ${metrics['total_portfolio_value']:,.2f} | "
                                 f"盈亏: ${metrics['total_pnl']:+.2f} | "
                                 f"持仓: {metrics['active_positions']}个[/dim]")
                
                user_input = Prompt.ask("[bold cyan]💬 DyFlow AI")
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    self.console.print("[bold green]👋 感谢使用DyFlow，再见![/bold green]")
                    break
                
                elif user_input.lower() == 'dashboard':
                    self.console.print("[yellow]🔄 启动实时Dashboard... (按Ctrl+C返回对话模式)[/yellow]")
                    await self.run_dashboard()
                    continue
                
                # 处理AI指令
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=self.console
                ) as progress:
                    task = progress.add_task("[cyan]AI正在思考...", total=None)
                    response = await self.process_ai_command(user_input)
                    progress.update(task, description="[green]✅ AI回复完成")
                
                # 显示AI回复
                self.console.print(Panel(
                    response,
                    title="🤖 AI助手回复",
                    style="green"
                ))
                self.console.print()
                
            except KeyboardInterrupt:
                self.console.print("\n[bold green]👋 再见![/bold green]")
                break
            except Exception as e:
                self.console.print(f"[red]❌ 错误: {e}[/red]")

async def main():
    """主函数"""
    dashboard = DyFlowDashboard()
    await dashboard.run_interactive_mode()

if __name__ == "__main__":
    asyncio.run(main())
