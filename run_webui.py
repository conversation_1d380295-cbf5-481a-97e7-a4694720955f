#!/usr/bin/env python3
"""
DyFlow WebUI 启动器
简化的启动脚本，自动处理依赖和启动
"""

import subprocess
import sys
import os

def install_dependencies():
    """安装必要的依赖"""
    print("📦 检查并安装依赖...")
    
    required_packages = [
        "streamlit>=1.28.0",
        "pandas>=1.5.0", 
        "plotly>=5.15.0",
        "numpy>=1.24.0"
    ]
    
    for package in required_packages:
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                package, "--user", "--quiet"
            ])
        except subprocess.CalledProcessError:
            print(f"❌ 安装 {package} 失败")
            return False
    
    print("✅ 依赖安装完成")
    return True

def start_webui():
    """启动WebUI"""
    print("🚀 启动DyFlow WebUI Dashboard...")
    print("🌐 WebUI将在 http://localhost:8501 启动")
    print("📱 浏览器将自动打开")
    print("")
    print("✨ 功能特性:")
    print("   💼 实时钱包和LP持仓监控")
    print("   🟡 BSC池子扫描 (25个池子)")
    print("   🟣 Solana池子扫描 (25个池子)")
    print("   📋 交易日志和Agent日志")
    print("   🤖 AI指令交互")
    print("   🔄 自动刷新功能")
    print("")
    print("🔄 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "dyflow_webui.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0"
        ])
    except KeyboardInterrupt:
        print("\n👋 WebUI已停止")
    except FileNotFoundError:
        print("❌ 找不到 dyflow_webui.py 文件")
        print("请确保在正确的目录中运行此脚本")

def main():
    """主函数"""
    print("🚀 DyFlow WebUI 启动器")
    print("=" * 30)
    
    # 检查文件是否存在
    if not os.path.exists("dyflow_webui.py"):
        print("❌ 找不到 dyflow_webui.py 文件")
        print("请确保在包含WebUI文件的目录中运行")
        return
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，请检查网络连接")
        return
    
    # 启动WebUI
    start_webui()

if __name__ == "__main__":
    main()
